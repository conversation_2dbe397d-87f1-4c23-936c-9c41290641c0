# 🌐 Domain Setup Guide for Namecheap

This guide walks you through setting up `dev.datadrivenjobsearch.com` to point to your GCP deployment.

## 📋 Prerequisites

- Domain `datadrivenjobsearch.com` purchased through Namecheap
- GCP deployment completed (Cloud Run service deployed)
- Domain mapping created in Google Cloud

## 🔧 Step-by-Step Setup

### Step 1: Access Namecheap DNS Settings

1. **Log in to Namecheap**
   - Go to [namecheap.com](https://www.namecheap.com)
   - Sign in to your account

2. **Navigate to Domain Management**
   - Click on "Domain List" in the left sidebar
   - Find `datadrivenjobsearch.com` and click "Manage"

3. **Access Advanced DNS**
   - Click on the "Advanced DNS" tab
   - You'll see the current DNS records

### Step 2: Add CNAME Record

1. **Click "Add New Record"**
   - Type: Select "CNAME Record"
   - Host: Enter `dev`
   - Value: Enter the target provided by the deployment script
   - TTL: Select "Automatic" or "300"

2. **Example CNAME Record:**
   ```
   Type: CNAME Record
   Host: dev
   Value: ghs.googlehosted.com
   TTL: Automatic
   ```

3. **Save the Record**
   - Click the green checkmark to save
   - The record will appear in your DNS records list

### Step 3: Verify Domain Ownership (If Required)

If the deployment script indicated domain ownership verification is needed:

1. **Go to Google Search Console**
   - Visit [search.google.com/search-console](https://search.google.com/search-console)
   - Click "Add Property"

2. **Add Your Domain**
   - Select "Domain" (not URL prefix)
   - Enter: `datadrivenjobsearch.com`
   - Click "Continue"

3. **Verify via DNS**
   - Google will provide a TXT record
   - Add this TXT record to your Namecheap DNS:
     ```
     Type: TXT Record
     Host: @
     Value: [Google provided value]
     TTL: Automatic
     ```

4. **Complete Verification**
   - Click "Verify" in Google Search Console
   - Wait for confirmation

### Step 4: Wait for DNS Propagation

1. **Propagation Time**
   - DNS changes can take 5 minutes to 48 hours
   - Typically propagates within 1-2 hours

2. **Check Propagation Status**
   - Use [dnschecker.org](https://dnschecker.org/)
   - Enter: `dev.datadrivenjobsearch.com`
   - Look for green checkmarks worldwide

3. **Test Your Domain**
   - Try accessing: `https://dev.datadrivenjobsearch.com`
   - Initially may show SSL certificate warnings (normal)

## 🔒 SSL Certificate

### Automatic SSL

- Google Cloud Run automatically provisions SSL certificates
- Can take up to 24 hours to be fully active
- No action required on your part

### Checking SSL Status

1. **Test SSL Certificate**
   - Visit: [ssllabs.com/ssltest](https://www.ssllabs.com/ssltest/)
   - Enter: `dev.datadrivenjobsearch.com`
   - Wait for analysis

2. **Browser Test**
   - Visit: `https://dev.datadrivenjobsearch.com`
   - Look for the lock icon in the address bar

## 🧪 Testing Your Setup

### DNS Resolution Test

```bash
# Test DNS resolution
nslookup dev.datadrivenjobsearch.com

# Test with dig (if available)
dig dev.datadrivenjobsearch.com
```

### HTTP/HTTPS Test

```bash
# Test HTTP redirect
curl -I http://dev.datadrivenjobsearch.com

# Test HTTPS
curl -I https://dev.datadrivenjobsearch.com
```

### Application Test

1. **Visit the Application**
   - Go to: `https://dev.datadrivenjobsearch.com`
   - Should load the login page

2. **Test Authentication**
   - Sign in with Clerk
   - Verify dashboard loads

3. **Test Core Functionality**
   - Try email analysis
   - Check real-time updates
   - Verify API endpoints work

## 🚨 Troubleshooting

### Common Issues

1. **"This site can't be reached"**
   - DNS hasn't propagated yet
   - Wait longer or check DNS propagation
   - Verify CNAME record is correct

2. **SSL Certificate Warning**
   - Certificate is still being provisioned
   - Wait up to 24 hours
   - Try accessing via HTTP first

3. **404 Not Found**
   - Domain mapping may not be complete
   - Check Cloud Run domain mapping status
   - Verify deployment was successful

4. **502 Bad Gateway**
   - Application may not be running
   - Check Cloud Run logs
   - Verify environment variables

### Getting Help

1. **Check Cloud Run Status**
   ```bash
   gcloud run services describe webapp --region=us-central1 --project=ddjs-dev-458016
   ```

2. **View Application Logs**
   ```bash
   gcloud logging tail 'resource.type=cloud_run_revision' --project=ddjs-dev-458016
   ```

3. **Check Domain Mapping**
   ```bash
   gcloud run domain-mappings describe dev.datadrivenjobsearch.com --region=us-central1 --project=ddjs-dev-458016
   ```

## 📞 Support Resources

- **Namecheap Support**: [support.namecheap.com](https://support.namecheap.com)
- **Google Cloud Support**: [cloud.google.com/support](https://cloud.google.com/support)
- **DNS Checker**: [dnschecker.org](https://dnschecker.org)
- **SSL Test**: [ssllabs.com/ssltest](https://www.ssllabs.com/ssltest/)

## ✅ Success Checklist

- [ ] CNAME record added to Namecheap
- [ ] DNS propagation completed (green on dnschecker.org)
- [ ] Domain ownership verified (if required)
- [ ] SSL certificate active (lock icon in browser)
- [ ] Application loads at https://dev.datadrivenjobsearch.com
- [ ] Authentication works
- [ ] Core functionality tested

Once all items are checked, your domain setup is complete! 🎉
