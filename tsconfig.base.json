{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@webapp/shared": ["libs/shared/src/index.ts"], "@webapp/email-core": ["libs/email-core/src/index.ts"], "@webapp/gcp-utils": ["libs/gcp-utils/src/index.ts"], "@webapp/services": ["libs/services/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}