# Production environment variables template
# Copy this file to .env.production and fill in your actual values
# NEVER commit .env.production to git!

NODE_ENV=production
GOOGLE_CLOUD_PROJECT=ddjs-dev-458016

# Base URL for production
BASE_URL=https://dev.datadrivenjobsearch.com

# Clerk Authentication - CREATE NEW PRODUCTION KEYS!
# Get these from https://dashboard.clerk.com/
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_REPLACE_WITH_YOUR_ACTUAL_PRODUCTION_KEY
CLERK_SECRET_KEY=sk_live_REPLACE_WITH_YOUR_ACTUAL_PRODUCTION_SECRET
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# OpenAI API Key - GET YOUR OWN KEY!
# Get from https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-REPLACE_WITH_YOUR_ACTUAL_OPENAI_KEY

# Webhook URL for Cloud Functions
WEBAPP_PROGRESS_WEBHOOK_URL=https://dev.datadrivenjobsearch.com/api/emails/progress-webhook

# Gmail API Configuration
GMAIL_PUBSUB_TOPIC=projects/ddjs-dev-458016/topics/gmail-notifications
GMAIL_PUBSUB_SUBSCRIPTION=gmail-notifications-sub

# Cloud Functions Configuration
FUNCTIONS_REGION=us-central1
