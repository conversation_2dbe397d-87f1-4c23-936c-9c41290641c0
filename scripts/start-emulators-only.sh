#!/bin/bash

# Firebase Emulators Only Script
set -e

echo "🔥 Starting Firebase Emulators Only"
echo "==================================="

# Load nvm and use Node.js 22
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 22 || nvm use default

# Set up Java path for Firebase emulators
export PATH="/opt/homebrew/opt/openjdk@11/bin:$PATH"

echo "📋 Using Node.js $(node --version) and npm $(npm --version)"
echo "☕ Using Java $(java -version 2>&1 | head -1)"

# Build projects first
echo "🔨 Building projects..."
npx nx run-many --target=build --projects=shared,email-core,email-analysis-function,notification-handler-function

# Prepare Cloud Functions
echo "📦 Preparing Cloud Functions..."
cp package.json dist/apps/email-analysis-function/ 2>/dev/null || true
cp package.json dist/apps/notification-handler-function/ 2>/dev/null || true

# Set environment variables
export FIRESTORE_EMULATOR_HOST="localhost:8080"
export PUBSUB_EMULATOR_HOST="localhost:8085"
export FUNCTIONS_EMULATOR_HOST="localhost:5001"
export WEBAPP_PROGRESS_WEBHOOK_URL="http://localhost:3000/api/emails/progress-webhook"

echo ""
echo "🔥 Starting Firebase emulators..."
echo ""
echo "📊 Emulator UI: http://localhost:4000"
echo "☁️ Functions: http://localhost:5001"
echo "📄 Firestore: http://localhost:8080"
echo "📨 Pub/Sub: http://localhost:8085"
echo ""
echo "Press Ctrl+C to stop emulators"
echo ""

# Start emulators
firebase emulators:start --only functions,firestore,pubsub,ui
