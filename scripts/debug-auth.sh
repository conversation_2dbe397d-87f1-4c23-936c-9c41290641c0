#!/bin/bash

# 🔍 Debug Authentication Script
# This script helps diagnose authentication issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Authentication Debug Script${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check current gcloud configuration
print_status "📋 Current gcloud configuration:"
echo "Account: $(gcloud config get-value account 2>/dev/null || echo 'Not set')"
echo "Project: $(gcloud config get-value project 2>/dev/null || echo 'Not set')"
echo ""

# Skip Firebase check since we're using pure GCP
print_status "🔥 Firebase: Skipped (using pure GCP Cloud Functions)"
echo ""

# Check Google Cloud login status
print_status "☁️ Google Cloud login status:"
if gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 >/dev/null 2>&1; then
    print_success "Google Cloud: Logged in"
    echo "Active account: $(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1)"
else
    print_error "Google Cloud: Not logged in or error"
    echo "Run: gcloud auth login"
fi
echo ""

# Test project access
print_status "🎯 Testing project access:"

echo "Testing ddjs-dev-458016..."
if gcloud projects describe ddjs-dev-458016 >/dev/null 2>&1; then
    print_success "Can access ddjs-dev-458016"
else
    print_error "Cannot access ddjs-dev-458016"
    echo "You may need to be granted access to this project"
fi

echo "Testing data-driven-job-search..."
if gcloud projects describe data-driven-job-search >/dev/null 2>&1; then
    print_success "Can access data-driven-job-search"
else
    print_error "Cannot access data-driven-job-search"
    echo "You may need to be granted access to this project"
fi
echo ""

# Test Secret Manager access
print_status "🔐 Testing Secret Manager access:"
if gcloud secrets list --project=data-driven-job-search --limit=1 >/dev/null 2>&1; then
    print_success "Can access Secret Manager in data-driven-job-search"
    echo "Available secrets:"
    gcloud secrets list --project=data-driven-job-search --format="value(name)" | head -5
else
    print_error "Cannot access Secret Manager in data-driven-job-search"
    echo "You may need the secretmanager.secretAccessor role"
fi
echo ""

# Skip Firebase project check since we're using pure GCP
print_status "🔥 Firebase project access: Skipped (using pure GCP)"
echo ""

print_status "🎯 Recommendations:"
echo "1. Make sure you're logged in to Google Cloud"
echo "2. Ensure you have access to both GCP projects"
echo "3. Check that you have the necessary IAM roles"
echo ""
echo "If you see errors above, fix them before running the deployment script."
