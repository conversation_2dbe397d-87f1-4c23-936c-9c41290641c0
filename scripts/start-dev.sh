#!/bin/bash

# Quick Development Startup Script
set -e

echo "🚀 Starting Development Environment"
echo "=================================="

# Load nvm and use Node.js 22
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 22 || nvm use default

# Set up Java path for Firebase emulators
export PATH="/opt/homebrew/opt/openjdk@11/bin:$PATH"

echo "📋 Using Node.js $(node --version) and npm $(npm --version)"
echo "☕ Using Java $(java -version 2>&1 | head -1)"

# Check if Firebase CLI is available
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Installing..."
    npm install -g firebase-tools
fi

# Ensure environment file is in webapp directory
echo "📋 Setting up environment..."
cp .env.local apps/webapp/.env.local 2>/dev/null || true

# Build projects
echo "🔨 Building projects..."
npx nx run-many --target=build --projects=shared,email-core,email-analysis-function,notification-handler-function

# Start emulators in background
echo "🔥 Starting Firebase emulators..."
./scripts/test-locally.sh &
EMULATOR_PID=$!

# Wait for emulators to start
echo "⏳ Waiting for emulators to start..."
sleep 10

# Start webapp
echo "🌐 Starting webapp..."
echo ""
echo "🎉 Development environment ready!"
echo ""
echo "📱 Webapp: http://localhost:3000"
echo "🔥 Firebase UI: http://localhost:4000"
echo ""
echo "Press Ctrl+C to stop all services"
echo ""

# Start webapp (this will block)
echo "Starting Next.js development server..."
npm run dev

# Cleanup when script exits
trap "kill $EMULATOR_PID 2>/dev/null" EXIT
