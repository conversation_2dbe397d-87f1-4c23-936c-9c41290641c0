const { Firestore } = require('@google-cloud/firestore');

async function initializeEmulator() {
    // Use development project ID for emulator
    const firestore = new Firestore({
        projectId: 'ddjs-dev-458016',
        host: 'localhost',
        port: 8081,
        ssl: false
    });

    // Create initial collections and documents if needed
    try {
        await firestore.collection('users').doc('test').set({
            userId: 'test',
            email: '<EMAIL>',
            createdAt: new Date().toISOString()
        });
        console.log('Firestore emulator initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Firestore emulator:', error);
        process.exit(1);
    }
}

initializeEmulator();