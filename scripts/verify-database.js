/**
 * <PERSON><PERSON><PERSON> to verify database connection
 * This is used during application startup to ensure the database is working
 */

const Database = require('../src/utils/firestore');

async function verifyDatabaseConnection() {
  console.log('Verifying database connection...');
  
  try {
    const database = new Database();
    
    // Verify connection
    const connected = await database.verifyConnection();
    
    if (connected) {
      console.log('Database connection verified successfully');
      process.exit(0);
    } else {
      console.warn('Database connection verification failed');
      
      // In production, this is a fatal error
      if (process.env.NODE_ENV === 'production') {
        console.error('FATAL: Cannot continue in production without database connection');
        process.exit(1);
      } else {
        console.warn('Continuing in development mode with potential database issues');
        process.exit(0);
      }
    }
  } catch (error) {
    console.error('Error verifying database connection:', error);
    
    // In production, this is a fatal error
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    } else {
      console.warn('Continuing in development mode despite database connection error');
      process.exit(0);
    }
  }
}

// Run the function
verifyDatabaseConnection();
