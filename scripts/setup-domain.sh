#!/bin/bash

# 🌐 Domain Setup Script for dev.datadrivenjobsearch.com
# This script helps set up the custom domain mapping for Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="ddjs-dev-458016"
DOMAIN="dev.datadrivenjobsearch.com"
REGION="us-central1"
SERVICE_NAME="webapp"

echo -e "${BLUE}🌐 Setting up custom domain for Data Driven Job Search${NC}"
echo -e "${BLUE}Domain: ${DOMAIN}${NC}"
echo -e "${BLUE}Project: ${PROJECT_ID}${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GRE<PERSON>}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print info
print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if gcloud is installed and user is logged in
if ! command -v gcloud &> /dev/null; then
    print_error "Google Cloud CLI is not installed"
    exit 1
fi

if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
    print_error "Not logged in to Google Cloud. Please run: gcloud auth login"
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

# Get the Cloud Run service URL
print_status "🔍 Getting Cloud Run service information..."
CLOUD_RUN_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)" --project=$PROJECT_ID)

if [ -z "$CLOUD_RUN_URL" ]; then
    print_error "Cloud Run service not found. Please deploy first using ./scripts/deploy-to-gcp.sh"
    exit 1
fi

print_success "Found Cloud Run service: $CLOUD_RUN_URL"

# Create domain mapping
print_status "🔗 Creating domain mapping..."

# First, we need to verify domain ownership
print_info "Creating domain mapping for $DOMAIN..."

gcloud run domain-mappings create \
    --service=$SERVICE_NAME \
    --domain=$DOMAIN \
    --region=$REGION \
    --project=$PROJECT_ID

if [ $? -eq 0 ]; then
    print_success "Domain mapping created successfully"
else
    print_error "Domain mapping creation failed. This might be because:"
    echo "1. Domain ownership is not verified"
    echo "2. Domain mapping already exists"
    echo "3. DNS records are not properly configured"
fi

# Get the domain mapping status
print_status "📊 Checking domain mapping status..."
MAPPING_STATUS=$(gcloud run domain-mappings describe $DOMAIN --region=$REGION --format="value(status.conditions[0].type)" --project=$PROJECT_ID 2>/dev/null || echo "NOT_FOUND")

if [ "$MAPPING_STATUS" != "NOT_FOUND" ]; then
    print_success "Domain mapping exists"
    
    # Get the required DNS records
    print_status "📋 Getting required DNS records..."
    
    # Get the CNAME record target
    CNAME_TARGET=$(gcloud run domain-mappings describe $DOMAIN --region=$REGION --format="value(status.resourceRecords[0].rrdata)" --project=$PROJECT_ID 2>/dev/null || echo "")
    
    if [ -n "$CNAME_TARGET" ]; then
        print_success "DNS configuration required:"
        echo ""
        echo -e "${BLUE}📝 DNS Records to add in Namecheap:${NC}"
        echo "Type: CNAME"
        echo "Host: dev"
        echo "Value: $CNAME_TARGET"
        echo "TTL: 300 (or automatic)"
        echo ""
    fi
else
    print_error "Domain mapping not found"
fi

# Verify domain ownership instructions
echo ""
echo -e "${YELLOW}🔐 Domain Ownership Verification:${NC}"
echo ""
echo "If domain mapping failed, you need to verify domain ownership first:"
echo ""
echo "1. Go to Google Search Console: https://search.google.com/search-console"
echo "2. Add property for: datadrivenjobsearch.com"
echo "3. Verify ownership using one of these methods:"
echo "   - HTML file upload"
echo "   - HTML tag"
echo "   - DNS record"
echo "   - Google Analytics"
echo "   - Google Tag Manager"
echo ""
echo "4. After verification, run this script again"
echo ""

# SSL Certificate information
echo -e "${BLUE}🔒 SSL Certificate:${NC}"
echo "Google Cloud Run automatically provisions SSL certificates for custom domains."
echo "It may take up to 24 hours for the certificate to be issued and active."
echo ""

# DNS Configuration Guide
echo -e "${BLUE}📋 Complete DNS Setup Guide for Namecheap:${NC}"
echo ""
echo "1. Log in to your Namecheap account"
echo "2. Go to Domain List and click 'Manage' next to datadrivenjobsearch.com"
echo "3. Go to the 'Advanced DNS' tab"
echo "4. Add a new CNAME record:"
echo "   - Type: CNAME Record"
echo "   - Host: dev"
echo "   - Value: $CNAME_TARGET (if available above)"
echo "   - TTL: Automatic or 300"
echo ""
echo "5. Save the record and wait for DNS propagation (up to 48 hours)"
echo ""

# Testing instructions
echo -e "${BLUE}🧪 Testing:${NC}"
echo ""
echo "After DNS propagation, test your domain:"
echo "1. Check DNS propagation: https://dnschecker.org/"
echo "2. Test the domain: https://$DOMAIN"
echo "3. Verify SSL certificate: https://www.ssllabs.com/ssltest/"
echo ""

# Monitoring and troubleshooting
echo -e "${BLUE}📊 Monitoring:${NC}"
echo ""
echo "Monitor your deployment:"
echo "- Cloud Run logs: gcloud logging read 'resource.type=cloud_run_revision' --project=$PROJECT_ID"
echo "- Domain mapping status: gcloud run domain-mappings describe $DOMAIN --region=$REGION --project=$PROJECT_ID"
echo "- Service status: gcloud run services describe $SERVICE_NAME --region=$REGION --project=$PROJECT_ID"
echo ""

print_success "Domain setup script completed!"
print_info "Remember to update your DNS records and wait for propagation."
