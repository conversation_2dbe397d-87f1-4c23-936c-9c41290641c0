#!/usr/bin/env tsx

/**
 * Migration Script: Add sourceType field to analysisJobs collection
 * 
 * This script adds the sourceType field to existing analysisJobs documents
 * with backward compatibility. All existing jobs will be marked as 'manual'.
 * 
 * Usage:
 *   npm run migrate:add-source-type
 *   NODE_ENV=production npm run migrate:add-source-type
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, query, limit, startAfter, getDocs, writeBatch, doc } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  projectId: process.env.NODE_ENV === 'production' ? 'data-driven-job-search' : 'ddjs-dev-458016',
  // Add other config as needed
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

interface AnalysisJobDocument {
  jobId: string;
  clerkUserId: string;
  status: string;
  sourceType?: 'manual' | 'automatic';
  createdAt: any;
  updatedAt: any;
  [key: string]: any;
}

async function migrateSourceType() {
  const batchSize = 100;
  let processed = 0;
  let lastDoc: any = null;
  
  console.log('🚀 Starting sourceType migration...');
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🗄️  Target project: ${firebaseConfig.projectId}`);
  
  try {
    const analysisJobsRef = collection(db, 'analysisJobs');
    
    while (true) {
      // Build query
      let q = query(analysisJobsRef, limit(batchSize));
      if (lastDoc) {
        q = query(analysisJobsRef, startAfter(lastDoc), limit(batchSize));
      }
      
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.log('📝 No more documents to process');
        break;
      }
      
      // Create batch for updates
      const batch = writeBatch(db);
      let batchCount = 0;
      
      for (const docSnapshot of snapshot.docs) {
        const data = docSnapshot.data() as AnalysisJobDocument;
        
        // Only update if sourceType doesn't exist
        if (!data.sourceType) {
          const docRef = doc(db, 'analysisJobs', docSnapshot.id);
          batch.update(docRef, {
            sourceType: 'manual', // Default for existing jobs
            updatedAt: new Date()
          });
          batchCount++;
        }
      }
      
      // Commit batch if there are updates
      if (batchCount > 0) {
        await batch.commit();
        processed += batchCount;
        console.log(`✅ Migrated batch: ${batchCount} documents (Total: ${processed})`);
      } else {
        console.log(`⏭️  Skipped batch: all documents already have sourceType`);
      }
      
      // Set up for next iteration
      lastDoc = snapshot.docs[snapshot.docs.length - 1];
      
      // Add small delay to avoid overwhelming Firestore
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`🎉 Migration completed successfully!`);
    console.log(`📊 Total documents migrated: ${processed}`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Rollback function for emergency use
async function rollbackSourceType() {
  const batchSize = 100;
  let processed = 0;
  let lastDoc: any = null;
  
  console.log('🔄 Starting sourceType rollback...');
  console.log('⚠️  WARNING: This will remove sourceType field from all documents');
  
  try {
    const analysisJobsRef = collection(db, 'analysisJobs');
    
    while (true) {
      // Build query for documents with sourceType
      let q = query(analysisJobsRef, limit(batchSize));
      if (lastDoc) {
        q = query(analysisJobsRef, startAfter(lastDoc), limit(batchSize));
      }
      
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        break;
      }
      
      // Create batch for updates
      const batch = writeBatch(db);
      let batchCount = 0;
      
      for (const docSnapshot of snapshot.docs) {
        const data = docSnapshot.data() as AnalysisJobDocument;
        
        // Only update if sourceType exists
        if (data.sourceType) {
          const docRef = doc(db, 'analysisJobs', docSnapshot.id);
          // Note: Firestore doesn't have FieldValue.delete() in v9 SDK
          // We'll need to use the admin SDK for this or recreate the document
          console.log(`⚠️  Document ${docSnapshot.id} has sourceType: ${data.sourceType}`);
          batchCount++;
        }
      }
      
      processed += batchCount;
      lastDoc = snapshot.docs[snapshot.docs.length - 1];
    }
    
    console.log(`📊 Found ${processed} documents with sourceType field`);
    console.log('ℹ️  Use Firebase Admin SDK for complete field removal if needed');
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--rollback')) {
    await rollbackSourceType();
  } else {
    await migrateSourceType();
  }
  
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { migrateSourceType, rollbackSourceType };
