/**
 * <PERSON><PERSON>t to clear the database
 * This is used when the --clear-data flag is passed to RunLocally.sh
 *
 * Note: This script no longer clears data as we've removed the in-memory database.
 * It's kept for backward compatibility with existing scripts.
 */

const Database = require('../src/utils/firestore');

async function clearDatabase() {
  console.log('Database clearing is no longer supported...');

  try {
    const database = new Database();

    // Attempt to clear data (will always return false now)
    const cleared = database.clearData();

    console.log('To clear data, please use the Firestore console in GCP');

    process.exit(0);
  } catch (error) {
    console.error('Error initializing database:', error);
    process.exit(1);
  }
}

// Run the function
clearDatabase();
