#!/bin/bash

# Simple Webapp-Only Startup Script
set -e

echo "🌐 Starting Webapp Only (No Emulators)"
echo "======================================"

# Load nvm and use Node.js 22
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 22 || nvm use default

echo "📋 Using Node.js $(node --version) and npm $(npm --version)"

# Ensure environment file is in webapp directory
echo "📋 Setting up environment..."
cp .env.local apps/webapp/.env.local 2>/dev/null || true

# Build projects first
echo "🔨 Building projects..."
npx nx run-many --target=build --projects=shared,email-core,email-analysis-function,notification-handler-function

echo ""
echo "🌐 Starting Next.js webapp..."
echo ""
echo "📱 Webapp will be available at: http://localhost:3000"
echo ""
echo "Note: Cloud Functions will not be available without emulators"
echo "To start with emulators, use: ./scripts/start-dev.sh"
echo ""
echo "Press Ctrl+C to stop the webapp"
echo ""

# Start webapp
npm run dev
