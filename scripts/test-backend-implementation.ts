#!/usr/bin/env tsx

/**
 * Test Script: Backend Implementation Validation
 * 
 * This script tests the new backend implementation for the Email Analysis Status Page:
 * - Tests the new /api/analysis-runs endpoint
 * - Tests the enhanced /api/jobs/[jobId]/status endpoint
 * - Validates data model changes
 * 
 * Usage:
 *   npm run test:backend-implementation
 */

import { NextRequest } from 'next/server';

// Mock Clerk auth for testing
jest.mock('@clerk/nextjs/server', () => ({
  auth: jest.fn().mockResolvedValue({ userId: 'test-user-123' })
}));

// Mock Database service
jest.mock('@webapp/services', () => ({
  Database: jest.fn().mockImplementation(() => ({
    collection: jest.fn().mockReturnValue({
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      get: jest.fn().mockResolvedValue({
        size: 2,
        docs: [
          {
            id: 'job-1',
            data: () => ({
              jobId: 'job-1',
              clerkUserId: 'test-user-123',
              status: 'completed',
              sourceType: 'manual',
              total: 150,
              processed: 150,
              cachedCount: 45,
              newAnalysisCount: 105,
              startDate: '2024-01-01',
              endDate: '2024-01-31',
              current: 'Analysis completed successfully',
              createdAt: { toISOString: () => '2024-01-15T10:30:00Z' },
              updatedAt: { toISOString: () => '2024-01-15T11:45:00Z' },
              completedAt: { toISOString: () => '2024-01-15T11:45:00Z' }
            })
          },
          {
            id: 'job-2',
            data: () => ({
              jobId: 'job-2',
              clerkUserId: 'test-user-123',
              status: 'processing',
              sourceType: 'automatic',
              total: 75,
              processed: 50,
              cachedCount: 20,
              newAnalysisCount: 55,
              startDate: '2024-01-01',
              endDate: '2024-01-15',
              current: 'Processing email 50 of 75',
              createdAt: { toISOString: () => '2024-01-16T09:00:00Z' },
              updatedAt: { toISOString: () => '2024-01-16T09:30:00Z' }
            })
          }
        ]
      }),
      doc: jest.fn().mockReturnValue({
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            jobId: 'job-1',
            clerkUserId: 'test-user-123',
            status: 'completed',
            sourceType: 'manual',
            total: 150,
            processed: 150,
            cachedCount: 45,
            newAnalysisCount: 105,
            startDate: '2024-01-01',
            endDate: '2024-01-31',
            current: 'Analysis completed successfully',
            createdAt: { toISOString: () => '2024-01-15T10:30:00Z', toMillis: () => 1705315800000 },
            updatedAt: { toISOString: () => '2024-01-15T11:45:00Z' },
            completedAt: { toISOString: () => '2024-01-15T11:45:00Z', toMillis: () => 1705320300000 }
          })
        })
      })
    })
  })),
  Logger: jest.fn().mockImplementation(() => ({
    error: jest.fn(),
    info: jest.fn()
  }))
}));

async function testAnalysisRunsEndpoint() {
  console.log('🧪 Testing /api/analysis-runs endpoint...');
  
  try {
    // Import the route handler
    const { GET } = await import('../apps/webapp/src/app/api/analysis-runs/route');
    
    // Create test request
    const request = new NextRequest('http://localhost:3000/api/analysis-runs?page=1&limit=10&sourceType=manual');
    
    // Call the endpoint
    const response = await GET(request);
    const data = await response.json();
    
    // Validate response structure
    console.log('✅ Response received:', JSON.stringify(data, null, 2));
    
    // Validate required fields
    if (!data.analysisRuns || !Array.isArray(data.analysisRuns)) {
      throw new Error('Missing or invalid analysisRuns array');
    }
    
    if (!data.pagination) {
      throw new Error('Missing pagination object');
    }
    
    // Validate analysis run structure
    const firstRun = data.analysisRuns[0];
    if (firstRun) {
      const requiredFields = [
        'jobId', 'sourceType', 'status', 'totalEmails', 'cachedEmails',
        'pendingEmails', 'successfullyAnalyzed', 'emailDateRange',
        'analysisStartTime', 'lastUpdated', 'currentStatus'
      ];
      
      for (const field of requiredFields) {
        if (!(field in firstRun)) {
          throw new Error(`Missing required field: ${field}`);
        }
      }
      
      // Validate calculated fields
      const expectedPending = firstRun.totalEmails - firstRun.successfullyAnalyzed;
      if (firstRun.pendingEmails !== expectedPending) {
        throw new Error(`Incorrect pendingEmails calculation: expected ${expectedPending}, got ${firstRun.pendingEmails}`);
      }
    }
    
    console.log('✅ /api/analysis-runs endpoint test passed');
    return true;
    
  } catch (error) {
    console.error('❌ /api/analysis-runs endpoint test failed:', error);
    return false;
  }
}

async function testJobStatusEndpoint() {
  console.log('🧪 Testing enhanced /api/jobs/[jobId]/status endpoint...');
  
  try {
    // Import the route handler
    const { GET } = await import('../apps/webapp/src/app/api/jobs/[jobId]/status/route');
    
    // Create test request
    const request = new NextRequest('http://localhost:3000/api/jobs/job-1/status');
    const params = { jobId: 'job-1' };
    
    // Call the endpoint
    const response = await GET(request, { params });
    const data = await response.json();
    
    console.log('✅ Enhanced response received:', JSON.stringify(data, null, 2));
    
    // Validate enhanced fields
    const requiredFields = ['jobId', 'status', 'sourceType', 'progress', 'timing', 'emailDetails'];
    for (const field of requiredFields) {
      if (!(field in data)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    // Validate new progress fields
    if (!('percentage' in data.progress)) {
      throw new Error('Missing progress.percentage field');
    }
    
    if (!('pendingEmails' in data.progress)) {
      throw new Error('Missing progress.pendingEmails field');
    }
    
    // Validate timing section
    if (!data.timing.createdAt || !data.timing.updatedAt) {
      throw new Error('Missing timing information');
    }
    
    // Validate emailDetails section
    if (!data.emailDetails.dateRange || !data.emailDetails.totalEmails) {
      throw new Error('Missing email details');
    }
    
    console.log('✅ Enhanced /api/jobs/[jobId]/status endpoint test passed');
    return true;
    
  } catch (error) {
    console.error('❌ Enhanced job status endpoint test failed:', error);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Backend Implementation Tests\n');
  
  const results = await Promise.all([
    testAnalysisRunsEndpoint(),
    testJobStatusEndpoint()
  ]);
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All backend tests passed! Implementation is ready.');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed. Please review the implementation.');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch((error) => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  });
}

export { testAnalysisRunsEndpoint, testJobStatusEndpoint };
