#!/bin/bash

# 🔐 Setup Secrets in Google Cloud Secret Manager
# This script helps you create and manage secrets for the application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SECRETS_PROJECT_ID="data-driven-job-search"  # Production project where secrets are stored

echo -e "${BLUE}🔐 Setting up secrets in Google Cloud Secret Manager${NC}"
echo -e "${BLUE}Secrets Project: ${SECRETS_PROJECT_ID}${NC}"
echo -e "${BLUE}Note: Secrets are stored in the production project for centralized management${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print info
print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if gcloud is installed and user is logged in
if ! command -v gcloud &> /dev/null; then
    print_error "Google Cloud CLI is not installed"
    exit 1
fi

if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
    print_error "Not logged in to Google Cloud. Please run: gcloud auth login"
    exit 1
fi

# Set the project for secret management
gcloud config set project $SECRETS_PROJECT_ID

# Enable Secret Manager API
print_status "🔧 Enabling Secret Manager API..."
gcloud services enable secretmanager.googleapis.com --project=$SECRETS_PROJECT_ID
print_success "Secret Manager API enabled"

# Function to create or update a secret
create_or_update_secret() {
    local secret_name=$1
    local description=$2
    local example_value=$3

    echo ""
    print_status "Setting up secret: $secret_name"
    print_info "$description"

    # Check if secret already exists
    if gcloud secrets describe $secret_name --project=$SECRETS_PROJECT_ID &>/dev/null; then
        print_info "Secret $secret_name already exists"
        echo -n "Do you want to update it? (y/N): "
        read -r response
        if [[ ! $response =~ ^[Yy]$ ]]; then
            print_info "Skipping $secret_name"
            return
        fi
    else
        # Create the secret
        print_status "Creating secret: $secret_name"
        gcloud secrets create $secret_name \
            --replication-policy="automatic" \
            --project=$SECRETS_PROJECT_ID
        print_success "Secret $secret_name created"
    fi

    # Prompt for the secret value
    echo ""
    print_info "Example: $example_value"
    echo -n "Enter the value for $secret_name: "
    read -s secret_value
    echo ""

    if [ -z "$secret_value" ]; then
        print_error "No value provided for $secret_name"
        return 1
    fi

    # Add the secret version
    echo "$secret_value" | gcloud secrets versions add $secret_name \
        --data-file=- \
        --project=$SECRETS_PROJECT_ID

    print_success "Secret $secret_name updated successfully"
}

# Function to test secret retrieval
test_secret_retrieval() {
    local secret_name=$1

    print_status "Testing retrieval of $secret_name..."
    local retrieved_value=$(gcloud secrets versions access latest --secret="$secret_name" --project=$SECRETS_PROJECT_ID 2>/dev/null)

    if [ -n "$retrieved_value" ]; then
        print_success "$secret_name can be retrieved successfully"
    else
        print_error "Failed to retrieve $secret_name"
        return 1
    fi
}

echo -e "${BLUE}📋 We need to set up the following secrets:${NC}"
echo "1. CLERK_PUBLISHABLE_KEY - Clerk authentication public key"
echo "2. CLERK_SECRET_KEY - Clerk authentication secret key"
echo "3. OPENAI_API_KEY - OpenAI API key for email analysis"
echo ""

print_info "You can get these values from:"
echo "- Clerk keys: https://dashboard.clerk.com/ (create production application)"
echo "- OpenAI key: https://platform.openai.com/api-keys"
echo ""

echo -n "Do you want to proceed with setting up secrets? (y/N): "
read -r proceed
if [[ ! $proceed =~ ^[Yy]$ ]]; then
    echo "Setup cancelled."
    exit 0
fi

# Set up each secret
create_or_update_secret "CLERK_PUBLISHABLE_KEY" \
    "Clerk publishable key for authentication (production)" \
    "pk_live_REPLACE_WITH_YOUR_ACTUAL_CLERK_KEY"

create_or_update_secret "CLERK_SECRET_KEY" \
    "Clerk secret key for authentication (production)" \
    "sk_live_REPLACE_WITH_YOUR_ACTUAL_CLERK_SECRET"

create_or_update_secret "OPENAI_API_KEY" \
    "OpenAI API key for email analysis" \
    "sk-proj-REPLACE_WITH_YOUR_ACTUAL_OPENAI_KEY"

echo ""
print_status "🧪 Testing secret retrieval..."

# Test all secrets
test_secret_retrieval "CLERK_PUBLISHABLE_KEY"
test_secret_retrieval "CLERK_SECRET_KEY"
test_secret_retrieval "OPENAI_API_KEY"

echo ""
print_success "🎉 Secret setup completed!"
echo ""

print_info "Your secrets are now stored securely in Google Cloud Secret Manager."
print_info "The deployment script will automatically retrieve these secrets at runtime."
echo ""

print_info "To view your secrets (without revealing values):"
echo "gcloud secrets list --project=$SECRETS_PROJECT_ID"
echo ""

print_info "To update a secret later:"
echo "echo 'new-secret-value' | gcloud secrets versions add SECRET_NAME --data-file=- --project=$SECRETS_PROJECT_ID"
echo ""

print_info "Next steps:"
echo "1. Run the deployment script: ./scripts/deploy-to-gcp.sh"
echo "2. The deployment will automatically use these secrets"
echo "3. No need to create .env.production files manually"
