#!/bin/bash

# 🚀 Complete Deployment Orchestrator with Enhanced Diagnostics
# This script runs the complete deployment process with comprehensive checks and logging
# Updated for Nx migration and polling-based architecture

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging configuration
LOG_FILE="deployment-$(date +%Y%m%d-%H%M%S).log"
SCRIPT_START_TIME=$(date +%s)

# Create logs directory if it doesn't exist
mkdir -p logs

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "logs/$LOG_FILE"
}

# Enhanced logging functions
log_info() {
    log_with_timestamp "INFO: $1"
}

log_warn() {
    log_with_timestamp "WARN: $1"
}

log_error() {
    log_with_timestamp "ERROR: $1"
}

log_debug() {
    if [[ "${DEBUG:-}" == "true" ]]; then
        log_with_timestamp "DEBUG: $1"
    fi
}

# Trap to handle script exit and cleanup
trap 'handle_exit $?' EXIT

handle_exit() {
    local exit_code=$1
    local end_time=$(date +%s)
    local duration=$((end_time - SCRIPT_START_TIME))

    if [ $exit_code -eq 0 ]; then
        log_info "Deployment completed successfully in ${duration} seconds"
        echo -e "${GREEN}🎉 Deployment completed successfully in ${duration} seconds${NC}"
    else
        log_error "Deployment failed with exit code $exit_code after ${duration} seconds"
        echo -e "${RED}💥 Deployment failed with exit code $exit_code after ${duration} seconds${NC}"
        echo -e "${YELLOW}📋 Check the log file for details: logs/$LOG_FILE${NC}"
    fi
}

# Enhanced print functions with logging
print_status() {
    echo -e "${YELLOW}$1${NC}"
    log_info "$1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    log_info "SUCCESS: $1"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    log_error "$1"
}

print_debug() {
    echo -e "${PURPLE}🔍 $1${NC}"
    log_debug "$1"
}

print_warn() {
    echo -e "${YELLOW}⚠️ $1${NC}"
    log_warn "$1"
}

# Environment validation functions
validate_environment() {
    print_status "🔍 Validating deployment environment..."

    local validation_failed=false

    # Check required commands
    local required_commands=("gcloud" "npm" "node" "git" "curl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            print_error "Required command not found: $cmd"
            validation_failed=true
        else
            local version=$($cmd --version 2>/dev/null | head -n1 || echo "unknown")
            print_debug "$cmd version: $version"
        fi
    done

    # Check Node.js version
    local node_version=$(node --version)
    local node_major=$(echo $node_version | cut -d'.' -f1 | sed 's/v//')
    if [ "$node_major" -lt 18 ]; then
        print_error "Node.js version $node_version is too old. Minimum required: v18"
        validation_failed=true
    else
        print_debug "Node.js version: $node_version ✓"
    fi

    # Check if we're in the correct directory
    if [ ! -f "package.json" ] || [ ! -d "apps/webapp" ]; then
        print_error "Not in the correct project directory. Expected to find package.json and apps/webapp"
        validation_failed=true
    else
        print_debug "Project directory structure validated ✓"
    fi

    # Check Git status
    if [ -n "$(git status --porcelain)" ]; then
        print_warn "Working directory has uncommitted changes"
        git status --short
    else
        print_debug "Git working directory is clean ✓"
    fi

    # Check current branch
    local current_branch=$(git branch --show-current)
    print_debug "Current Git branch: $current_branch"

    if [ "$validation_failed" = true ]; then
        print_error "Environment validation failed. Please fix the issues above."
        exit 1
    fi

    print_success "Environment validation passed"
}

# GCP authentication validation
validate_gcp_auth() {
    print_status "🔐 Validating GCP authentication..."

    # Check if gcloud is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
        print_error "No active GCP authentication found"
        echo "Please run: gcloud auth login"
        return 1
    fi

    local active_account=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1)
    print_debug "Active GCP account: $active_account"

    # Check project access
    local dev_project="ddjs-dev-458016"
    local prod_project="data-driven-job-search"

    if ! gcloud projects describe "$dev_project" &>/dev/null; then
        print_error "Cannot access dev project: $dev_project"
        return 1
    fi

    if ! gcloud projects describe "$prod_project" &>/dev/null; then
        print_error "Cannot access production project: $prod_project"
        return 1
    fi

    print_debug "GCP project access validated ✓"
    print_success "GCP authentication validated"
}

echo -e "${BLUE}🚀 Complete Deployment Process for Data Driven Job Search${NC}"
echo -e "${BLUE}Target: dev.datadrivenjobsearch.com${NC}"
echo -e "${CYAN}Log file: logs/$LOG_FILE${NC}"
echo ""

log_info "Starting deployment process"
log_info "Script version: Enhanced with Nx migration support"
log_info "Target environment: ddjs-dev-458016 (dev)"

# Function to ask for confirmation
ask_confirmation() {
    echo -e "${YELLOW}$1${NC}"
    log_info "Asking for user confirmation: $1"
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Deployment cancelled by user"
        echo "Deployment cancelled."
        exit 1
    fi
    log_info "User confirmed to continue"
}

# Enhanced step execution with error context
execute_step() {
    local step_name="$1"
    local script_path="$2"
    local step_number="$3"

    print_status "🔄 Step $step_number: $step_name"
    log_info "Starting step $step_number: $step_name"

    if [ ! -f "$script_path" ]; then
        print_error "Script not found: $script_path"
        log_error "Script file missing: $script_path"
        echo "Expected script location: $(pwd)/$script_path"
        echo "Available scripts in scripts/:"
        ls -la scripts/ 2>/dev/null || echo "scripts/ directory not found"
        exit 1
    fi

    # Make script executable
    chmod +x "$script_path"

    # Execute with error capture (timeout removed for macOS compatibility)
    local start_time=$(date +%s)
    if "$script_path" 2>&1 | tee -a "logs/$LOG_FILE"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        print_success "$step_name completed in ${duration} seconds"
        log_info "Step $step_number completed successfully in ${duration} seconds"
    else
        local exit_code=${PIPESTATUS[0]}
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        print_error "$step_name failed with exit code $exit_code after ${duration} seconds"
        log_error "Step $step_number failed with exit code $exit_code after ${duration} seconds"

        echo ""
        echo -e "${YELLOW}🔍 Troubleshooting Information:${NC}"
        echo "- Step: $step_name"
        echo "- Script: $script_path"
        echo "- Exit code: $exit_code"
        echo "- Duration: ${duration} seconds"
        echo "- Log file: logs/$LOG_FILE"
        echo ""

        return $exit_code
    fi
}

# Step 0: Environment validation
print_status "🔍 Step 0: Environment validation..."
validate_environment
validate_gcp_auth
echo ""

# Step 1: Pre-deployment check
execute_step "Pre-deployment checks" "./scripts/pre-deployment-check.sh" "1"

if [ $? -ne 0 ]; then
    print_error "Pre-deployment checks failed. Please fix the issues and try again."
    echo ""
    echo -e "${YELLOW}🔧 Common fixes:${NC}"
    echo "For authentication debugging, run:"
    echo "  ./scripts/debug-auth.sh"
    echo ""
    echo "Or check authentication manually:"
    echo "  firebase login"
    echo "  gcloud auth login"
    echo ""
    echo "To check project access:"
    echo "  gcloud projects list"
    echo "  gcloud config set project ddjs-dev-458016"
    exit 1
fi

echo ""

# Step 2: Check if secrets are set up
print_status "🔐 Step 2: Checking Secret Manager setup..."
SECRETS_PROJECT_ID="data-driven-job-search"
log_info "Checking for secrets in production project: $SECRETS_PROJECT_ID"

# Enhanced secret validation with comprehensive listing
validate_secrets() {
    local required_secrets=("OPENAI_API_KEY" "CLERK_SECRET_KEY")
    local missing_secrets=()

    print_status "🔍 Listing all secrets in both projects for debugging..."

    # List all secrets in production project
    echo ""
    echo -e "${CYAN}📋 All secrets in production project ($SECRETS_PROJECT_ID):${NC}"
    if gcloud secrets list --project=$SECRETS_PROJECT_ID --format="table(name)" 2>/dev/null; then
        echo ""
    else
        print_error "Failed to list secrets in production project: $SECRETS_PROJECT_ID"
        echo "Error details:"
        gcloud secrets list --project=$SECRETS_PROJECT_ID 2>&1 || true
        echo ""
    fi

    # List all secrets in dev project for comparison
    local dev_project="ddjs-dev-458016"
    echo -e "${CYAN}📋 All secrets in dev project ($dev_project):${NC}"
    if gcloud secrets list --project=$dev_project --format="table(name)" 2>/dev/null; then
        echo ""
    else
        print_error "Failed to list secrets in dev project: $dev_project"
        echo "Error details:"
        gcloud secrets list --project=$dev_project 2>&1 || true
        echo ""
    fi

    # Now check for required secrets with better error handling
    print_status "🔍 Checking for required secrets..."
    for secret in "${required_secrets[@]}"; do
        print_debug "Checking for secret: $secret"

        # Try multiple approaches to find the secret
        local found_in_prod=false
        local found_in_dev=false

        # Check in production project
        if gcloud secrets describe "$secret" --project=$SECRETS_PROJECT_ID &>/dev/null; then
            found_in_prod=true
            print_debug "Secret found in production: $secret ✓"
        elif gcloud secrets list --project=$SECRETS_PROJECT_ID --format="value(name)" 2>/dev/null | grep -q "^${secret}$"; then
            found_in_prod=true
            print_debug "Secret found in production (via list): $secret ✓"
        fi

        # Check in dev project
        if gcloud secrets describe "$secret" --project=$dev_project &>/dev/null; then
            found_in_dev=true
            print_debug "Secret found in dev: $secret ✓"
        elif gcloud secrets list --project=$dev_project --format="value(name)" 2>/dev/null | grep -q "^${secret}$"; then
            found_in_dev=true
            print_debug "Secret found in dev (via list): $secret ✓"
        fi

        if [ "$found_in_prod" = true ]; then
            print_success "Secret available in production: $secret"
        elif [ "$found_in_dev" = true ]; then
            print_warn "Secret found in dev but not production: $secret"
            echo "  Consider copying from dev to production"
        else
            missing_secrets+=("$secret")
            print_error "Secret not found in either project: $secret"
        fi
    done

    if [ ${#missing_secrets[@]} -gt 0 ]; then
        echo ""
        print_error "Missing secrets in Secret Manager:"
        for secret in "${missing_secrets[@]}"; do
            echo "  - $secret"
        done
        echo ""
        echo -e "${YELLOW}🔧 Troubleshooting options:${NC}"
        echo "1. Set up secrets automatically:"
        echo "   ./scripts/setup-secrets.sh"
        echo ""
        echo "2. Check current project configuration:"
        echo "   gcloud config get-value project"
        echo "   gcloud config set project $SECRETS_PROJECT_ID"
        echo ""
        echo "3. List secrets manually:"
        echo "   gcloud secrets list --project=$SECRETS_PROJECT_ID"
        echo "   gcloud secrets list --project=$dev_project"
        echo ""
        echo "4. Check IAM permissions:"
        echo "   gcloud projects get-iam-policy $SECRETS_PROJECT_ID"
        echo ""
        ask_confirmation "Do you want to set up missing secrets now?"
        execute_step "Secret Manager setup" "./scripts/setup-secrets.sh" "2a"
        if [ $? -ne 0 ]; then
            print_error "Secret setup failed"
            exit 1
        fi
    else
        print_success "All required secrets found in Secret Manager"
    fi
}

validate_secrets

# Step 3: Build validation
print_status "🔨 Step 3: Validating builds..."
log_info "Checking if all components build successfully after Nx migration"

# Check if builds are working
print_status "Running build validation with enhanced error handling..."

# Build each component individually with proper error filtering
components=("webapp" "email-analysis-function" "notification-handler-function")
build_failed=false

for component in "${components[@]}"; do
    print_status "Building $component..."

    # Capture build output and filter for actual errors vs warnings
    build_output=$(npx nx build "$component" 2>&1)
    build_exit_code=$?

    # Log the full output
    echo "$build_output" >> "logs/$LOG_FILE"

    # Check for actual build failures vs Nx lockfile warnings
    if [ $build_exit_code -eq 0 ]; then
        # Build succeeded, check for lockfile warnings
        if echo "$build_output" | grep -q "Pruned lock file creation failed"; then
            print_warn "$component build successful with lockfile pruning warning (non-fatal)"
            echo "  ℹ️  Nx will use root lockfile as fallback - deployment will work normally"
        else
            print_success "$component build successful"
        fi
    else
        # Actual build failure
        print_error "$component build failed with exit code $build_exit_code"
        echo "Build output:"
        echo "$build_output"
        build_failed=true
    fi
done

if [ "$build_failed" = true ]; then
    print_error "One or more builds failed. Check the log file for details: logs/$LOG_FILE"
    exit 1
else
    print_success "All builds completed successfully"
    echo ""
    echo -e "${CYAN}ℹ️  Note: Nx lockfile pruning warnings are non-fatal and don't affect deployment${NC}"
fi

# Step 4: Confirm deployment
ask_confirmation "🚨 This will deploy to production GCP environment (ddjs-dev-458016)."

# Step 5: Deploy to GCP
execute_step "GCP deployment" "./scripts/deploy-to-gcp.sh" "5"

if [ $? -ne 0 ]; then
    print_error "GCP deployment failed"
    echo ""
    echo -e "${YELLOW}🔧 Common GCP deployment issues:${NC}"
    echo "1. Check if Cloud Run API is enabled"
    echo "2. Verify Cloud Functions API is enabled"
    echo "3. Check IAM permissions for deployment"
    echo "4. Ensure billing is enabled on the project"
    echo ""
    echo "To debug further:"
    echo "  gcloud config list"
    echo "  gcloud services list --enabled --project=ddjs-dev-458016"
    exit 1
fi

echo ""

# Step 6: Domain setup
print_status "🌐 Step 6: Setting up custom domain..."
execute_step "Domain setup" "./scripts/setup-domain.sh" "6"

if [ $? -ne 0 ]; then
    print_error "Domain setup encountered issues (this is often normal for first-time setup)"
    log_warn "Domain setup failed - this may require manual verification"
    echo ""
    echo -e "${YELLOW}ℹ️ Domain setup notes:${NC}"
    echo "- You may need to verify domain ownership first"
    echo "- DNS propagation can take up to 48 hours"
    echo "- SSL certificate provisioning may take additional time"
fi

echo ""
print_success "Deployment process completed!"
echo ""

# Enhanced deployment summary with diagnostics
echo -e "${BLUE}🎉 Enhanced Deployment Summary${NC}"
echo -e "${CYAN}Deployment completed at: $(date)${NC}"
echo -e "${CYAN}Total deployment time: $(($(date +%s) - SCRIPT_START_TIME)) seconds${NC}"
echo -e "${CYAN}Log file: logs/$LOG_FILE${NC}"
echo ""

# Verify deployment status
print_status "🔍 Verifying deployment status..."

# Check Cloud Run service
if gcloud run services describe webapp --region=us-central1 --project=ddjs-dev-458016 &>/dev/null; then
    echo -e "${GREEN}✅ Webapp deployed to Cloud Run${NC}"
    webapp_url=$(gcloud run services describe webapp --region=us-central1 --project=ddjs-dev-458016 --format="value(status.url)")
    echo -e "${CYAN}   URL: $webapp_url${NC}"
else
    echo -e "${RED}❌ Webapp deployment verification failed${NC}"
fi

# Check Cloud Functions
functions=("analyzeEmail" "handleNotification")
for func in "${functions[@]}"; do
    if gcloud functions describe "$func" --region=us-central1 --project=ddjs-dev-458016 &>/dev/null; then
        echo -e "${GREEN}✅ Cloud Function deployed: $func${NC}"
    else
        echo -e "${RED}❌ Cloud Function verification failed: $func${NC}"
    fi
done

# Check Pub/Sub topics
topics=("email-analysis" "notification-handler")
for topic in "${topics[@]}"; do
    if gcloud pubsub topics describe "$topic" --project=ddjs-dev-458016 &>/dev/null; then
        echo -e "${GREEN}✅ Pub/Sub topic created: $topic${NC}"
    else
        echo -e "${YELLOW}⚠️ Pub/Sub topic verification failed: $topic${NC}"
    fi
done

echo -e "${GREEN}✅ Secret Manager configured${NC}"
echo -e "${GREEN}✅ Firestore database ready${NC}"
echo ""

echo -e "${BLUE}📋 Next Steps:${NC}"
echo ""
echo "1. 🌐 Configure DNS in Namecheap:"
echo "   - Log in to Namecheap"
echo "   - Go to datadrivenjobsearch.com → Advanced DNS"
echo "   - Add CNAME record: dev → [target from domain setup]"
echo ""
echo "2. ⏳ Wait for DNS propagation (up to 48 hours)"
echo ""
echo "3. 🧪 Test your deployment:"
echo "   - Check DNS: https://dnschecker.org/"
echo "   - Visit: https://dev.datadrivenjobsearch.com"
echo "   - Test SSL: https://www.ssllabs.com/ssltest/"
echo "   - Test polling architecture: Try email analysis workflow"
echo ""
echo "4. 📊 Monitor your deployment:"
echo "   - Cloud Run: https://console.cloud.google.com/run?project=ddjs-dev-458016"
echo "   - Cloud Functions: https://console.cloud.google.com/functions/list?project=ddjs-dev-458016"
echo "   - Firestore: https://console.firebase.google.com/project/ddjs-dev-458016/firestore"
echo "   - Logs: gcloud logging tail 'resource.type=cloud_run_revision' --project=ddjs-dev-458016"
echo ""

echo -e "${YELLOW}⚠️ Important Notes (Updated for Nx Migration):${NC}"
echo "- SSL certificate may take up to 24 hours to be active"
echo "- DNS propagation can take up to 48 hours"
echo "- You may need to verify domain ownership in Google Search Console"
echo "- New polling-based architecture eliminates Socket.IO dependencies"
echo "- Cloud Functions now use real services instead of mocks"
echo "- Job tracking is now database-driven for better reliability"
echo ""

echo -e "${PURPLE}🔧 Troubleshooting Commands:${NC}"
echo "If issues occur, use these diagnostic commands:"
echo ""
echo "Check deployment status:"
echo "  gcloud run services list --project=ddjs-dev-458016"
echo "  gcloud functions list --project=ddjs-dev-458016"
echo ""
echo "View logs:"
echo "  gcloud logging read 'resource.type=cloud_run_revision' --project=ddjs-dev-458016 --limit=50"
echo "  gcloud logging read 'resource.type=cloud_function' --project=ddjs-dev-458016 --limit=50"
echo ""
echo "Test API endpoints:"
echo "  curl -X GET 'https://[your-cloud-run-url]/api/tokens'"
echo "  curl -X POST 'https://[your-cloud-run-url]/api/emails/estimate' -H 'Content-Type: application/json' -d '{\"startDate\":\"2024-01-01\",\"endDate\":\"2024-01-02\"}'"
echo ""
echo "Check Firestore:"
echo "  gcloud firestore databases list --project=ddjs-dev-458016"
echo ""
echo "Re-run specific deployment steps:"
echo "  ./scripts/deploy-to-gcp.sh  # Re-deploy services"
echo "  ./scripts/setup-domain.sh   # Re-configure domain"
echo ""

echo -e "${GREEN}🎉 Your enhanced application is now deployed to the cloud!${NC}"
echo -e "${CYAN}Architecture: Nx monorepo with polling-based job tracking${NC}"
echo -e "${CYAN}Log file saved: logs/$LOG_FILE${NC}"
