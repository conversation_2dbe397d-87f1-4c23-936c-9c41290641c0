#!/bin/bash

# 🔍 Pre-deployment Check Script
# Verifies that everything is ready for GCP deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Pre-deployment Check for Data Driven Job Search${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

# Function to print check result
print_check() {
    if [ $2 -eq 0 ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1${NC}"
    fi
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# Function to print info
print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

ERRORS=0

# Check if required tools are installed
echo -e "${BLUE}🛠️ Checking required tools...${NC}"

command -v node >/dev/null 2>&1
print_check "Node.js installed" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

command -v npm >/dev/null 2>&1
print_check "npm installed" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

command -v firebase >/dev/null 2>&1
print_check "Firebase CLI installed" $?
if [ $? -ne 0 ]; then
    ((ERRORS++))
    print_info "Install with: npm install -g firebase-tools"
fi

command -v gcloud >/dev/null 2>&1
print_check "Google Cloud CLI installed" $?
if [ $? -ne 0 ]; then
    ((ERRORS++))
    print_info "Install from: https://cloud.google.com/sdk/docs/install"
fi

command -v npx >/dev/null 2>&1
print_check "npx available" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

echo ""

# Check authentication
echo -e "${BLUE}🔐 Checking authentication...${NC}"

# Function to run command with timeout (macOS compatible)
run_with_timeout() {
    local timeout_duration=$1
    shift
    local cmd="$@"

    # Run command in background
    eval "$cmd" >/dev/null 2>&1 &
    local cmd_pid=$!

    # Wait for timeout or command completion
    local count=0
    while [ $count -lt $timeout_duration ]; do
        if ! kill -0 $cmd_pid 2>/dev/null; then
            wait $cmd_pid
            return $?
        fi
        sleep 1
        count=$((count + 1))
    done

    # Timeout reached, kill the command
    kill $cmd_pid 2>/dev/null
    wait $cmd_pid 2>/dev/null
    return 124  # Timeout exit code
}

# Skip Firebase authentication check since we're using pure GCP
print_check "Firebase authentication (skipped - using pure GCP)" 0

# Check Google Cloud authentication with timeout
run_with_timeout 10 "gcloud auth list --filter=status:ACTIVE --format=\"value(account)\" | head -n1"
GCLOUD_AUTH_STATUS=$?
if [ $GCLOUD_AUTH_STATUS -eq 124 ]; then
    print_check "Google Cloud authentication (timed out)" 1
    ((ERRORS++))
    print_info "gcloud command timed out. Try: gcloud auth login"
elif [ $GCLOUD_AUTH_STATUS -ne 0 ]; then
    print_check "Google Cloud authentication" 1
    ((ERRORS++))
    print_info "Login with: gcloud auth login"
else
    print_check "Google Cloud authentication" 0
fi

# Check access to specific projects
print_status "Checking project access..."

# Check access to deployment project
run_with_timeout 10 "gcloud projects describe ddjs-dev-458016"
DEV_PROJECT_STATUS=$?
if [ $DEV_PROJECT_STATUS -eq 124 ]; then
    print_check "Access to ddjs-dev-458016 (timed out)" 1
    ((ERRORS++))
elif [ $DEV_PROJECT_STATUS -ne 0 ]; then
    print_check "Access to ddjs-dev-458016" 1
    ((ERRORS++))
    print_info "You may not have access to the ddjs-dev-458016 project"
else
    print_check "Access to ddjs-dev-458016" 0
fi

# Check access to secrets project
run_with_timeout 10 "gcloud projects describe data-driven-job-search"
SECRETS_PROJECT_STATUS=$?
if [ $SECRETS_PROJECT_STATUS -eq 124 ]; then
    print_check "Access to data-driven-job-search (timed out)" 1
    ((ERRORS++))
elif [ $SECRETS_PROJECT_STATUS -ne 0 ]; then
    print_check "Access to data-driven-job-search" 1
    ((ERRORS++))
    print_info "You may not have access to the data-driven-job-search project"
else
    print_check "Access to data-driven-job-search" 0
fi

echo ""

# Check project structure
echo -e "${BLUE}📁 Checking project structure...${NC}"

[ -f "package.json" ]
print_check "package.json exists" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

# Skip Firebase files since we're using pure GCP
print_check "firebase.json (skipped - using pure GCP)" 0
print_check ".firebaserc (skipped - using pure GCP)" 0

[ -f "nx.json" ]
print_check "nx.json exists" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

[ -d "apps/webapp" ]
print_check "webapp directory exists" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

[ -d "apps/email-analysis-function" ]
print_check "email-analysis-function directory exists" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

[ -d "apps/notification-handler-function" ]
print_check "notification-handler-function directory exists" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

echo ""

# Check environment files
echo -e "${BLUE}🌍 Checking environment configuration...${NC}"

[ -f ".env.local" ]
print_check ".env.local exists" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

# Skip .env.production check since we use Secret Manager
print_check ".env.production (skipped - using Secret Manager)" 0

# Check for required environment variables in .env.local
if [ -f ".env.local" ]; then
    grep -q "OPENAI_API_KEY" .env.local
    print_check "OPENAI_API_KEY in .env.local" $?
    if [ $? -ne 0 ]; then ((ERRORS++)); fi

    grep -q "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY" .env.local
    print_check "Clerk keys in .env.local" $?
    if [ $? -ne 0 ]; then ((ERRORS++)); fi
fi

echo ""

# Check GCP project configuration
echo -e "${BLUE}☁️ Checking GCP project configuration...${NC}"

# Check if we can access the target project
run_with_timeout 10 "gcloud projects describe ddjs-dev-458016"
if [ $? -eq 0 ]; then
    print_check "GCP project ddjs-dev-458016 accessible" 0
else
    print_check "GCP project ddjs-dev-458016 not accessible" 1
    ((ERRORS++))
fi

echo ""

# Check if we can build the project
echo -e "${BLUE}🔨 Testing build process...${NC}"

npm install >/dev/null 2>&1
print_check "npm install successful" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

npx nx run-many --target=build --projects=shared,email-core >/dev/null 2>&1
print_check "Core libraries build" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

echo ""

# Check deployment scripts
echo -e "${BLUE}📜 Checking deployment scripts...${NC}"

[ -f "scripts/deploy-to-gcp.sh" ]
print_check "deploy-to-gcp.sh exists" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

[ -x "scripts/deploy-to-gcp.sh" ]
print_check "deploy-to-gcp.sh is executable" $?
if [ $? -ne 0 ]; then
    print_warning "Run: chmod +x scripts/deploy-to-gcp.sh"
fi

[ -f "scripts/setup-domain.sh" ]
print_check "setup-domain.sh exists" $?
if [ $? -ne 0 ]; then ((ERRORS++)); fi

[ -x "scripts/setup-domain.sh" ]
print_check "setup-domain.sh is executable" $?
if [ $? -ne 0 ]; then
    print_warning "Run: chmod +x scripts/setup-domain.sh"
fi

echo ""

# Summary
echo -e "${BLUE}📊 Pre-deployment Check Summary${NC}"
echo ""

if [ $ERRORS -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! Ready for deployment.${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Run: ./scripts/deploy-to-gcp.sh"
    echo "2. Run: ./scripts/setup-domain.sh"
    echo "3. Configure DNS in Namecheap"
    echo "4. Wait for DNS propagation"
    echo "5. Test: https://dev.datadrivenjobsearch.com"
else
    echo -e "${RED}❌ Found $ERRORS issues that need to be resolved before deployment.${NC}"
    echo ""
    echo -e "${YELLOW}Please fix the issues above and run this script again.${NC}"
    exit 1
fi
