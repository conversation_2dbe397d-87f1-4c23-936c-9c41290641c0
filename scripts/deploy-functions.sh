#!/bin/bash

# Deploy Cloud Functions script
set -e

echo "🚀 Building all projects..."
npx nx run-many --target=build --projects=shared,email-core,email-analysis-function,notification-handler-function

echo "📦 Preparing function deployments..."

# Copy package.json and dependencies to function dist directories
cp package.json dist/apps/email-analysis-function/
cp package.json dist/apps/notification-handler-function/

echo "☁️ Deploying Cloud Functions..."
echo "Note: Make sure you have Firebase CLI installed and are logged in"
echo "Run: npm install -g firebase-tools && firebase login"

echo "To deploy, run:"
echo "firebase deploy --only functions:email-analysis-function"
echo "firebase deploy --only functions:notification-handler-function"

echo "✅ Build completed successfully!"
echo "📋 Next steps:"
echo "1. Set up Pub/Sub topics: email-analysis-requests"
echo "2. Configure environment variables for OpenAI API key"
echo "3. Set up IAM permissions for Cloud Functions"
echo "4. Update Gmail push notification webhook URL"
