#!/bin/bash

# Local Testing Script for Cloud Functions Architecture
set -e

echo "🚀 Starting Local Testing Environment for Cloud Functions"
echo "========================================================"

# Load nvm and use Node.js 22
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 22 || nvm use default

echo "📋 Using Node.js $(node --version) and npm $(npm --version)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Build all projects
print_status "Building all projects..."
npx nx run-many --target=build --projects=shared,email-core,email-analysis-function,notification-handler-function

if [ $? -eq 0 ]; then
    print_success "All projects built successfully!"
else
    print_error "Build failed. Please fix build errors before continuing."
    exit 1
fi

# Step 2: Prepare Cloud Functions for deployment
print_status "Preparing Cloud Functions for local deployment..."

# Copy package.json to function directories
cp package.json dist/apps/email-analysis-function/
cp package.json dist/apps/notification-handler-function/

# Install dependencies in function directories
print_status "Installing dependencies in Cloud Function directories..."
cd dist/apps/email-analysis-function && npm install --production && cd ../../..
cd dist/apps/notification-handler-function && npm install --production && cd ../../..

print_success "Cloud Functions prepared for deployment!"

# Step 3: Set environment variables
print_status "Setting up environment variables..."
export FIRESTORE_EMULATOR_HOST="localhost:8080"
export PUBSUB_EMULATOR_HOST="localhost:8085"
export FUNCTIONS_EMULATOR_HOST="localhost:5001"
export WEBAPP_PROGRESS_WEBHOOK_URL="http://localhost:3000/api/emails/progress-webhook"

print_success "Environment variables set!"

# Step 4: Start Firebase Emulators
print_status "Starting Firebase Emulators..."
print_warning "This will start the emulators in the background."
print_warning "Access the Emulator UI at: http://localhost:4000"
print_warning "Functions will be available at: http://localhost:5001"
print_warning "Firestore will be available at: http://localhost:8080"
print_warning "Pub/Sub will be available at: http://localhost:8085"

echo ""
echo "🔧 TESTING INSTRUCTIONS:"
echo "========================"
echo ""
echo "1. 📱 Start the webapp in another terminal:"
echo "   cd /path/to/your/webapp"
echo "   npm run dev"
echo ""
echo "2. 🌐 Access the application:"
echo "   - Webapp: http://localhost:3000"
echo "   - Firebase Emulator UI: http://localhost:4000"
echo ""
echo "3. 🧪 Test the Cloud Functions:"
echo "   - Trigger email analysis from the webapp"
echo "   - Check the Emulator UI for function logs"
echo "   - Monitor Pub/Sub messages in the emulator"
echo ""
echo "4. 📊 Monitor progress:"
echo "   - Watch real-time progress updates in the webapp"
echo "   - Check function execution logs in the emulator UI"
echo ""
echo "5. 🛑 To stop the emulators:"
echo "   - Press Ctrl+C in this terminal"
echo "   - Or run: firebase emulators:stop"
echo ""
echo "Starting emulators now..."

# Start the emulators
firebase emulators:start
