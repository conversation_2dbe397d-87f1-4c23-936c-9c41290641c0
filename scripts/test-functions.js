#!/usr/bin/env node

// Test script for Cloud Functions
const { PubSub } = require('@google-cloud/pubsub');

// Configure for emulator
process.env.PUBSUB_EMULATOR_HOST = 'localhost:8085';

async function testEmailAnalysisFunction() {
  console.log('🧪 Testing Email Analysis Function...');
  
  try {
    const pubsub = new PubSub({
      projectId: 'ddjs-dev-458016'
    });

    // Create the topic if it doesn't exist
    const topicName = 'email-analysis-requests';
    const [topic] = await pubsub.topic(topicName).get({ autoCreate: true });
    
    console.log(`📤 Publishing test message to topic: ${topicName}`);

    // Test message
    const testMessage = {
      clerkUserId: 'test_user_123',
      messageId: 'test_message_456',
      monitoredEmail: '<EMAIL>',
      batchIndex: 0,
      totalInBatch: 1
    };

    const messageBuffer = Buffer.from(JSON.stringify(testMessage));
    const messageId = await topic.publishMessage({ data: messageBuffer });
    
    console.log(`✅ Message published with ID: ${messageId}`);
    console.log('📋 Test message data:', testMessage);
    console.log('');
    console.log('🔍 Check the Firebase Emulator UI at http://localhost:4000 to see:');
    console.log('   - Function execution logs');
    console.log('   - Pub/Sub message delivery');
    console.log('   - Any errors or output');
    
  } catch (error) {
    console.error('❌ Error testing function:', error);
  }
}

async function testNotificationHandler() {
  console.log('🧪 Testing Notification Handler Function...');
  
  try {
    const testNotification = {
      message: {
        data: Buffer.from(JSON.stringify({
          emailAddress: '<EMAIL>',
          historyId: '12345'
        })).toString('base64')
      }
    };

    console.log('📤 Sending test notification to handler...');
    console.log('📋 Test notification data:', testNotification);
    
    // Make HTTP request to the function
    const response = await fetch('http://localhost:5001/ddjs-dev-458016/us-central1/handleGmailNotification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testNotification)
    });

    if (response.ok) {
      const result = await response.text();
      console.log('✅ Notification handler response:', result);
    } else {
      console.error('❌ Notification handler error:', response.status, response.statusText);
    }
    
  } catch (error) {
    console.error('❌ Error testing notification handler:', error);
  }
}

async function main() {
  console.log('🚀 Cloud Functions Test Suite');
  console.log('==============================');
  console.log('');
  console.log('⚠️  Make sure Firebase emulators are running first!');
  console.log('   Run: ./scripts/test-locally.sh');
  console.log('');

  // Wait a moment for user to read
  await new Promise(resolve => setTimeout(resolve, 2000));

  await testEmailAnalysisFunction();
  console.log('');
  await testNotificationHandler();
  
  console.log('');
  console.log('🎉 Test suite completed!');
  console.log('📊 Check the Firebase Emulator UI for detailed logs and results.');
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testEmailAnalysisFunction, testNotificationHandler };
