#!/usr/bin/env node

/**
 * Cloud Functions Production Test Suite
 * 
 * This script tests the deployed Cloud Functions in the dev environment
 * to validate that all production implementations are working correctly.
 */

const https = require('https');
const { PubSub } = require('@google-cloud/pubsub');

class CloudFunctionsValidator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
    
    // Configuration for dev environment
    this.config = {
      projectId: 'ddjs-dev-458016',
      notificationHandlerUrl: 'https://handlegmailnotification-4r5k3ebaga-uc.a.run.app',
      region: 'us-central1'
    };
    
    this.pubsub = new PubSub({ projectId: this.config.projectId });
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'error': '❌',
      'warning': '⚠️',
      'success': '✅',
      'info': 'ℹ️'
    }[type] || 'ℹ️';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  addResult(test, passed, message) {
    this.results.tests.push({ test, passed, message });
    if (passed) {
      this.results.passed++;
      this.log(`PASS: ${test} - ${message}`, 'success');
    } else {
      this.results.failed++;
      this.log(`FAIL: ${test} - ${message}`, 'error');
    }
  }

  /**
   * Test 1: Verify Cloud Functions are deployed and accessible
   */
  async testCloudFunctionsDeployment() {
    this.log('Testing Cloud Functions deployment...', 'info');

    try {
      // Test notification handler HTTP endpoint
      const response = await this.makeHttpRequest(this.config.notificationHandlerUrl, {
        method: 'GET'
      });

      if (response.statusCode === 405) {
        // Expected: Method Not Allowed for GET request
        this.addResult('Notification Handler Deployment', true, 
          'Function is deployed and responding (405 Method Not Allowed expected for GET)');
      } else {
        this.addResult('Notification Handler Deployment', false, 
          `Unexpected response: ${response.statusCode}`);
      }

    } catch (error) {
      this.addResult('Notification Handler Deployment', false, 
        `Failed to reach function: ${error.message}`);
    }
  }

  /**
   * Test 2: Test Pub/Sub topics exist
   */
  async testPubSubTopics() {
    this.log('Testing Pub/Sub topics...', 'info');

    const requiredTopics = ['email-analysis-requests', 'gmail-notifications'];

    for (const topicName of requiredTopics) {
      try {
        const topic = this.pubsub.topic(topicName);
        const [exists] = await topic.exists();
        
        if (exists) {
          this.addResult(`Pub/Sub Topic: ${topicName}`, true, 'Topic exists and is accessible');
        } else {
          this.addResult(`Pub/Sub Topic: ${topicName}`, false, 'Topic does not exist');
        }
      } catch (error) {
        this.addResult(`Pub/Sub Topic: ${topicName}`, false, 
          `Error checking topic: ${error.message}`);
      }
    }
  }

  /**
   * Test 3: Test notification handler with mock Gmail notification
   */
  async testNotificationHandler() {
    this.log('Testing notification handler with mock data...', 'info');

    const mockNotification = {
      message: {
        data: Buffer.from(JSON.stringify({
          historyId: '12345',
          emailAddress: '<EMAIL>'
        })).toString('base64')
      }
    };

    try {
      const response = await this.makeHttpRequest(this.config.notificationHandlerUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(mockNotification)
      });

      if (response.statusCode === 200) {
        this.addResult('Notification Handler Processing', true, 
          'Function processed mock notification successfully');
      } else {
        this.addResult('Notification Handler Processing', false, 
          `Unexpected response: ${response.statusCode} - ${response.data}`);
      }

    } catch (error) {
      this.addResult('Notification Handler Processing', false, 
        `Failed to process notification: ${error.message}`);
    }
  }

  /**
   * Test 4: Test email analysis function by publishing to Pub/Sub
   */
  async testEmailAnalysisFunction() {
    this.log('Testing email analysis function via Pub/Sub...', 'info');

    const mockAnalysisRequest = {
      clerkUserId: 'test_user_123',
      messageId: 'test_message_456',
      monitoredEmail: '<EMAIL>',
      historyId: '12345'
    };

    try {
      const topic = this.pubsub.topic('email-analysis-requests');
      const messageId = await topic.publishMessage({
        data: Buffer.from(JSON.stringify(mockAnalysisRequest))
      });

      this.addResult('Email Analysis Function Trigger', true, 
        `Successfully published test message: ${messageId}`);

      // Note: We can't easily test the actual processing without setting up
      // full authentication and email access, but we can verify the trigger works
      
    } catch (error) {
      this.addResult('Email Analysis Function Trigger', false, 
        `Failed to publish test message: ${error.message}`);
    }
  }

  /**
   * Test 5: Verify environment configuration
   */
  async testEnvironmentConfiguration() {
    this.log('Testing environment configuration...', 'info');

    // Test that we can access the project
    try {
      const [topics] = await this.pubsub.getTopics();
      this.addResult('Project Access', true, 
        `Successfully accessed project with ${topics.length} topics`);
    } catch (error) {
      this.addResult('Project Access', false, 
        `Failed to access project: ${error.message}`);
    }

    // Test that required APIs are enabled by trying to use them
    try {
      const topic = this.pubsub.topic('email-analysis-requests');
      await topic.getMetadata();
      this.addResult('Pub/Sub API', true, 'Pub/Sub API is accessible');
    } catch (error) {
      this.addResult('Pub/Sub API', false, 
        `Pub/Sub API error: ${error.message}`);
    }
  }

  /**
   * Helper method to make HTTP requests
   */
  makeHttpRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: options.headers || {}
      };

      const req = https.request(requestOptions, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (options.data) {
        req.write(options.data);
      }

      req.end();
    });
  }

  /**
   * Run all tests and generate report
   */
  async runAllTests() {
    this.log('Starting Cloud Functions Production Tests...', 'info');
    this.log('='.repeat(60), 'info');

    await this.testCloudFunctionsDeployment();
    await this.testPubSubTopics();
    await this.testNotificationHandler();
    await this.testEmailAnalysisFunction();
    await this.testEnvironmentConfiguration();

    this.generateReport();
  }

  generateReport() {
    this.log('='.repeat(60), 'info');
    this.log('CLOUD FUNCTIONS TEST REPORT', 'info');
    this.log('='.repeat(60), 'info');

    this.log(`✅ Tests Passed: ${this.results.passed}`, 'success');
    this.log(`❌ Tests Failed: ${this.results.failed}`, 'error');

    if (this.results.failed > 0) {
      this.log('\nFAILED TESTS:', 'error');
      this.results.tests
        .filter(test => !test.passed)
        .forEach(test => {
          this.log(`  - ${test.test}: ${test.message}`, 'error');
        });
    }

    const overallStatus = this.results.failed === 0 ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED';
    this.log(`\nOVERALL STATUS: ${overallStatus}`, 
      this.results.failed === 0 ? 'success' : 'error');

    if (this.results.failed === 0) {
      this.log('\n🎉 Cloud Functions are deployed and working correctly!', 'success');
      this.log('✅ Production implementations are functioning as expected', 'success');
    } else {
      this.log('\n⚠️ Some issues found with Cloud Functions deployment', 'warning');
      this.log('Please review the failed tests above', 'warning');
    }

    return this.results.failed === 0;
  }
}

// Run the validation if this script is executed directly
if (require.main === module) {
  const validator = new CloudFunctionsValidator();
  validator.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Cloud Functions validation failed:', error);
    process.exit(1);
  });
}

module.exports = CloudFunctionsValidator;
