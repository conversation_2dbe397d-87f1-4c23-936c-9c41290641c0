#!/usr/bin/env node

/**
 * Deployment Readiness Test Suite
 * 
 * This script validates that the application is ready for deployment
 * by testing all critical production implementations.
 */

const fs = require('fs');
const path = require('path');

class DeploymentReadinessValidator {
  constructor() {
    this.results = {
      critical: { passed: 0, failed: 0, tests: [] },
      important: { passed: 0, failed: 0, tests: [] },
      optional: { passed: 0, failed: 0, tests: [] }
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'error': '❌',
      'warning': '⚠️',
      'success': '✅',
      'info': 'ℹ️'
    }[type] || 'ℹ️';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  addResult(category, test, passed, message) {
    this.results[category].tests.push({ test, passed, message });
    if (passed) {
      this.results[category].passed++;
      this.log(`PASS: ${test} - ${message}`, 'success');
    } else {
      this.results[category].failed++;
      this.log(`FAIL: ${test} - ${message}`, 'error');
    }
  }

  /**
   * CRITICAL: Test that all core services are production-ready
   */
  testCoreServices() {
    this.log('Testing Core Services (CRITICAL)...', 'info');

    // Test 1: Authentication Service
    const authPath = 'apps/webapp/src/lib/auth.ts';
    if (fs.existsSync(authPath)) {
      const content = fs.readFileSync(authPath, 'utf8');
      const hasRealClerkIntegration = content.includes('createClerkClient') && 
                                     content.includes('clerkClient.users.getUser');
      this.addResult('critical', 'Authentication Service', hasRealClerkIntegration,
        hasRealClerkIntegration ? 'Real Clerk API integration implemented' : 'Missing real Clerk integration');
    } else {
      this.addResult('critical', 'Authentication Service', false, 'Auth service file not found');
    }

    // Test 2: Token Service
    const tokenServicePath = 'libs/services/src/lib/token.service.ts';
    if (fs.existsSync(tokenServicePath)) {
      const content = fs.readFileSync(tokenServicePath, 'utf8');
      const hasConfigIntegration = content.includes('ConfigService');
      this.addResult('critical', 'Token Service', hasConfigIntegration,
        hasConfigIntegration ? 'ConfigService integration implemented' : 'Missing ConfigService integration');
    } else {
      this.addResult('critical', 'Token Service', false, 'Token service file not found');
    }

    // Test 3: Email Processing Service
    const emailServicePath = 'libs/services/src/lib/email.service.ts';
    if (fs.existsSync(emailServicePath)) {
      const content = fs.readFileSync(emailServicePath, 'utf8');
      const hasRealProcessing = content.includes('openAIService.analyzeEmail') && 
                               content.includes('gmailService.getEmailContent') &&
                               !content.includes('simplified') &&
                               !content.includes('placeholder');
      this.addResult('critical', 'Email Processing', hasRealProcessing,
        hasRealProcessing ? 'Real email processing implemented' : 'Still has simplified/placeholder code');
    } else {
      this.addResult('critical', 'Email Processing', false, 'Email service file not found');
    }

    // Test 4: Gmail History Processing
    const notificationPath = 'apps/notification-handler-function/src/main.ts';
    if (fs.existsSync(notificationPath)) {
      const content = fs.readFileSync(notificationPath, 'utf8');
      const hasRealHistoryProcessing = content.includes('getHistory') && 
                                      content.includes('messagesAdded') &&
                                      !content.includes('TODO: Process Gmail history');
      this.addResult('critical', 'Gmail History Processing', hasRealHistoryProcessing,
        hasRealHistoryProcessing ? 'Real history processing implemented' : 'Still has TODO or missing implementation');
    } else {
      this.addResult('critical', 'Gmail History Processing', false, 'Notification handler not found');
    }
  }

  /**
   * IMPORTANT: Test configuration and infrastructure
   */
  testConfigurationAndInfrastructure() {
    this.log('Testing Configuration and Infrastructure (IMPORTANT)...', 'info');

    // Test 1: ConfigService Implementation
    const configPath = 'libs/services/src/lib/config.ts';
    if (fs.existsSync(configPath)) {
      const content = fs.readFileSync(configPath, 'utf8');
      const requiredMethods = [
        'getUserTokenLimit', 'getInitialTokenAllocation', 'getWebappProgressWebhookUrl',
        'getMaxEmailsPerBatch', 'getOpenAIConfig', 'getDatabaseConfig'
      ];
      const hasAllMethods = requiredMethods.every(method => content.includes(method));
      const hasSingleton = content.includes('getInstance()');
      
      this.addResult('important', 'ConfigService', hasAllMethods && hasSingleton,
        hasAllMethods && hasSingleton ? 'Complete ConfigService implementation' : 'Missing methods or singleton pattern');
    } else {
      this.addResult('important', 'ConfigService', false, 'ConfigService file not found');
    }

    // Test 2: No Hardcoded Values in API Routes
    const apiRoutes = [
      'apps/webapp/src/app/api/tokens/route.ts',
      'apps/webapp/src/app/api/emails/estimate/route.ts'
    ];

    let hasHardcodedValues = false;
    for (const route of apiRoutes) {
      if (fs.existsSync(route)) {
        const content = fs.readFileSync(route, 'utf8');
        if (content.includes('5000') && !content.includes('ConfigService')) {
          hasHardcodedValues = true;
          break;
        }
      }
    }

    this.addResult('important', 'Configuration Management', !hasHardcodedValues,
      !hasHardcodedValues ? 'No hardcoded values in API routes' : 'Found hardcoded values in API routes');

    // Test 3: Socket.IO Implementation
    const socketServerPath = 'apps/webapp/src/lib/socketServer.ts';
    if (fs.existsSync(socketServerPath)) {
      const content = fs.readFileSync(socketServerPath, 'utf8');
      const hasRealSocketIO = content.includes('SocketIOServer') &&
                             content.includes('verifyToken') &&
                             content.includes('io.use(async (socket, next)');
      this.addResult('important', 'Socket.IO Server', hasRealSocketIO,
        hasRealSocketIO ? 'Real Socket.IO server implemented' : 'Missing real Socket.IO implementation');
    } else {
      this.addResult('important', 'Socket.IO Server', false, 'Socket.IO server file not found');
    }
  }

  /**
   * OPTIONAL: Test additional features and optimizations
   */
  testAdditionalFeatures() {
    this.log('Testing Additional Features (OPTIONAL)...', 'info');

    // Test 1: Progress Route Authentication
    const progressRoutePath = 'apps/webapp/src/app/api/emails/progress/route.ts';
    if (fs.existsSync(progressRoutePath)) {
      const content = fs.readFileSync(progressRoutePath, 'utf8');
      const hasRealAuth = content.includes('verifyToken') && 
                         !content.includes('user_from_token');
      this.addResult('optional', 'Progress Route Auth', hasRealAuth,
        hasRealAuth ? 'Real token verification implemented' : 'Still has placeholder authentication');
    } else {
      this.addResult('optional', 'Progress Route Auth', false, 'Progress route not found');
    }

    // Test 2: Estimate Route Accuracy
    const estimateRoutePath = 'apps/webapp/src/app/api/emails/estimate/route.ts';
    if (fs.existsSync(estimateRoutePath)) {
      const content = fs.readFileSync(estimateRoutePath, 'utf8');
      const hasAccurateEstimation = content.includes('analysisDoc.exists') &&
                                   content.includes('emailsNeedingTokens++');
      this.addResult('optional', 'Estimate Accuracy', hasAccurateEstimation,
        hasAccurateEstimation ? 'Accurate token estimation implemented' : 'Still using simplified estimation');
    } else {
      this.addResult('optional', 'Estimate Accuracy', false, 'Estimate route not found');
    }

    // Test 3: Error Handling Quality
    const tokenRoutePath = 'apps/webapp/src/app/api/tokens/route.ts';
    if (fs.existsSync(tokenRoutePath)) {
      const content = fs.readFileSync(tokenRoutePath, 'utf8');
      const hasGoodErrorHandling = content.includes('status: 503') &&
                                  !content.includes('mock') &&
                                  content.includes('error instanceof Error');
      this.addResult('optional', 'Error Handling', hasGoodErrorHandling,
        hasGoodErrorHandling ? 'Proper error handling implemented' : 'Error handling needs improvement');
    } else {
      this.addResult('optional', 'Error Handling', false, 'Token route not found');
    }
  }

  /**
   * Test build and deployment readiness
   */
  testBuildReadiness() {
    this.log('Testing Build Readiness...', 'info');

    // Test 1: Required files exist
    const requiredFiles = [
      'package.json',
      'nx.json',
      'tsconfig.base.json',
      'firebase.json',
      '.firebaserc'
    ];

    let allFilesExist = true;
    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        allFilesExist = false;
        break;
      }
    }

    this.addResult('critical', 'Required Files', allFilesExist,
      allFilesExist ? 'All required build files present' : 'Missing required build files');

    // Test 2: No temporary files
    const tempFiles = [
      'libs/email-core/src/lib/shared-utils.ts'
    ];

    let noTempFiles = true;
    for (const file of tempFiles) {
      if (fs.existsSync(file)) {
        noTempFiles = false;
        break;
      }
    }

    this.addResult('important', 'Clean Codebase', noTempFiles,
      noTempFiles ? 'No temporary files found' : 'Temporary files still present');
  }

  /**
   * Run all tests and generate deployment report
   */
  async runAllTests() {
    this.log('Starting Deployment Readiness Validation...', 'info');
    this.log('='.repeat(60), 'info');

    this.testCoreServices();
    this.testConfigurationAndInfrastructure();
    this.testAdditionalFeatures();
    this.testBuildReadiness();

    this.generateDeploymentReport();
  }

  generateDeploymentReport() {
    this.log('='.repeat(60), 'info');
    this.log('DEPLOYMENT READINESS REPORT', 'info');
    this.log('='.repeat(60), 'info');

    const categories = ['critical', 'important', 'optional'];
    let overallReady = true;

    for (const category of categories) {
      const result = this.results[category];
      const total = result.passed + result.failed;
      const percentage = total > 0 ? Math.round((result.passed / total) * 100) : 0;
      
      this.log(`${category.toUpperCase()}: ${result.passed}/${total} (${percentage}%)`, 
        result.failed === 0 ? 'success' : 'error');

      if (category === 'critical' && result.failed > 0) {
        overallReady = false;
      }
    }

    this.log('', 'info');

    // Show failed critical tests
    const criticalFailures = this.results.critical.tests.filter(t => !t.passed);
    if (criticalFailures.length > 0) {
      this.log('CRITICAL FAILURES (MUST FIX BEFORE DEPLOYMENT):', 'error');
      criticalFailures.forEach(test => {
        this.log(`  - ${test.test}: ${test.message}`, 'error');
      });
      this.log('', 'info');
    }

    // Deployment recommendation
    if (overallReady) {
      this.log('🚀 READY FOR DEPLOYMENT', 'success');
      this.log('All critical tests passed. The application is ready for production deployment.', 'success');
    } else {
      this.log('🛑 NOT READY FOR DEPLOYMENT', 'error');
      this.log('Critical issues must be resolved before deployment.', 'error');
    }

    return overallReady;
  }
}

// Run the validation if this script is executed directly
if (require.main === module) {
  const validator = new DeploymentReadinessValidator();
  validator.runAllTests().then(ready => {
    process.exit(ready ? 0 : 1);
  }).catch(error => {
    console.error('Deployment validation failed:', error);
    process.exit(1);
  });
}

module.exports = DeploymentReadinessValidator;
