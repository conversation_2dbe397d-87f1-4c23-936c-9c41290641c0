#!/usr/bin/env node

/**
 * Comprehensive Test Suite - Tests everything we can without requiring emulators
 * This works with Node.js 18 and focuses on build integrity and code structure
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  skipped: 0,
  errors: []
};

const logTest = (testName, status, details = '') => {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : status === 'SKIP' ? '⏭️' : '🔄';
  console.log(`${statusIcon} [${timestamp}] ${testName}: ${status}`);
  if (details) console.log(`   ${details}`);

  if (status === 'PASS') testResults.passed++;
  else if (status === 'FAIL') {
    testResults.failed++;
    testResults.errors.push({ test: testName, details });
  } else if (status === 'SKIP') testResults.skipped++;
};

class ComprehensiveTests {
  static async runAll() {
    console.log('🧪 Comprehensive Test Suite for Cloud Functions Architecture');
    console.log('============================================================');
    console.log('Testing build integrity, code structure, and configuration');
    console.log('');

    const startTime = Date.now();

    try {
      await this.testProjectStructure();
      await this.testBuildSystem();
      await this.testLibraryIntegration();
      await this.testCloudFunctionStructure();
      await this.testConfigurationFiles();
      await this.testCodeQuality();
      await this.testDependencies();
      await this.testDeploymentReadiness();
    } catch (error) {
      console.error('❌ Test suite encountered an error:', error);
    }

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    this.printSummary(duration);
    return testResults.failed === 0;
  }

  static async testProjectStructure() {
    console.log('\n📁 Testing Project Structure...');

    // Test Nx workspace structure
    const nxProjects = [
      'libs/shared',
      'libs/email-core',
      'apps/email-analysis-function',
      'apps/notification-handler-function',
      'apps/webapp'
    ];

    let allProjectsExist = true;
    nxProjects.forEach(project => {
      if (fs.existsSync(project)) {
        logTest(`Project Structure: ${project}`, 'PASS');
      } else {
        logTest(`Project Structure: ${project}`, 'FAIL', 'Directory missing');
        allProjectsExist = false;
      }
    });

    // Test project.json files
    nxProjects.forEach(project => {
      const projectJson = path.join(project, 'project.json');
      if (fs.existsSync(projectJson)) {
        try {
          const config = JSON.parse(fs.readFileSync(projectJson, 'utf8'));
          if (config.targets) {
            logTest(`Project Config: ${project}`, 'PASS');
          } else {
            logTest(`Project Config: ${project}`, 'FAIL', 'Missing targets');
          }
        } catch (error) {
          logTest(`Project Config: ${project}`, 'FAIL', 'Invalid JSON');
        }
      } else {
        logTest(`Project Config: ${project}`, 'FAIL', 'project.json missing');
      }
    });

    return allProjectsExist;
  }

  static async testBuildSystem() {
    console.log('\n🔨 Testing Build System...');

    // Test that all projects can build
    const buildTargets = [
      'shared',
      'email-core',
      'email-analysis-function',
      'notification-handler-function'
    ];

    for (const target of buildTargets) {
      try {
        const { stdout, stderr } = await execAsync(`npx nx build ${target}`, { timeout: 30000 });
        logTest(`Build: ${target}`, 'PASS');
      } catch (error) {
        logTest(`Build: ${target}`, 'FAIL', error.message.split('\n')[0]);
      }
    }

    // Test build outputs exist
    const expectedOutputs = [
      'dist/libs/shared/src/index.js',
      'dist/libs/email-core/src/index.js',
      'dist/apps/email-analysis-function/main.js',
      'dist/apps/notification-handler-function/main.js'
    ];

    expectedOutputs.forEach(output => {
      if (fs.existsSync(output)) {
        logTest(`Build Output: ${path.dirname(output)}`, 'PASS');
      } else {
        logTest(`Build Output: ${path.dirname(output)}`, 'FAIL', 'Output file missing');
      }
    });
  }

  static async testLibraryIntegration() {
    console.log('\n📚 Testing Library Integration...');

    // Test shared library exports
    try {
      const sharedIndexPath = 'dist/libs/shared/src/index.js';
      if (fs.existsSync(sharedIndexPath)) {
        const sharedLib = require(path.resolve(sharedIndexPath));
        const expectedExports = ['Logger', 'Database', 'sanitizeId', 'sanitizeEmail'];

        const missingExports = expectedExports.filter(exp => !sharedLib[exp]);
        if (missingExports.length === 0) {
          logTest('Shared Library Exports', 'PASS');
        } else {
          logTest('Shared Library Exports', 'FAIL', `Missing: ${missingExports.join(', ')}`);
        }
      } else {
        logTest('Shared Library Exports', 'FAIL', 'Build output not found');
      }
    } catch (error) {
      logTest('Shared Library Exports', 'FAIL', error.message);
    }

    // Test email-core library exports
    try {
      const emailCoreIndexPath = 'dist/libs/email-core/src/index.js';
      if (fs.existsSync(emailCoreIndexPath)) {
        const emailCoreLib = require(path.resolve(emailCoreIndexPath));
        const expectedExports = ['EmailAnalyzer', 'OpenAIService'];

        const missingExports = expectedExports.filter(exp => !emailCoreLib[exp]);
        if (missingExports.length === 0) {
          logTest('Email Core Library Exports', 'PASS');
        } else {
          logTest('Email Core Library Exports', 'FAIL', `Missing: ${missingExports.join(', ')}`);
        }
      } else {
        logTest('Email Core Library Exports', 'FAIL', 'Build output not found');
      }
    } catch (error) {
      logTest('Email Core Library Exports', 'FAIL', error.message);
    }

    // Test library imports in functions
    const emailFunctionPath = 'dist/apps/email-analysis-function/apps/email-analysis-function/src/main.js';
    if (fs.existsSync(emailFunctionPath)) {
      const functionCode = fs.readFileSync(emailFunctionPath, 'utf8');
      if (functionCode.includes('EmailAnalyzer') || functionCode.includes('import_src.EmailAnalyzer')) {
        logTest('Function Library Imports', 'PASS');
      } else {
        logTest('Function Library Imports', 'FAIL', 'EmailAnalyzer not found in function');
      }
    } else {
      logTest('Function Library Imports', 'FAIL', 'Function build output not found');
    }
  }

  static async testCloudFunctionStructure() {
    console.log('\n☁️ Testing Cloud Function Structure...');

    // Test email analysis function
    const emailFunctionSrc = 'apps/email-analysis-function/src/main.ts';
    if (fs.existsSync(emailFunctionSrc)) {
      const content = fs.readFileSync(emailFunctionSrc, 'utf8');

      const requiredImports = [
        'onMessagePublished',
        'EmailAnalyzer'
      ];

      const missingImports = requiredImports.filter(imp => !content.includes(imp));
      if (missingImports.length === 0) {
        logTest('Email Analysis Function Structure', 'PASS');
      } else {
        logTest('Email Analysis Function Structure', 'FAIL', `Missing: ${missingImports.join(', ')}`);
      }
    } else {
      logTest('Email Analysis Function Structure', 'FAIL', 'Source file not found');
    }

    // Test notification handler function
    const notificationFunctionSrc = 'apps/notification-handler-function/src/main.ts';
    if (fs.existsSync(notificationFunctionSrc)) {
      const content = fs.readFileSync(notificationFunctionSrc, 'utf8');

      const requiredImports = [
        'onRequest',
        'PubSub',
        'Database'
      ];

      const missingImports = requiredImports.filter(imp => !content.includes(imp));
      if (missingImports.length === 0) {
        logTest('Notification Handler Function Structure', 'PASS');
      } else {
        logTest('Notification Handler Function Structure', 'FAIL', `Missing: ${missingImports.join(', ')}`);
      }
    } else {
      logTest('Notification Handler Function Structure', 'FAIL', 'Source file not found');
    }

    // Test function exports
    const emailFunctionBuilt = 'dist/apps/email-analysis-function/apps/email-analysis-function/src/main.js';
    if (fs.existsSync(emailFunctionBuilt)) {
      const content = fs.readFileSync(emailFunctionBuilt, 'utf8');
      if (content.includes('analyzeEmail: () => analyzeEmail') || content.includes('exports.analyzeEmail')) {
        logTest('Email Analysis Function Export', 'PASS');
      } else {
        logTest('Email Analysis Function Export', 'FAIL', 'analyzeEmail export not found');
      }
    }

    const notificationFunctionBuilt = 'dist/apps/notification-handler-function/apps/notification-handler-function/src/main.js';
    if (fs.existsSync(notificationFunctionBuilt)) {
      const content = fs.readFileSync(notificationFunctionBuilt, 'utf8');
      if (content.includes('handleGmailNotification: () => handleGmailNotification') || content.includes('exports.handleGmailNotification')) {
        logTest('Notification Handler Function Export', 'PASS');
      } else {
        logTest('Notification Handler Function Export', 'FAIL', 'handleGmailNotification export not found');
      }
    }
  }

  static async testConfigurationFiles() {
    console.log('\n⚙️ Testing Configuration Files...');

    // Test Firebase configuration
    try {
      const firebaseConfig = JSON.parse(fs.readFileSync('firebase.json', 'utf8'));

      if (firebaseConfig.functions && Array.isArray(firebaseConfig.functions)) {
        logTest('Firebase Functions Config', 'PASS');
      } else {
        logTest('Firebase Functions Config', 'FAIL', 'Invalid functions configuration');
      }

      if (firebaseConfig.emulators) {
        logTest('Firebase Emulators Config', 'PASS');
      } else {
        logTest('Firebase Emulators Config', 'FAIL', 'Missing emulators configuration');
      }
    } catch (error) {
      logTest('Firebase Configuration', 'FAIL', error.message);
    }

    // Test Nx configuration
    try {
      const nxConfig = JSON.parse(fs.readFileSync('nx.json', 'utf8'));

      if (nxConfig.targetDefaults && nxConfig.projects) {
        logTest('Nx Configuration', 'PASS');
      } else {
        logTest('Nx Configuration', 'FAIL', 'Missing required properties');
      }
    } catch (error) {
      logTest('Nx Configuration', 'FAIL', error.message);
    }

    // Test TypeScript configuration
    try {
      const tsconfigBase = JSON.parse(fs.readFileSync('tsconfig.base.json', 'utf8'));

      if (tsconfigBase.compilerOptions && tsconfigBase.compilerOptions.paths) {
        logTest('TypeScript Configuration', 'PASS');
      } else {
        logTest('TypeScript Configuration', 'FAIL', 'Missing path mapping');
      }
    } catch (error) {
      logTest('TypeScript Configuration', 'FAIL', error.message);
    }
  }

  static async testCodeQuality() {
    console.log('\n🔍 Testing Code Quality...');

    // Test for proper error handling in functions
    const emailFunctionSrc = 'apps/email-analysis-function/src/main.ts';
    if (fs.existsSync(emailFunctionSrc)) {
      const content = fs.readFileSync(emailFunctionSrc, 'utf8');

      if (content.includes('try') && content.includes('catch')) {
        logTest('Error Handling: Email Function', 'PASS');
      } else {
        logTest('Error Handling: Email Function', 'FAIL', 'Missing try-catch blocks');
      }
    }

    // Test for logging in functions
    const notificationFunctionSrc = 'apps/notification-handler-function/src/main.ts';
    if (fs.existsSync(notificationFunctionSrc)) {
      const content = fs.readFileSync(notificationFunctionSrc, 'utf8');

      if (content.includes('logger') || content.includes('console.log')) {
        logTest('Logging: Notification Function', 'PASS');
      } else {
        logTest('Logging: Notification Function', 'FAIL', 'No logging found');
      }
    }

    // Test for TypeScript strict mode
    const libTsConfigs = [
      'libs/shared/tsconfig.json',
      'libs/email-core/tsconfig.json'
    ];

    libTsConfigs.forEach(configPath => {
      if (fs.existsSync(configPath)) {
        try {
          const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
          logTest(`TypeScript Config: ${path.dirname(configPath)}`, 'PASS');
        } catch (error) {
          logTest(`TypeScript Config: ${path.dirname(configPath)}`, 'FAIL', 'Invalid JSON');
        }
      }
    });
  }

  static async testDependencies() {
    console.log('\n📦 Testing Dependencies...');

    // Test package.json
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

      const requiredDeps = [
        'firebase-functions',
        'firebase-admin',
        '@google-cloud/pubsub',
        'openai',
        'zod'
      ];

      const missingDeps = requiredDeps.filter(dep =>
        !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
      );

      if (missingDeps.length === 0) {
        logTest('Required Dependencies', 'PASS');
      } else {
        logTest('Required Dependencies', 'FAIL', `Missing: ${missingDeps.join(', ')}`);
      }
    } catch (error) {
      logTest('Package.json', 'FAIL', error.message);
    }

    // Test node_modules
    const criticalModules = [
      'node_modules/firebase-functions',
      'node_modules/firebase-admin',
      'node_modules/@google-cloud/pubsub'
    ];

    criticalModules.forEach(module => {
      if (fs.existsSync(module)) {
        logTest(`Module: ${path.basename(module)}`, 'PASS');
      } else {
        logTest(`Module: ${path.basename(module)}`, 'FAIL', 'Not installed');
      }
    });
  }

  static async testDeploymentReadiness() {
    console.log('\n🚀 Testing Deployment Readiness...');

    // Test that function build outputs have package.json
    const functionDirs = [
      'dist/apps/email-analysis-function',
      'dist/apps/notification-handler-function'
    ];

    functionDirs.forEach(dir => {
      const packageJsonPath = path.join(dir, 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        logTest(`Deployment Package: ${path.basename(dir)}`, 'PASS');
      } else {
        logTest(`Deployment Package: ${path.basename(dir)}`, 'FAIL', 'package.json missing');
      }
    });

    // Test Firebase project configuration
    if (fs.existsSync('.firebaserc')) {
      try {
        const firebaserc = JSON.parse(fs.readFileSync('.firebaserc', 'utf8'));
        if (firebaserc.projects && firebaserc.projects.default) {
          logTest('Firebase Project Config', 'PASS');
        } else {
          logTest('Firebase Project Config', 'FAIL', 'Missing default project');
        }
      } catch (error) {
        logTest('Firebase Project Config', 'FAIL', error.message);
      }
    } else {
      logTest('Firebase Project Config', 'FAIL', '.firebaserc missing');
    }

    // Test deployment scripts
    const deploymentScripts = [
      'scripts/deploy-functions.sh',
      'scripts/test-locally.sh'
    ];

    deploymentScripts.forEach(script => {
      if (fs.existsSync(script)) {
        logTest(`Deployment Script: ${path.basename(script)}`, 'PASS');
      } else {
        logTest(`Deployment Script: ${path.basename(script)}`, 'FAIL', 'Script missing');
      }
    });
  }

  static printSummary(duration) {
    console.log('\n📊 Test Results Summary');
    console.log('=======================');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⏭️ Skipped: ${testResults.skipped}`);
    console.log(`⏱️ Duration: ${duration}s`);

    if (testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      testResults.errors.forEach(error => {
        console.log(`   • ${error.test}: ${error.details}`);
      });
    }

    const total = testResults.passed + testResults.failed;
    const successRate = total > 0 ? ((testResults.passed / total) * 100).toFixed(1) : 0;
    console.log(`\n📈 Success Rate: ${successRate}%`);

    if (testResults.failed === 0) {
      console.log('\n🎉 All tests passed! The Cloud Functions architecture is ready for deployment.');
      console.log('\n✅ Architecture Status:');
      console.log('   • All projects build successfully');
      console.log('   • Libraries are properly integrated');
      console.log('   • Cloud Functions have correct structure');
      console.log('   • Configuration files are valid');
      console.log('   • Dependencies are installed');
      console.log('   • Deployment files are ready');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the errors above before deployment.');
    }

    console.log('\n🔧 Next Steps:');
    if (testResults.failed === 0) {
      console.log('   1. Start emulators: ./scripts/test-locally.sh');
      console.log('   2. Test locally: node scripts/test-functions.js');
      console.log('   3. Deploy to staging: firebase deploy --only functions');
    } else {
      console.log('   1. Fix the failed tests above');
      console.log('   2. Re-run tests: node tests/comprehensive-test.js');
      console.log('   3. Proceed with local testing once all tests pass');
    }
  }
}

// Run if called directly
if (require.main === module) {
  ComprehensiveTests.runAll()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

module.exports = ComprehensiveTests;
