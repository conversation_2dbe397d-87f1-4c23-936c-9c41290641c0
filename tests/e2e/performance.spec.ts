import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test('analysis page should load within performance budget', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Page should load within 3 seconds (allowing for network latency in tests)
    expect(loadTime).toBeLessThan(3000);
    console.log(`Analysis page load time: ${loadTime}ms`);
  });

  test('table should render large dataset efficiently', async ({ page }) => {
    // Mock API to return large dataset
    await page.route('/api/analysis-runs*', async route => {
      const jobs = Array.from({ length: 100 }, (_, i) => ({
        jobId: `job-${i}`,
        sourceType: i % 2 === 0 ? 'manual' : 'automatic',
        status: ['completed', 'processing', 'error'][i % 3],
        totalEmails: Math.floor(Math.random() * 1000) + 100,
        cachedEmails: Math.floor(Math.random() * 50),
        pendingEmails: Math.floor(Math.random() * 20),
        successfullyAnalyzed: Math.floor(Math.random() * 900) + 100,
        emailDateRange: {
          start: '2024-01-01',
          end: '2024-01-31'
        },
        analysisStartTime: new Date(Date.now() - Math.random() * 86400000).toISOString(),
        lastUpdated: new Date(Date.now() - Math.random() * 3600000).toISOString(),
        currentStatus: 'Analysis completed successfully'
      }));
      
      await route.fulfill({
        json: {
          analysisRuns: jobs,
          pagination: {
            currentPage: 1,
            totalPages: 5,
            totalRuns: 500,
            hasNextPage: true,
            hasPreviousPage: false,
            pageSize: 100
          },
          filters: {
            sortBy: 'analysisStartTime',
            sortOrder: 'desc'
          }
        }
      });
    });
    
    const startTime = Date.now();
    await page.goto('/analysis');
    await page.waitForSelector('[data-testid="analysis-runs-table"]');
    
    const renderTime = Date.now() - startTime;
    
    // Table should render within 1 second even with large dataset
    expect(renderTime).toBeLessThan(1000);
    console.log(`Large table render time: ${renderTime}ms`);
    
    // Check that all rows are rendered
    const rows = page.locator('tbody tr');
    const rowCount = await rows.count();
    expect(rowCount).toBeGreaterThan(50); // Should have rendered many rows
  });

  test('filtering should be responsive', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    const startTime = Date.now();
    
    // Apply filter
    await page.selectOption('[data-testid="source-type-filter"]', 'manual');
    
    // Wait for filter to apply
    await page.waitForTimeout(100);
    
    const filterTime = Date.now() - startTime;
    
    // Filtering should be nearly instantaneous
    expect(filterTime).toBeLessThan(500);
    console.log(`Filter application time: ${filterTime}ms`);
  });

  test('pagination should be smooth', async ({ page }) => {
    // Mock paginated data
    await page.route('/api/analysis-runs*', async route => {
      const url = new URL(route.request().url());
      const page_num = url.searchParams.get('page') || '1';
      
      const jobs = Array.from({ length: 20 }, (_, i) => ({
        jobId: `job-page${page_num}-${i}`,
        sourceType: 'manual',
        status: 'completed',
        totalEmails: 100,
        cachedEmails: 20,
        pendingEmails: 0,
        successfullyAnalyzed: 100,
        emailDateRange: {
          start: '2024-01-01',
          end: '2024-01-31'
        },
        analysisStartTime: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        currentStatus: 'Analysis completed successfully'
      }));
      
      await route.fulfill({
        json: {
          analysisRuns: jobs,
          pagination: {
            currentPage: parseInt(page_num),
            totalPages: 5,
            totalRuns: 100,
            hasNextPage: parseInt(page_num) < 5,
            hasPreviousPage: parseInt(page_num) > 1,
            pageSize: 20
          },
          filters: {
            sortBy: 'analysisStartTime',
            sortOrder: 'desc'
          }
        }
      });
    });

    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    // Test pagination performance
    const nextButton = page.locator('[data-testid="next-page"]');
    if (await nextButton.isVisible() && await nextButton.isEnabled()) {
      const startTime = Date.now();
      
      await nextButton.click();
      await page.waitForSelector('[data-testid="current-page"]');
      
      const paginationTime = Date.now() - startTime;
      
      // Pagination should be fast
      expect(paginationTime).toBeLessThan(1000);
      console.log(`Pagination time: ${paginationTime}ms`);
    }
  });

  test('real-time updates should not impact performance', async ({ page }) => {
    // Mock processing job for real-time updates
    await page.route('/api/analysis-runs*', async route => {
      await route.fulfill({
        json: {
          analysisRuns: [{
            jobId: 'processing-job',
            sourceType: 'manual',
            status: 'processing',
            totalEmails: 1000,
            cachedEmails: 200,
            pendingEmails: 300,
            successfullyAnalyzed: 700,
            emailDateRange: {
              start: '2024-01-01',
              end: '2024-01-31'
            },
            analysisStartTime: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            currentStatus: 'Processing email 700 of 1000',
            progress: { percentage: 70 }
          }],
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalRuns: 1,
            hasNextPage: false,
            hasPreviousPage: false,
            pageSize: 20
          },
          filters: {
            sortBy: 'analysisStartTime',
            sortOrder: 'desc'
          }
        }
      });
    });

    // Mock job status updates
    let updateCount = 0;
    await page.route('/api/jobs/processing-job/status', async route => {
      updateCount++;
      await route.fulfill({
        json: {
          jobId: 'processing-job',
          status: 'processing',
          progress: {
            percentage: 70 + updateCount,
            processed: 700 + updateCount * 10,
            total: 1000,
            pendingEmails: 300 - updateCount * 10
          }
        }
      });
    });

    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    // Measure performance during real-time updates
    const startTime = Date.now();
    
    // Wait for several polling cycles
    await page.waitForTimeout(12000);
    
    const totalTime = Date.now() - startTime;
    
    // Page should remain responsive during polling
    const refreshButton = page.locator('button', { hasText: 'Refresh' });
    const clickStartTime = Date.now();
    await refreshButton.click();
    const clickTime = Date.now() - clickStartTime;
    
    // UI should remain responsive (click should be fast)
    expect(clickTime).toBeLessThan(200);
    console.log(`UI responsiveness during polling: ${clickTime}ms`);
    console.log(`Total polling updates: ${updateCount}`);
  });

  test('memory usage should be stable', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    // Get initial memory usage
    const initialMetrics = await page.evaluate(() => {
      return {
        usedJSHeapSize: (performance as any).memory?.usedJSHeapSize || 0,
        totalJSHeapSize: (performance as any).memory?.totalJSHeapSize || 0
      };
    });
    
    // Perform various operations
    for (let i = 0; i < 5; i++) {
      // Apply and clear filters
      await page.selectOption('[data-testid="source-type-filter"]', 'manual');
      await page.waitForTimeout(100);
      await page.selectOption('[data-testid="source-type-filter"]', '');
      await page.waitForTimeout(100);
      
      // Refresh data
      await page.click('button:has-text("Refresh")');
      await page.waitForTimeout(500);
    }
    
    // Get final memory usage
    const finalMetrics = await page.evaluate(() => {
      return {
        usedJSHeapSize: (performance as any).memory?.usedJSHeapSize || 0,
        totalJSHeapSize: (performance as any).memory?.totalJSHeapSize || 0
      };
    });
    
    // Memory usage should not increase dramatically
    const memoryIncrease = finalMetrics.usedJSHeapSize - initialMetrics.usedJSHeapSize;
    const memoryIncreasePercent = (memoryIncrease / initialMetrics.usedJSHeapSize) * 100;
    
    console.log(`Memory increase: ${memoryIncrease} bytes (${memoryIncreasePercent.toFixed(2)}%)`);
    
    // Memory increase should be reasonable (less than 50% increase)
    expect(memoryIncreasePercent).toBeLessThan(50);
  });
});
