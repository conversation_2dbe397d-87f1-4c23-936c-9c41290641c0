import { test, expect } from '@playwright/test';

test.describe('Email Analysis Status Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if we need to sign in (this will depend on your auth setup)
    const signInButton = page.locator('text=Sign in');
    if (await signInButton.isVisible()) {
      // Handle sign in if needed - this might need to be customized based on your auth flow
      console.log('Sign in required - this test assumes user is already authenticated');
    }
  });

  test('should display analysis page with correct title and navigation', async ({ page }) => {
    // Navigate to analysis page
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Check page title and header
    await expect(page.locator('h1')).toContainText('Email Analysis Status');
    
    // Check navigation includes Analysis link
    const analysisNavLink = page.locator('nav a[href="/analysis"]');
    await expect(analysisNavLink).toBeVisible();
    
    // Check page description
    await expect(page.locator('text=Monitor and manage your email analysis runs')).toBeVisible();
  });

  test('should display analysis runs table with correct headers', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Wait for table to load
    await page.waitForSelector('[data-testid="analysis-runs-table"]', { timeout: 10000 });

    // Check table headers are present
    const expectedHeaders = [
      'Source Type',
      'Total Emails', 
      'Cached',
      'Pending',
      'Analyzed',
      'Status',
      'Email Date Range',
      'Started'
    ];

    for (const header of expectedHeaders) {
      await expect(page.locator('th', { hasText: header })).toBeVisible();
    }
  });

  test('should display start analysis section', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Check start analysis section is present
    await expect(page.locator('h2', { hasText: 'Start New Email Analysis' })).toBeVisible();
    
    // Check date inputs are present
    await expect(page.locator('[data-testid="start-date"]')).toBeVisible();
    await expect(page.locator('[data-testid="end-date"]')).toBeVisible();
    
    // Check analyze button is present
    await expect(page.locator('[data-testid="analyze-button"]')).toBeVisible();
  });

  test('should handle date selection and enable analyze button', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Initially analyze button should be disabled
    const analyzeButton = page.locator('[data-testid="analyze-button"]');
    await expect(analyzeButton).toBeDisabled();

    // Fill in dates
    const startDate = '2024-01-01';
    const endDate = '2024-01-03';
    
    await page.fill('[data-testid="start-date"]', startDate);
    await page.fill('[data-testid="end-date"]', endDate);

    // Wait a moment for any validation/estimation to complete
    await page.waitForTimeout(1000);

    // Button should now be enabled (assuming valid dates and sufficient tokens)
    // Note: This might be disabled if there are no tokens or other validation issues
    const buttonState = await analyzeButton.isDisabled();
    console.log('Analyze button disabled state:', buttonState);
  });

  test('should display and interact with filters', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Check filter section is present
    await expect(page.locator('text=Filters')).toBeVisible();

    // Check filter dropdowns
    await expect(page.locator('[data-testid="source-type-filter"]')).toBeVisible();
    await expect(page.locator('[data-testid="status-filter"]')).toBeVisible();

    // Test source type filter
    await page.selectOption('[data-testid="source-type-filter"]', 'manual');
    
    // Check if filter badge appears
    const filterBadge = page.locator('text=Source: manual');
    if (await filterBadge.isVisible()) {
      await expect(filterBadge).toBeVisible();
    }

    // Test status filter
    await page.selectOption('[data-testid="status-filter"]', 'completed');
    
    // Test clear filters
    const clearFiltersButton = page.locator('text=Clear all');
    if (await clearFiltersButton.isVisible()) {
      await clearFiltersButton.click();
    }
  });

  test('should handle table sorting', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Wait for table to load
    await page.waitForSelector('[data-testid="analysis-runs-table"]', { timeout: 10000 });

    // Try to click on a sortable column header
    const totalEmailsHeader = page.locator('th', { hasText: 'Total Emails' });
    if (await totalEmailsHeader.isVisible()) {
      await totalEmailsHeader.click();
      
      // Wait for sort to apply
      await page.waitForTimeout(500);
      
      // Check for sort indicator (this depends on your implementation)
      // You might need to adjust this based on your actual sort indicator implementation
    }
  });

  test('should handle pagination if data is present', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Wait for table to load
    await page.waitForSelector('[data-testid="analysis-runs-table"]', { timeout: 10000 });

    // Check if pagination is present
    const pagination = page.locator('[data-testid="pagination"]');
    if (await pagination.isVisible()) {
      // Test next page button if available
      const nextButton = page.locator('[data-testid="next-page"]');
      if (await nextButton.isEnabled()) {
        await nextButton.click();
        await page.waitForTimeout(1000);
        
        // Check if page changed
        const currentPage = page.locator('[data-testid="current-page"]');
        await expect(currentPage).toBeVisible();
      }
    }
  });

  test('should display empty state when no analysis runs exist', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Wait for table to load
    await page.waitForSelector('[data-testid="analysis-runs-table"]', { timeout: 10000 });

    // Check for empty state message (this will show if user has no analysis runs)
    const emptyMessage = page.locator('text=No analysis runs found');
    if (await emptyMessage.isVisible()) {
      await expect(emptyMessage).toBeVisible();
      await expect(page.locator('text=Start your first email analysis')).toBeVisible();
    }
  });

  test('should handle refresh functionality', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Find and click refresh button
    const refreshButton = page.locator('button', { hasText: 'Refresh' });
    await expect(refreshButton).toBeVisible();
    
    await refreshButton.click();
    
    // Wait for refresh to complete
    await page.waitForTimeout(1000);
    
    // Check that refresh button is not in loading state
    await expect(refreshButton).not.toHaveClass(/animate-spin/);
  });

  test('should navigate from dashboard to analysis page', async ({ page }) => {
    // Start on dashboard
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Check for analysis link on dashboard
    const analysisLink = page.locator('a[href="/analysis"]');
    if (await analysisLink.first().isVisible()) {
      await analysisLink.first().click();
      
      // Should navigate to analysis page
      await expect(page).toHaveURL('/analysis');
      await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
    }
  });

  test('should display summary statistics when data is present', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Wait for page to fully load
    await page.waitForTimeout(2000);

    // Check if summary section is present (only shows when there are analysis runs)
    const summarySection = page.locator('h3', { hasText: 'Summary' });
    if (await summarySection.isVisible()) {
      await expect(summarySection).toBeVisible();
      
      // Check for summary stats
      await expect(page.locator('text=Completed')).toBeVisible();
      await expect(page.locator('text=Processing')).toBeVisible();
      await expect(page.locator('text=Failed')).toBeVisible();
      await expect(page.locator('text=Total Emails')).toBeVisible();
    }
  });
});

test.describe('Mobile Responsiveness', () => {
  test.use({ viewport: { width: 375, height: 667 } }); // iPhone SE size

  test('should display mobile-optimized layout', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Check that page loads and is responsive
    await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
    
    // Check that table is present (it should adapt to mobile)
    await page.waitForSelector('[data-testid="analysis-runs-table"]', { timeout: 10000 });
    
    // Check that filters are accessible on mobile
    await expect(page.locator('text=Filters')).toBeVisible();
  });

  test('should handle mobile navigation', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Check if mobile menu exists and navigate to analysis
    const mobileMenuButton = page.locator('button[aria-label="Open menu"]');
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
    }
    
    // Navigate to analysis page
    const analysisLink = page.locator('a[href="/analysis"]');
    if (await analysisLink.isVisible()) {
      await analysisLink.click();
      await expect(page).toHaveURL('/analysis');
    }
  });
});

test.describe('Error Handling', () => {
  test('should handle network errors gracefully', async ({ page }) => {
    // Intercept API calls and simulate network error
    await page.route('/api/analysis-runs*', route => {
      route.abort('failed');
    });

    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Wait for error to appear
    await page.waitForTimeout(3000);

    // Check for error message
    const errorMessage = page.locator('text=Error:');
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toBeVisible();
    }
  });

  test('should handle authentication errors', async ({ page }) => {
    // Intercept API calls and simulate auth error
    await page.route('/api/analysis-runs*', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Authentication required' })
      });
    });

    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');

    // Wait for error handling
    await page.waitForTimeout(3000);

    // Should handle auth error appropriately
    // This might redirect to sign-in or show an error message
  });
});
