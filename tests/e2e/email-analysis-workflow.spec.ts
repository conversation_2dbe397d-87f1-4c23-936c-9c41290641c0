import { test, expect } from '@playwright/test';

/**
 * End-to-End Email Analysis Workflow Tests
 * 
 * This test suite covers the complete email analysis workflow:
 * 1. Date selection and validation
 * 2. Analysis trigger and progress tracking
 * 3. Real-time updates via Socket.io
 * 4. Results display and metrics
 * 5. Error handling and recovery
 */

const WEBAPP_URL = process.env.WEBAPP_URL || 'https://webapp-4r5k3ebaga-uc.a.run.app';

test.describe('Email Analysis Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the webapp
    await page.goto(WEBAPP_URL);
    
    // Wait for authentication to complete
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
  });

  test('should load dashboard with all components', async ({ page }) => {
    // Verify dashboard elements are present
    await expect(page.locator('h1')).toContainText('Dashboard');
    await expect(page.locator('h2')).toContainText('Analyze New Emails');
    await expect(page.locator('h2')).toContainText('Metrics Overview');
    
    // Verify date inputs are present
    await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
    await expect(page.locator('input[placeholder*="End"]')).toBeVisible();
    
    // Verify analyze button is present (may be disabled initially)
    await expect(page.locator('button')).toContainText(/Analyze|Connecting/);
  });

  test('should handle date selection correctly', async ({ page }) => {
    const startDateInput = page.locator('input[placeholder*="Start"]').first();
    const endDateInput = page.locator('input[placeholder*="End"]').first();
    
    // Set test dates (May 12-13, 2025)
    await startDateInput.fill('2025-05-12');
    await endDateInput.fill('2025-05-13');
    
    // Verify date range is updated
    await expect(page.locator('text=/Date range:/')).toBeVisible();
    
    // Verify button state changes based on date selection
    const analyzeButton = page.locator('button').filter({ hasText: /Analyze|Connecting/ });
    await expect(analyzeButton).toBeVisible();
  });

  test('should show connection status and progress updates', async ({ page }) => {
    // Check for connection status indicators
    await expect(page.locator('text=/Status:|Connecting|Ready/')).toBeVisible();
    
    // Check for progress indicators
    await expect(page.locator('text=/Progress:|Waiting/')).toBeVisible();
    
    // Check for internal state display
    await expect(page.locator('text=/Internal state:/')).toBeVisible();
  });

  test('should handle Socket.io connection gracefully', async ({ page }) => {
    // Monitor console for Socket.io connection attempts
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      consoleMessages.push(msg.text());
    });
    
    // Wait for Socket.io connection attempts
    await page.waitForTimeout(3000);
    
    // Verify that Socket.io errors are handled gracefully
    const hasSocketErrors = consoleMessages.some(msg => 
      msg.includes('socket.io') || msg.includes('xhr poll error')
    );
    
    if (hasSocketErrors) {
      // Verify the UI still functions without Socket.io
      await expect(page.locator('text=/Connecting|Waiting/')).toBeVisible();
      console.log('Socket.io connection failed gracefully - UI remains functional');
    }
  });

  test('should trigger email analysis workflow', async ({ page }) => {
    const startDateInput = page.locator('input[placeholder*="Start"]').first();
    const endDateInput = page.locator('input[placeholder*="End"]').first();
    
    // Set test dates
    await startDateInput.fill('2025-05-12');
    await endDateInput.fill('2025-05-13');
    
    // Wait for any connection attempts to complete
    await page.waitForTimeout(2000);
    
    // Try to click the analyze button if it's enabled
    const analyzeButton = page.locator('button').filter({ hasText: /Analyze/ });
    
    if (await analyzeButton.isEnabled()) {
      await analyzeButton.click();
      
      // Verify analysis starts
      await expect(page.locator('text=/Analyzing|Processing|In Progress/')).toBeVisible({ timeout: 5000 });
      
      // Monitor for progress updates
      await page.waitForTimeout(5000);
      
      // Check if progress is being tracked
      const progressText = await page.locator('text=/Progress:.*\/.*-.*Status:/')
      await expect(progressText).toBeVisible();
    } else {
      console.log('Analyze button is disabled - likely due to connection issues');
      
      // Verify the button shows appropriate disabled state
      await expect(analyzeButton).toBeDisabled();
      await expect(page.locator('text=/Connecting|Waiting/')).toBeVisible();
    }
  });

  test('should display metrics overview', async ({ page }) => {
    // Verify metrics section is present
    await expect(page.locator('h2')).toContainText('Metrics Overview');
    
    // Check for date range display
    await expect(page.locator('text=/Date range:/')).toBeVisible();
    
    // Check for metric type selector
    await expect(page.locator('button')).toContainText('Applications');
    
    // Check for period selector
    await expect(page.locator('text=/Weekly|Monthly|Daily/')).toBeVisible();
  });

  test('should handle navigation between pages', async ({ page }) => {
    // Test Analytics navigation
    await page.click('a[href="/analytics"]');
    
    // Check if Analytics page loads or shows 404
    const pageContent = await page.textContent('body');
    if (pageContent?.includes('404')) {
      console.log('Analytics page not yet deployed - showing 404 as expected');
      await expect(page.locator('text=/404.*Page Not Found/')).toBeVisible();
    } else {
      await expect(page.locator('h1')).toContainText('Analytics');
    }
    
    // Test Settings navigation
    await page.click('a[href="/settings"]');
    
    const settingsContent = await page.textContent('body');
    if (settingsContent?.includes('404')) {
      console.log('Settings page not yet deployed - showing 404 as expected');
      await expect(page.locator('text=/404.*Page Not Found/')).toBeVisible();
    } else {
      await expect(page.locator('h1')).toContainText('Settings');
    }
    
    // Return to Dashboard
    await page.click('a[href="/"]');
    await expect(page.locator('h1')).toContainText('Dashboard');
  });

  test('should handle authentication correctly', async ({ page }) => {
    // Verify user is authenticated
    await expect(page.locator('img[alt*="logo"]')).toBeVisible();
    
    // Check for user menu or profile indicator
    const userButton = page.locator('button').filter({ hasText: /user|profile|menu/i });
    if (await userButton.count() > 0) {
      await expect(userButton.first()).toBeVisible();
    }
  });
});

test.describe('Error Handling and Recovery', () => {
  test('should handle API failures gracefully', async ({ page }) => {
    // Monitor network requests
    const failedRequests: string[] = [];
    page.on('response', response => {
      if (response.status() >= 400) {
        failedRequests.push(`${response.status()} ${response.url()}`);
      }
    });
    
    await page.goto(WEBAPP_URL);
    await page.waitForTimeout(3000);
    
    // Log any API failures
    if (failedRequests.length > 0) {
      console.log('API failures detected:', failedRequests);
      
      // Verify the UI still functions despite API failures
      await expect(page.locator('h1')).toContainText('Dashboard');
      await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
    }
  });

  test('should show appropriate error messages', async ({ page }) => {
    await page.goto(WEBAPP_URL);
    
    // Check for error indicators in the UI
    const errorElements = page.locator('text=/Error|Failed|Internal Server Error/');
    const errorCount = await errorElements.count();
    
    if (errorCount > 0) {
      console.log(`Found ${errorCount} error messages in UI`);
      
      // Verify errors are displayed appropriately
      for (let i = 0; i < errorCount; i++) {
        const errorText = await errorElements.nth(i).textContent();
        console.log(`Error ${i + 1}: ${errorText}`);
      }
    }
  });
});

test.describe('Performance and Responsiveness', () => {
  test('should load within acceptable time limits', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto(WEBAPP_URL);
    await page.waitForSelector('h1', { timeout: 10000 });
    
    const loadTime = Date.now() - startTime;
    console.log(`Page load time: ${loadTime}ms`);
    
    // Verify page loads within 10 seconds
    expect(loadTime).toBeLessThan(10000);
  });

  test('should be responsive on different screen sizes', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto(WEBAPP_URL);
    
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.reload();
    
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
  });
});
