import { test, expect } from '@playwright/test';

test.describe('Real-time Updates for Analysis Runs', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
  });

  test('should show active run indicators when processing jobs exist', async ({ page }) => {
    // Mock API response with processing jobs
    await page.route('/api/analysis-runs*', async route => {
      const mockResponse = {
        analysisRuns: [
          {
            jobId: 'test-job-1',
            sourceType: 'manual',
            status: 'processing',
            totalEmails: 100,
            cachedEmails: 20,
            pendingEmails: 30,
            successfullyAnalyzed: 70,
            emailDateRange: {
              start: '2024-01-01',
              end: '2024-01-03'
            },
            analysisStartTime: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            currentStatus: 'Processing email 70 of 100',
            progress: {
              percentage: 70
            }
          }
        ],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalRuns: 1,
          hasNextPage: false,
          hasPreviousPage: false,
          pageSize: 20
        },
        filters: {
          sortBy: 'analysisStartTime',
          sortOrder: 'desc'
        }
      };
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockResponse)
      });
    });

    // Reload page to get mock data
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Check for active run indicator
    const activeRunIndicator = page.locator('text=1 active run');
    if (await activeRunIndicator.isVisible()) {
      await expect(activeRunIndicator).toBeVisible();
      
      // Check for animated indicator
      const animatedDot = page.locator('.animate-pulse');
      await expect(animatedDot).toBeVisible();
    }

    // Check for processing status badge
    const processingBadge = page.locator('[data-testid="status-badge"]', { hasText: 'Processing' });
    if (await processingBadge.isVisible()) {
      await expect(processingBadge).toBeVisible();
      
      // Check for animated indicator on processing badge
      const animatedIndicator = page.locator('[data-testid="animated-indicator"]');
      if (await animatedIndicator.isVisible()) {
        await expect(animatedIndicator).toBeVisible();
      }
    }

    // Check for progress indicator
    const progressIndicator = page.locator('[data-testid="progress-indicator"]');
    if (await progressIndicator.isVisible()) {
      await expect(progressIndicator).toBeVisible();
      
      // Check progress percentage
      const progressPercentage = page.locator('[data-testid="progress-percentage"]');
      if (await progressPercentage.isVisible()) {
        await expect(progressPercentage).toContainText('70%');
      }
    }
  });

  test('should update job status in real-time', async ({ page }) => {
    let callCount = 0;
    
    // Mock initial response with processing job
    await page.route('/api/analysis-runs*', async route => {
      const mockResponse = {
        analysisRuns: [
          {
            jobId: 'test-job-1',
            sourceType: 'manual',
            status: 'processing',
            totalEmails: 100,
            cachedEmails: 20,
            pendingEmails: 20,
            successfullyAnalyzed: 80,
            emailDateRange: {
              start: '2024-01-01',
              end: '2024-01-03'
            },
            analysisStartTime: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            currentStatus: 'Processing email 80 of 100',
            progress: {
              percentage: 80
            }
          }
        ],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalRuns: 1,
          hasNextPage: false,
          hasPreviousPage: false,
          pageSize: 20
        },
        filters: {
          sortBy: 'analysisStartTime',
          sortOrder: 'desc'
        }
      };
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockResponse)
      });
    });

    // Mock job status endpoint to simulate progress updates
    await page.route('/api/jobs/test-job-1/status', async route => {
      callCount++;
      
      const mockJobStatus = {
        jobId: 'test-job-1',
        status: callCount < 3 ? 'processing' : 'completed',
        sourceType: 'manual',
        progress: {
          total: 100,
          processed: callCount < 3 ? 80 + (callCount * 5) : 100,
          current: callCount < 3 ? `Processing email ${80 + (callCount * 5)} of 100` : 'Analysis completed successfully',
          cachedCount: 20,
          newAnalysisCount: 80,
          percentage: callCount < 3 ? 80 + (callCount * 5) : 100,
          pendingEmails: callCount < 3 ? 20 - (callCount * 5) : 0
        },
        timing: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          completedAt: callCount >= 3 ? new Date().toISOString() : undefined
        },
        emailDetails: {
          dateRange: {
            start: '2024-01-01',
            end: '2024-01-03'
          },
          monitoredEmail: '<EMAIL>',
          totalEmails: 100,
          pendingEmails: callCount < 3 ? 20 - (callCount * 5) : 0
        }
      };
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockJobStatus)
      });
    });

    // Load page and wait for initial data
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Wait for real-time updates (polling should happen every 5 seconds)
    // We'll wait for multiple polling cycles
    await page.waitForTimeout(12000); // Wait 12 seconds to see updates

    // Check if status was updated (this depends on the mock responses)
    console.log('Real-time update test completed. Call count:', callCount);
  });

  test('should handle polling errors gracefully', async ({ page }) => {
    // Mock initial successful response
    await page.route('/api/analysis-runs*', async route => {
      const mockResponse = {
        analysisRuns: [
          {
            jobId: 'test-job-1',
            sourceType: 'manual',
            status: 'processing',
            totalEmails: 100,
            cachedEmails: 20,
            pendingEmails: 30,
            successfullyAnalyzed: 70,
            emailDateRange: {
              start: '2024-01-01',
              end: '2024-01-03'
            },
            analysisStartTime: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            currentStatus: 'Processing email 70 of 100',
            progress: {
              percentage: 70
            }
          }
        ],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalRuns: 1,
          hasNextPage: false,
          hasPreviousPage: false,
          pageSize: 20
        },
        filters: {
          sortBy: 'analysisStartTime',
          sortOrder: 'desc'
        }
      };
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockResponse)
      });
    });

    // Mock job status endpoint to fail
    await page.route('/api/jobs/test-job-1/status', route => {
      route.abort('failed');
    });

    // Load page
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Wait for polling attempts
    await page.waitForTimeout(8000);

    // Page should still be functional despite polling errors
    await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
    
    // Check console for error messages (polling errors should be logged but not break the UI)
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(msg.text());
      }
    });
  });

  test('should stop polling when no active runs exist', async ({ page }) => {
    // Mock response with only completed jobs
    await page.route('/api/analysis-runs*', async route => {
      const mockResponse = {
        analysisRuns: [
          {
            jobId: 'test-job-1',
            sourceType: 'manual',
            status: 'completed',
            totalEmails: 100,
            cachedEmails: 20,
            pendingEmails: 0,
            successfullyAnalyzed: 100,
            emailDateRange: {
              start: '2024-01-01',
              end: '2024-01-03'
            },
            analysisStartTime: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
            lastUpdated: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
            completedAt: new Date(Date.now() - 1800000).toISOString(),
            currentStatus: 'Analysis completed successfully'
          }
        ],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalRuns: 1,
          hasNextPage: false,
          hasPreviousPage: false,
          pageSize: 20
        },
        filters: {
          sortBy: 'analysisStartTime',
          sortOrder: 'desc'
        }
      };
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockResponse)
      });
    });

    // Track job status API calls (should not be called for completed jobs)
    let statusCallCount = 0;
    await page.route('/api/jobs/*/status', route => {
      statusCallCount++;
      route.continue();
    });

    // Load page
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Wait to see if polling occurs
    await page.waitForTimeout(8000);

    // Should not have active run indicators
    const activeRunIndicator = page.locator('text=active run');
    await expect(activeRunIndicator).not.toBeVisible();

    // Should not have made status API calls for completed jobs
    expect(statusCallCount).toBe(0);
  });
});
