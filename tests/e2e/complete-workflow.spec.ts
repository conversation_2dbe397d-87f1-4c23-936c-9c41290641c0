import { test, expect } from '@playwright/test';

test.describe('Complete Email Analysis Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Set up authentication if needed
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('complete user journey from dashboard to analysis completion', async ({ page }) => {
    // Step 1: Start on dashboard
    await expect(page.locator('h1', { hasText: 'Dashboard' })).toBeVisible();
    
    // Step 2: Navigate to analysis page from dashboard
    const analysisLink = page.locator('a[href="/analysis"]').first();
    await analysisLink.click();
    
    // Step 3: Verify analysis page loads
    await expect(page).toHaveURL('/analysis');
    await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
    
    // Step 4: Check initial state - should show start analysis section
    await expect(page.locator('h2', { hasText: 'Start New Email Analysis' })).toBeVisible();
    await expect(page.locator('[data-testid="start-date"]')).toBeVisible();
    await expect(page.locator('[data-testid="end-date"]')).toBeVisible();
    
    // Step 5: Fill in analysis dates
    const startDate = '2024-01-01';
    const endDate = '2024-01-03';
    
    await page.fill('[data-testid="start-date"]', startDate);
    await page.fill('[data-testid="end-date"]', endDate);
    
    // Step 6: Wait for token estimation (if implemented)
    await page.waitForTimeout(1000);
    
    // Step 7: Check if analyze button becomes enabled
    const analyzeButton = page.locator('[data-testid="analyze-button"]');
    
    // Note: Button might still be disabled due to insufficient tokens or other validation
    // In a real test environment, you'd want to ensure test data is set up properly
    const isDisabled = await analyzeButton.isDisabled();
    console.log('Analyze button disabled state:', isDisabled);
    
    if (!isDisabled) {
      // Step 8: Start analysis (only if button is enabled)
      await analyzeButton.click();
      
      // Step 9: Wait for analysis to start
      await page.waitForTimeout(2000);
      
      // Step 10: Check that analysis appears in the table
      await page.waitForSelector('[data-testid="analysis-runs-table"]', { timeout: 10000 });
      
      // Look for the new analysis run in the table
      const tableRows = page.locator('tbody tr');
      const rowCount = await tableRows.count();
      
      if (rowCount > 0) {
        // Check for processing status
        const processingBadge = page.locator('[data-testid="status-badge"]').first();
        if (await processingBadge.isVisible()) {
          const badgeText = await processingBadge.textContent();
          console.log('Analysis status:', badgeText);
        }
      }
    }
    
    // Step 11: Test filtering functionality
    await page.selectOption('[data-testid="source-type-filter"]', 'manual');
    await page.waitForTimeout(500);
    
    // Step 12: Test sorting functionality
    const totalEmailsHeader = page.locator('th', { hasText: 'Total Emails' });
    if (await totalEmailsHeader.isVisible()) {
      await totalEmailsHeader.click();
      await page.waitForTimeout(500);
    }
    
    // Step 13: Test refresh functionality
    const refreshButton = page.locator('button', { hasText: 'Refresh' });
    await refreshButton.click();
    await page.waitForTimeout(1000);
    
    // Step 14: Navigate back to dashboard
    const dashboardLink = page.locator('nav a[href="/"]');
    await dashboardLink.click();
    
    // Step 15: Verify we're back on dashboard
    await expect(page).toHaveURL('/');
    await expect(page.locator('h1', { hasText: 'Dashboard' })).toBeVisible();
  });

  test('error handling throughout the workflow', async ({ page }) => {
    // Test API error handling
    await page.route('/api/analysis-runs*', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    // Wait for error to appear
    await page.waitForTimeout(3000);
    
    // Should show error message
    const errorMessage = page.locator('text=Error:');
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toBeVisible();
      
      // Should have option to clear error
      const clearErrorButton = page.locator('button', { hasText: '×' });
      if (await clearErrorButton.isVisible()) {
        await clearErrorButton.click();
      }
    }
  });

  test('responsive design across different screen sizes', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
    await expect(page.locator('[data-testid="analysis-runs-table"]')).toBeVisible();
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);
    
    await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
    await expect(page.locator('[data-testid="analysis-runs-table"]')).toBeVisible();
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
    
    // Table should still be accessible on mobile (might be horizontally scrollable)
    await expect(page.locator('[data-testid="analysis-runs-table"]')).toBeVisible();
    
    // Filters should be accessible
    await expect(page.locator('text=Filters')).toBeVisible();
  });

  test('accessibility compliance', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    // Check for proper heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toBeVisible();
    
    const h2Elements = page.locator('h2');
    const h2Count = await h2Elements.count();
    expect(h2Count).toBeGreaterThan(0);
    
    // Check for proper form labels
    const startDateInput = page.locator('[data-testid="start-date"]');
    const startDateLabel = page.locator('label[for="startDate"]');
    if (await startDateInput.isVisible()) {
      // Should have associated label
      const hasLabel = await startDateLabel.isVisible();
      console.log('Start date has label:', hasLabel);
    }
    
    // Check for proper button accessibility
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i);
      const buttonText = await button.textContent();
      const hasAriaLabel = await button.getAttribute('aria-label');
      
      // Button should have text or aria-label
      const isAccessible = buttonText?.trim() || hasAriaLabel;
      console.log(`Button ${i} accessibility:`, !!isAccessible, buttonText?.trim());
    }
    
    // Check for proper table accessibility
    const table = page.locator('table');
    if (await table.isVisible()) {
      // Should have proper table headers
      const headers = page.locator('th');
      const headerCount = await headers.count();
      expect(headerCount).toBeGreaterThan(0);
      
      // Headers should have text content
      for (let i = 0; i < headerCount; i++) {
        const header = headers.nth(i);
        const headerText = await header.textContent();
        expect(headerText?.trim()).toBeTruthy();
      }
    }
  });

  test('data persistence and state management', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    // Apply filters
    await page.selectOption('[data-testid="source-type-filter"]', 'manual');
    await page.selectOption('[data-testid="status-filter"]', 'completed');
    
    // Navigate away and back
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    // Check if filters are preserved (this depends on implementation)
    const sourceTypeFilter = page.locator('[data-testid="source-type-filter"]');
    const statusFilter = page.locator('[data-testid="status-filter"]');
    
    const sourceTypeValue = await sourceTypeFilter.inputValue();
    const statusValue = await statusFilter.inputValue();
    
    console.log('Filter persistence - Source Type:', sourceTypeValue, 'Status:', statusValue);
    
    // Note: Filter persistence might not be implemented, which is fine
    // This test documents the current behavior
  });

  test('keyboard navigation support', async ({ page }) => {
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    // Test tab navigation
    await page.keyboard.press('Tab');
    await page.waitForTimeout(100);
    
    // Should be able to navigate through interactive elements
    const focusedElement = page.locator(':focus');
    const isVisible = await focusedElement.isVisible();
    console.log('Keyboard navigation - first tab stop visible:', isVisible);
    
    // Test Enter key on buttons
    const refreshButton = page.locator('button', { hasText: 'Refresh' });
    await refreshButton.focus();
    await page.keyboard.press('Enter');
    
    // Should trigger refresh
    await page.waitForTimeout(500);
    
    // Test arrow key navigation in table (if implemented)
    const firstTableCell = page.locator('tbody td').first();
    if (await firstTableCell.isVisible()) {
      await firstTableCell.focus();
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(100);
      
      // Check if focus moved (this depends on implementation)
      const newFocusedElement = page.locator(':focus');
      const newElementText = await newFocusedElement.textContent();
      console.log('Arrow key navigation result:', newElementText);
    }
  });
});
