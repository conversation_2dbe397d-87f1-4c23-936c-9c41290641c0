#!/usr/bin/env node

/**
 * Production Validation Test Suite
 * 
 * This script validates that all mock implementations have been replaced
 * with production-ready code and that the system is functioning correctly.
 */

const fs = require('fs');
const path = require('path');

class ProductionValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'error': '❌',
      'warning': '⚠️',
      'success': '✅',
      'info': 'ℹ️'
    }[type] || 'ℹ️';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  addError(test, message) {
    this.errors.push({ test, message });
    this.log(`FAIL: ${test} - ${message}`, 'error');
  }

  addWarning(test, message) {
    this.warnings.push({ test, message });
    this.log(`WARN: ${test} - ${message}`, 'warning');
  }

  addSuccess(test, message) {
    this.passed.push({ test, message });
    this.log(`PASS: ${test} - ${message}`, 'success');
  }

  /**
   * Test 1: Verify no mock implementations remain in codebase
   */
  async testNoMockImplementations() {
    this.log('Testing for remaining mock implementations...', 'info');
    
    const mockPatterns = [
      /return.*mock.*data/i,
      /hardcoded.*response/i,
      /placeholder.*implementation/i,
      /TODO.*replace.*with.*actual/i,
      /FIXME.*mock/i,
      /temporary.*implementation/i
    ];

    const filesToCheck = [
      'apps/webapp/src/app/api',
      'libs/services/src/lib',
      'apps/email-analysis-function/src',
      'apps/notification-handler-function/src'
    ];

    let foundMocks = false;

    for (const dir of filesToCheck) {
      if (fs.existsSync(dir)) {
        await this.scanDirectoryForMocks(dir, mockPatterns);
      }
    }

    if (!foundMocks) {
      this.addSuccess('Mock Detection', 'No obvious mock implementations found');
    }
  }

  async scanDirectoryForMocks(dirPath, patterns) {
    const files = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const file of files) {
      const fullPath = path.join(dirPath, file.name);
      
      if (file.isDirectory()) {
        await this.scanDirectoryForMocks(fullPath, patterns);
      } else if (file.name.endsWith('.ts') || file.name.endsWith('.js')) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        for (const pattern of patterns) {
          if (pattern.test(content)) {
            this.addWarning('Mock Detection', `Potential mock found in ${fullPath}`);
          }
        }
      }
    }
  }

  /**
   * Test 2: Verify ConfigService is properly implemented
   */
  testConfigService() {
    this.log('Testing ConfigService implementation...', 'info');
    
    try {
      const configPath = 'libs/services/src/lib/config.ts';
      if (!fs.existsSync(configPath)) {
        this.addError('ConfigService', 'ConfigService file not found');
        return;
      }

      const content = fs.readFileSync(configPath, 'utf8');
      
      // Check for required methods
      const requiredMethods = [
        'getUserTokenLimit',
        'getInitialTokenAllocation',
        'getWebappProgressWebhookUrl',
        'getMaxEmailsPerBatch',
        'getOpenAIConfig',
        'getDatabaseConfig'
      ];

      for (const method of requiredMethods) {
        if (content.includes(method)) {
          this.addSuccess('ConfigService', `Method ${method} found`);
        } else {
          this.addError('ConfigService', `Method ${method} missing`);
        }
      }

      // Check for singleton pattern
      if (content.includes('getInstance()')) {
        this.addSuccess('ConfigService', 'Singleton pattern implemented');
      } else {
        this.addError('ConfigService', 'Singleton pattern not found');
      }

    } catch (error) {
      this.addError('ConfigService', `Error reading config file: ${error.message}`);
    }
  }

  /**
   * Test 3: Verify authentication implementations
   */
  testAuthenticationImplementations() {
    this.log('Testing authentication implementations...', 'info');
    
    // Check auth.ts
    const authPath = 'apps/webapp/src/lib/auth.ts';
    if (fs.existsSync(authPath)) {
      const content = fs.readFileSync(authPath, 'utf8');
      
      if (content.includes('createClerkClient')) {
        this.addSuccess('Authentication', 'Real Clerk API integration found');
      } else {
        this.addError('Authentication', 'Clerk API integration not found');
      }

      if (content.includes('mock') || content.includes('hardcoded')) {
        this.addWarning('Authentication', 'Potential mock code found in auth.ts');
      }
    } else {
      this.addError('Authentication', 'auth.ts file not found');
    }
  }

  /**
   * Test 4: Verify token service implementations
   */
  testTokenServiceImplementations() {
    this.log('Testing token service implementations...', 'info');
    
    // Check token route
    const tokenRoutePath = 'apps/webapp/src/app/api/tokens/route.ts';
    if (fs.existsSync(tokenRoutePath)) {
      const content = fs.readFileSync(tokenRoutePath, 'utf8');
      
      if (content.includes('ConfigService')) {
        this.addSuccess('Token Service', 'ConfigService integration found');
      } else {
        this.addError('Token Service', 'ConfigService integration missing');
      }

      if (content.includes('4500') || content.includes('mock data')) {
        this.addError('Token Service', 'Mock fallback data still present');
      } else {
        this.addSuccess('Token Service', 'No mock fallback data found');
      }
    } else {
      this.addError('Token Service', 'Token route not found');
    }
  }

  /**
   * Test 5: Verify email processing implementations
   */
  testEmailProcessingImplementations() {
    this.log('Testing email processing implementations...', 'info');
    
    const emailServicePath = 'libs/services/src/lib/email.service.ts';
    if (fs.existsSync(emailServicePath)) {
      const content = fs.readFileSync(emailServicePath, 'utf8');
      
      if (content.includes('simplified') || content.includes('placeholder')) {
        this.addError('Email Processing', 'Simplified/placeholder code still present');
      } else {
        this.addSuccess('Email Processing', 'No simplified code found');
      }

      if (content.includes('openAIService.analyzeEmail')) {
        this.addSuccess('Email Processing', 'OpenAI integration found');
      } else {
        this.addError('Email Processing', 'OpenAI integration missing');
      }
    } else {
      this.addError('Email Processing', 'Email service not found');
    }
  }

  /**
   * Test 6: Verify Gmail history processing
   */
  testGmailHistoryProcessing() {
    this.log('Testing Gmail history processing...', 'info');
    
    const notificationHandlerPath = 'apps/notification-handler-function/src/main.ts';
    if (fs.existsSync(notificationHandlerPath)) {
      const content = fs.readFileSync(notificationHandlerPath, 'utf8');
      
      if (content.includes('getHistory')) {
        this.addSuccess('Gmail History', 'History processing implementation found');
      } else {
        this.addError('Gmail History', 'History processing implementation missing');
      }

      if (content.includes('TODO') && content.includes('history')) {
        this.addError('Gmail History', 'TODO comments still present');
      } else {
        this.addSuccess('Gmail History', 'No TODO comments found');
      }
    } else {
      this.addError('Gmail History', 'Notification handler not found');
    }
  }

  /**
   * Test 7: Verify environment configuration
   */
  testEnvironmentConfiguration() {
    this.log('Testing environment configuration...', 'info');
    
    const requiredEnvVars = [
      'CLERK_SECRET_KEY',
      'OPENAI_API_KEY',
      'GOOGLE_APPLICATION_CREDENTIALS'
    ];

    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        this.addSuccess('Environment', `${envVar} is configured`);
      } else {
        this.addWarning('Environment', `${envVar} is not configured`);
      }
    }
  }

  /**
   * Run all tests and generate report
   */
  async runAllTests() {
    this.log('Starting Production Validation Tests...', 'info');
    this.log('='.repeat(50), 'info');

    await this.testNoMockImplementations();
    this.testConfigService();
    this.testAuthenticationImplementations();
    this.testTokenServiceImplementations();
    this.testEmailProcessingImplementations();
    this.testGmailHistoryProcessing();
    this.testEnvironmentConfiguration();

    this.generateReport();
  }

  generateReport() {
    this.log('='.repeat(50), 'info');
    this.log('PRODUCTION VALIDATION REPORT', 'info');
    this.log('='.repeat(50), 'info');

    this.log(`✅ Tests Passed: ${this.passed.length}`, 'success');
    this.log(`⚠️  Warnings: ${this.warnings.length}`, 'warning');
    this.log(`❌ Errors: ${this.errors.length}`, 'error');

    if (this.errors.length > 0) {
      this.log('\nERRORS FOUND:', 'error');
      this.errors.forEach(error => {
        this.log(`  - ${error.test}: ${error.message}`, 'error');
      });
    }

    if (this.warnings.length > 0) {
      this.log('\nWARNINGS:', 'warning');
      this.warnings.forEach(warning => {
        this.log(`  - ${warning.test}: ${warning.message}`, 'warning');
      });
    }

    const overallStatus = this.errors.length === 0 ? 'READY FOR PRODUCTION' : 'NEEDS ATTENTION';
    this.log(`\nOVERALL STATUS: ${overallStatus}`, this.errors.length === 0 ? 'success' : 'error');

    return this.errors.length === 0;
  }
}

// Run the validation if this script is executed directly
if (require.main === module) {
  const validator = new ProductionValidator();
  validator.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Validation failed:', error);
    process.exit(1);
  });
}

module.exports = ProductionValidator;
