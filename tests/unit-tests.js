#!/usr/bin/env node

/**
 * Unit Tests for Individual Components
 * Tests specific functionality without requiring full environment
 */

const fs = require('fs');
const path = require('path');

class UnitTests {
  static async runAll() {
    console.log('🔬 Running Unit Tests...');
    
    await this.testProjectStructure();
    await this.testConfigurationFiles();
    await this.testBuildOutputs();
    await this.testLibraryStructure();
    await this.testFunctionStructure();
  }

  static async testProjectStructure() {
    console.log('\n📁 Testing Project Structure...');
    
    const requiredDirs = [
      'libs/shared',
      'libs/email-core',
      'apps/email-analysis-function',
      'apps/notification-handler-function',
      'apps/webapp'
    ];

    const requiredFiles = [
      'nx.json',
      'tsconfig.base.json',
      'firebase.json',
      '.firebaserc',
      'package.json'
    ];

    let allPresent = true;

    requiredDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`✅ Directory exists: ${dir}`);
      } else {
        console.log(`❌ Missing directory: ${dir}`);
        allPresent = false;
      }
    });

    requiredFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`✅ File exists: ${file}`);
      } else {
        console.log(`❌ Missing file: ${file}`);
        allPresent = false;
      }
    });

    return allPresent;
  }

  static async testConfigurationFiles() {
    console.log('\n⚙️ Testing Configuration Files...');
    
    // Test nx.json
    try {
      const nxConfig = JSON.parse(fs.readFileSync('nx.json', 'utf8'));
      if (nxConfig.projects && nxConfig.targetDefaults) {
        console.log('✅ nx.json is valid');
      } else {
        console.log('❌ nx.json missing required properties');
      }
    } catch (error) {
      console.log('❌ nx.json is invalid:', error.message);
    }

    // Test firebase.json
    try {
      const firebaseConfig = JSON.parse(fs.readFileSync('firebase.json', 'utf8'));
      if (firebaseConfig.functions && firebaseConfig.emulators) {
        console.log('✅ firebase.json is valid');
      } else {
        console.log('❌ firebase.json missing required properties');
      }
    } catch (error) {
      console.log('❌ firebase.json is invalid:', error.message);
    }

    // Test tsconfig.base.json
    try {
      const tsconfigBase = JSON.parse(fs.readFileSync('tsconfig.base.json', 'utf8'));
      if (tsconfigBase.compilerOptions && tsconfigBase.compilerOptions.paths) {
        console.log('✅ tsconfig.base.json is valid');
      } else {
        console.log('❌ tsconfig.base.json missing required properties');
      }
    } catch (error) {
      console.log('❌ tsconfig.base.json is invalid:', error.message);
    }
  }

  static async testBuildOutputs() {
    console.log('\n🔨 Testing Build Outputs...');
    
    const expectedOutputs = [
      'dist/libs/shared',
      'dist/libs/email-core',
      'dist/apps/email-analysis-function',
      'dist/apps/notification-handler-function'
    ];

    expectedOutputs.forEach(output => {
      if (fs.existsSync(output)) {
        console.log(`✅ Build output exists: ${output}`);
        
        // Check for main files
        const mainFile = path.join(output, 'main.js');
        const indexFile = path.join(output, 'src/index.js');
        
        if (fs.existsSync(mainFile) || fs.existsSync(indexFile)) {
          console.log(`✅ Main file exists in: ${output}`);
        } else {
          console.log(`⚠️ No main file found in: ${output}`);
        }
      } else {
        console.log(`❌ Missing build output: ${output}`);
      }
    });
  }

  static async testLibraryStructure() {
    console.log('\n📚 Testing Library Structure...');
    
    // Test shared library
    const sharedFiles = [
      'libs/shared/src/index.ts',
      'libs/shared/src/lib/logger.ts',
      'libs/shared/src/lib/database.ts',
      'libs/shared/project.json'
    ];

    sharedFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`✅ Shared library file: ${file}`);
      } else {
        console.log(`❌ Missing shared library file: ${file}`);
      }
    });

    // Test email-core library
    const emailCoreFiles = [
      'libs/email-core/src/index.ts',
      'libs/email-core/src/lib/email-analyzer.ts',
      'libs/email-core/src/lib/openai-service.ts',
      'libs/email-core/project.json'
    ];

    emailCoreFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`✅ Email-core library file: ${file}`);
      } else {
        console.log(`❌ Missing email-core library file: ${file}`);
      }
    });
  }

  static async testFunctionStructure() {
    console.log('\n☁️ Testing Function Structure...');
    
    // Test email analysis function
    const emailAnalysisFiles = [
      'apps/email-analysis-function/src/main.ts',
      'apps/email-analysis-function/project.json'
    ];

    emailAnalysisFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`✅ Email analysis function file: ${file}`);
      } else {
        console.log(`❌ Missing email analysis function file: ${file}`);
      }
    });

    // Test notification handler function
    const notificationFiles = [
      'apps/notification-handler-function/src/main.ts',
      'apps/notification-handler-function/project.json'
    ];

    notificationFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`✅ Notification handler function file: ${file}`);
      } else {
        console.log(`❌ Missing notification handler function file: ${file}`);
      }
    });

    // Test function content
    try {
      const emailFunctionContent = fs.readFileSync('apps/email-analysis-function/src/main.ts', 'utf8');
      if (emailFunctionContent.includes('onMessagePublished') && emailFunctionContent.includes('EmailAnalyzer')) {
        console.log('✅ Email analysis function has correct structure');
      } else {
        console.log('❌ Email analysis function missing required imports');
      }
    } catch (error) {
      console.log('❌ Could not read email analysis function:', error.message);
    }

    try {
      const notificationContent = fs.readFileSync('apps/notification-handler-function/src/main.ts', 'utf8');
      if (notificationContent.includes('onRequest') && notificationContent.includes('PubSub')) {
        console.log('✅ Notification handler function has correct structure');
      } else {
        console.log('❌ Notification handler function missing required imports');
      }
    } catch (error) {
      console.log('❌ Could not read notification handler function:', error.message);
    }
  }
}

// Run if called directly
if (require.main === module) {
  UnitTests.runAll()
    .then(() => console.log('\n🎉 Unit tests completed!'))
    .catch(error => {
      console.error('❌ Unit tests failed:', error);
      process.exit(1);
    });
}

module.exports = UnitTests;
