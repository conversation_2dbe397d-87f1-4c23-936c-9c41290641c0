# 🔒 Security Guidelines

This document outlines security best practices for the Data Driven Job Search project.

## 🚨 Critical Security Rules

### ❌ NEVER COMMIT THESE FILES:
- `.env` files (any environment)
- `*.key` files
- `service-account*.json` files
- `credentials.json`
- Any file containing API keys, passwords, or secrets

### ✅ ALWAYS DO THIS:
- Use `.env.template` files for examples
- Store secrets in environment variables
- Use different keys for development and production
- Regularly rotate API keys
- Review commits before pushing

## 🛡️ Security Safeguards in Place

### 1. Git Hooks
- **Pre-commit hook** automatically scans for secrets
- Blocks commits containing sensitive patterns
- Located in `.githooks/pre-commit`

### 2. .gitignore Protection
Enhanced `.gitignore` patterns for:
- All `.env*` files
- Credential files
- Private keys
- Service account files

### 3. Template System
- Use `.env.production.template` as a guide
- Copy to `.env.production` and fill in real values
- Templates are safe to commit, actual env files are not

## 🔑 Environment Variables Setup

### Development (.env.local)
```bash
# Copy from template and fill in development values
cp .env.local.template .env.local
# Edit with your development keys
```

### Production (.env.production)
```bash
# Copy from template and fill in production values
cp .env.production.template .env.production
# Edit with your PRODUCTION keys (different from dev!)
```

## 🔐 API Key Management

### OpenAI API Keys
- **Development**: Use a separate API key with usage limits
- **Production**: Use a different key with appropriate limits
- **Rotation**: Rotate keys every 90 days
- **Monitoring**: Monitor usage in OpenAI dashboard

### Clerk Authentication
- **Development**: Use test environment keys (`pk_test_*`, `sk_test_*`)
- **Production**: Use live environment keys (`pk_live_*`, `sk_live_*`)
- **Domains**: Configure authorized domains in Clerk dashboard

### Google Cloud / Firebase
- **Service Accounts**: Use least-privilege principle
- **API Keys**: Restrict by IP and service
- **Rotation**: Rotate service account keys regularly

## 🧪 Testing Security

### Run Security Check
```bash
# Test the pre-commit hook
.githooks/pre-commit

# Check for secrets in codebase
git secrets --scan
```

### Validate Environment
```bash
# Check that no secrets are in git history
git log --all --full-history -- .env*

# Verify .gitignore is working
git check-ignore .env.production
```

## 🚨 If Secrets Are Accidentally Committed

### Immediate Actions:
1. **Rotate the compromised keys immediately**
2. **Remove from git history**:
   ```bash
   # Remove file from history
   git filter-branch --force --index-filter \
     'git rm --cached --ignore-unmatch .env.production' \
     --prune-empty --tag-name-filter cat -- --all
   
   # Force push (dangerous - coordinate with team)
   git push origin --force --all
   ```
3. **Notify team members**
4. **Update deployment with new keys**

### Prevention:
- Always run `git status` before committing
- Use the pre-commit hook
- Review diffs carefully
- Use `git add` selectively, not `git add .`

## 📋 Security Checklist

Before deploying:
- [ ] All API keys are production-ready
- [ ] No secrets in git history
- [ ] Environment files are properly ignored
- [ ] Pre-commit hook is active
- [ ] Different keys for dev/prod environments
- [ ] Service accounts have minimal permissions
- [ ] SSL certificates are configured
- [ ] Firestore rules are restrictive

## 🔍 Regular Security Audits

### Monthly:
- Review API key usage
- Check for unused keys
- Audit service account permissions
- Review Firestore security rules

### Quarterly:
- Rotate all API keys
- Update dependencies (`npm audit`)
- Review access logs
- Test security measures

## 📞 Security Incident Response

If you suspect a security breach:

1. **Immediately rotate all potentially compromised keys**
2. **Check access logs** in Google Cloud Console
3. **Review recent commits** for accidental exposures
4. **Notify the team** about the incident
5. **Document the incident** and response actions

## 🛠️ Tools and Resources

### Security Tools:
- [git-secrets](https://github.com/awslabs/git-secrets) - Prevent secrets in git
- [truffleHog](https://github.com/trufflesecurity/truffleHog) - Find secrets in git history
- [GitGuardian](https://www.gitguardian.com/) - Automated secret detection

### Best Practices:
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Google Cloud Security Best Practices](https://cloud.google.com/security/best-practices)
- [GitHub Security Best Practices](https://docs.github.com/en/code-security)

## 🎯 Remember

**Security is everyone's responsibility!**

When in doubt:
- Ask before committing
- Use templates instead of real values
- Test security measures regularly
- Keep secrets secret! 🤐
