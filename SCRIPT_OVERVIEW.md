# 📜 Deployment Scripts Overview

This document clarifies what each script does automatically vs. what requires manual steps.

## 🤖 **Fully Automated Scripts**

### `scripts/deploy-to-gcp.sh`
**What it AUTOMATICALLY does:**
- ✅ Validates prerequisites (Firebase CLI, gcloud CLI, authentication)
- ✅ Sets active GCP project
- ✅ Builds all projects with Nx
- ✅ Enables required Google Cloud APIs
- ✅ Creates Pub/Sub topics
- ✅ Deploys Firestore rules and indexes
- ✅ Deploys Cloud Functions
- ✅ Builds webapp for production
- ✅ Creates production Dockerfile
- ✅ Builds and deploys to Cloud Run
- ✅ Provides deployment URLs and next steps

**What it REQUIRES from you:**
- 🔧 Set up secrets in Google Cloud Secret Manager (run `./scripts/setup-secrets.sh` first)
- 🔧 Be logged in to Firebase and Google Cloud

### `scripts/pre-deployment-check.sh`
**What it AUTOMATICALLY does:**
- ✅ Checks all required tools are installed
- ✅ Validates authentication status
- ✅ Verifies project structure
- ✅ Tests build process
- ✅ Validates environment configuration

**What it REQUIRES from you:**
- Nothing - just run it!

## 🔄 **Semi-Automated Scripts**

### `scripts/setup-domain.sh`
**What it AUTOMATICALLY does:**
- ✅ Creates domain mapping in Google Cloud
- ✅ Retrieves required DNS record information
- ✅ Checks domain mapping status

**What it PROVIDES INSTRUCTIONS for:**
- 📋 DNS configuration in Namecheap (cannot be automated)
- 📋 Domain ownership verification in Google Search Console
- 📋 SSL certificate monitoring

**Why not fully automated?**
- DNS providers don't have standardized APIs
- Requires your Namecheap login credentials
- Security risk to store DNS provider credentials

### `scripts/deploy-complete.sh`
**What it AUTOMATICALLY does:**
- ✅ Runs pre-deployment checks
- ✅ Executes full GCP deployment
- ✅ Sets up domain mapping
- ✅ Provides comprehensive next steps

**What it REQUIRES from you:**
- 🔧 Confirmation prompts for safety
- 🔧 Manual DNS configuration afterward

## 📋 **Manual Steps Required**

### 1. **Environment Setup** (One-time)
```bash
# You must do this manually for security:
cp .env.production.template .env.production
# Edit with your actual production API keys
```

### 2. **DNS Configuration** (One-time)
```bash
# You must do this in Namecheap dashboard:
# 1. Log in to Namecheap
# 2. Go to Advanced DNS
# 3. Add CNAME record: dev → [provided target]
```

### 3. **Domain Ownership** (If required)
```bash
# You must do this in Google Search Console:
# 1. Add property for datadrivenjobsearch.com
# 2. Verify ownership via DNS record
```

## 🚀 **Complete Deployment Flow**

### **Automated Steps** (Run once):
```bash
# 1. Check everything is ready
./scripts/pre-deployment-check.sh

# 2. Deploy everything to GCP
./scripts/deploy-to-gcp.sh

# 3. Set up domain mapping
./scripts/setup-domain.sh
```

### **Manual Steps** (Do once):
1. **Set up secrets in Secret Manager** (`./scripts/setup-secrets.sh`)
2. **Configure DNS in Namecheap**
3. **Wait for DNS propagation**
4. **Test the deployment**

## 🎯 **What Gets Deployed Automatically**

When you run `./scripts/deploy-to-gcp.sh`, it automatically:

1. **Cloud Functions** → `ddjs-dev-458016` project
2. **Firestore Database** → Production rules and indexes
3. **Pub/Sub Topics** → `email-analysis-requests`, `gmail-notifications`
4. **Cloud Run Service** → Next.js webapp
5. **Container Images** → Built and pushed to Google Container Registry
6. **API Enablement** → All required Google Cloud APIs

## 🔍 **Verification**

After running the scripts, you can verify deployment:

```bash
# Check Cloud Functions
firebase functions:list --project ddjs-dev-458016

# Check Cloud Run
gcloud run services list --project ddjs-dev-458016

# Check Pub/Sub topics
gcloud pubsub topics list --project ddjs-dev-458016
```

## 💡 **Key Takeaway**

- **95% of deployment is automated** 🤖
- **Only DNS configuration requires manual steps** 📋
- **Security-sensitive steps (API keys) are intentionally manual** 🔒

The scripts do the heavy lifting - you just need to configure DNS and provide your API keys!
