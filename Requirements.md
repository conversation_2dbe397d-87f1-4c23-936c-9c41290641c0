# Data Driven Job Search Requirements

## Overview
Data Driven Job Search is a web application that helps users track and analyze their job search emails using Gmail integration and AI analysis.

## Core Features
- automatically track how many jobs I've applied to over time
- automatically track how many of those applications converted into interviews
- automatically track how far i've progressed in the interview process for each application (e.g. recruiter screen, hiring manager screen, technical screen, onsite/panel interview, offer, rejection)
- generate summary statistics across all users on typical conversion rates from applications to interviews by company, role, and date range

### Email Analysis
- AI-powered analysis of job-related emails using a LLM
- Classification of emails into predefined categories:
  - Not job search related
  - Inbound job opportunities
  - Application confirmations
  - Rejections (various types)
  - Next steps (assessments, interviews, etc.)
  - Offers
- Extraction of key metadata:
  - Company name
  - Role name
  - ATS system used
  - Required actions for the candidate
- logic to link emails related to the same job application and track the status of the application over time
- save the analysis results so that that we don't have to re-run the analysis on the same email multiple times

### Gmail Integration
- OAuth2 authentication with Gmail API
- Access to read user's emails
- Ability to create and manage DDJS specific labels in Gmail
- Add appropriate labels to emails based on the analysis results
- Allow users to opt to have certain emails classifications skip their inbox (e.g. Confirmation emails, Rejection emails, etc.)
- Secure token management and refresh capabilities
- Support partial syncronization of emails using push notifications from the Gmail API so we can quickly classify new emails as they come in

### User Interface
- Login using Google OAuth
- Manage Logins across multiple devices
- Configure settings for which email classifications to skip from the inbox
- View summary statistics of applications and interviews
- Allow users to select a historical date range for which to analyze and view metrics
    - If email processing tokens are required show users how many email processing tokens will be used for the selected date range and how many tokens are remaining
    - Display interactive graphs showing:
        - Number of applications over time
        - Number of interviews over time
        - Allow users to toggle between daily, weekly, and monthly views
        - Show warning messages when no data is available for selected date range
    - Cache analysis results to avoid reprocessing the same emails

### Metrics and Analytics
- Track and visualize key job search metrics:
    - Applications submitted over time
    - Interview invitations received
    - Group related interview emails to count distinct interview rounds
    - Support different time period views (daily/weekly/monthly)
- Display warning messages when:
    - No analyzed emails exist for selected date range
    - Analysis needs to be run first
    - Errors occur during metrics retrieval

### Real-time Progress Tracking
- Live progress bar showing email processing status
- Detailed processing estimates showing:
  - Total emails to analyze
  - Number of emails needing token analysis
  - Real-time count of processed emails
  - Remaining emails requiring analysis
- Current email being processed
- Error reporting for individual email processing failures

### Dynamic Metrics Visualization
- Real-time metrics updates during email analysis
- Support for daily/weekly/monthly view granularity
- Automatic chart updates as new data is processed
- Visual feedback for data processing status

## Technical Requirements

### Build Targets
- Local development
  - Support local development with Firestore emulator
  - Hot reload support for development efficiency
  - Proper error handling and user feedback
  - Structured logging for debugging
- Production (Google Cloud Run)
- Test (Google Cloud Run)

### Security
- Secure handling of API keys and credentials
- Environment-based OAuth configurations
- HTTPS enforcement in production
- Proper secret management in Google Cloud
- Secure session management with unique session IDs for each device a user logs in from

### Email Processing
- email content truncation at 5000 characters
- efficient handling of multipart email content
- support text/plain and text/html mime types for email analysis
- AI analysis results should be cached so we don't have to re-run the analysis on the same email multiple times
    - results should only be saved if there are no errors
    - Saved results should be invalidated if there is a new version of the AI analysis code

### Email Processing Tokens
- Users initially start with 5000 email processing tokens
- One token is consumed per email analyzed by the AI
- Cached analysis results do not consume additional tokens
- Analysis results are cached in Firestore and linked to:
  - User ID
  - Email ID
  - Analysis version
- Users cannot use more email processing tokens than they have
- System must:
  - Show users their remaining token count
  - Estimate token usage before processing emails
  - Prevent analysis if insufficient tokens
  - Only deduct tokens for new analysis (not cached results)
  - Invalidate cached results when AI analysis version changes
  - Track total tokens used per user
  - Support future token purchase system (out of scope for initial release)

### Deployment
- Google Cloud Run deployment support
- Automated builds via Cloud Build
- Health check endpoints
- Production-ready Docker configuration

### Monitoring
- Structured logging system
- Error tracking and reporting
- Usage metrics collection
- API usage monitoring (Gmail & OpenAI)

## License
- Custom license for source code
- Contribution agreement for external contributors
- No commercial use without written agreement

## Infrastructure
- Google Cloud Platform hosting
- Cloud Run for application hosting
- Cloud Build for CI/CD
- Firestore for data storage
- Secret Manager for credentials
