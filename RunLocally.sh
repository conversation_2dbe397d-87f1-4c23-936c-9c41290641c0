#!/bin/bash

# Create log directory if it doesn't exist
LOG_DIR="logs"
mkdir -p "$LOG_DIR"

# Generate timestamp for log file name
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/run_local_$TIMESTAMP.log"

# Function to log messages to both console and log file
log() {
  echo "$@" | tee -a "$LOG_FILE"
}

log "Starting application at $(date)"
log "Logging to file: $LOG_FILE"

# Access secrets and store them in environment variables
log "Retrieving secrets from Google Secret Manager..."
# Capture gcloud command output to log file but don't display secrets in terminal
log "Fetching CLERK_PUBLISHABLE_KEY..."
export NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$(gcloud secrets versions access latest --secret="CLERK_PUBLISHABLE_KEY" 2>> "$LOG_FILE")
log "Fetching CLERK_SECRET_KEY..."
export CLERK_SECRET_KEY=$(gcloud secrets versions access latest --secret="CLERK_SECRET_KEY" 2>> "$LOG_FILE")
log "Fetching OPENAI_API_KEY..."
export OPENAI_API_KEY=$(gcloud secrets versions access latest --secret="OPENAI_API_KEY" 2>> "$LOG_FILE")

# Log success for each secret (without revealing the actual values)
[ -n "$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY" ] && log "✓ CLERK_PUBLISHABLE_KEY retrieved successfully" || log "✗ Failed to retrieve CLERK_PUBLISHABLE_KEY"
[ -n "$CLERK_SECRET_KEY" ] && log "✓ CLERK_SECRET_KEY retrieved successfully" || log "✗ Failed to retrieve CLERK_SECRET_KEY"
[ -n "$OPENAI_API_KEY" ] && log "✓ OPENAI_API_KEY retrieved successfully" || log "✗ Failed to retrieve OPENAI_API_KEY"

# Use explicit standard NODE_ENV as per Next.js warning
export NODE_ENV=development
export BASE_URL="http://localhost:8080"

# Add Clerk routing configuration
export NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"
export NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"
export NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/"
export NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/"

# Verify required secrets are set
REQUIRED_VARS=(
  "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
  "CLERK_SECRET_KEY"
  "OPENAI_API_KEY"
)

MISSING_VARS=()
for var in "${REQUIRED_VARS[@]}"; do
  if [ -z "${!var}" ]; then
    MISSING_VARS+=("$var")
  fi
done

if [ ${#MISSING_VARS[@]} -ne 0 ]; then
  log "Error: Missing required environment variables:"
  for var in "${MISSING_VARS[@]}"; do
    log " - $var"
  done
  log "Please ensure these secrets exist in Google Secret Manager and you have access permissions."
  exit 1
fi

log "All required secrets retrieved successfully."

# Set the development project ID explicitly for local development
export GOOGLE_CLOUD_PROJECT="ddjs-dev-458016"

# Only use gcloud config if we're in production mode
if [ "$USE_PRODUCTION" = true ]; then
  # Get the Google Cloud project ID from gcloud config
  PROD_PROJECT_ID=$(gcloud config get-value project 2>> "$LOG_FILE")

  # Check if the project ID was retrieved successfully
  if [ -z "$PROD_PROJECT_ID" ]; then
    log "Error: Could not retrieve Google Cloud project ID. Please make sure you are authenticated with gcloud and have a project selected."
    exit 1
  fi

  # Override with production project ID
  export GOOGLE_CLOUD_PROJECT="data-driven-job-search"
  log "Using production Google Cloud project ID: $GOOGLE_CLOUD_PROJECT"
else
  log "Using development Google Cloud project ID: $GOOGLE_CLOUD_PROJECT"
fi
log "Environment: $NODE_ENV"
log "Base URL: $BASE_URL"

# Create a temporary .env.local file for development
log "Creating temporary .env.local file for Docker build..."
cat > .env.local << EOF
# Development environment variables
NODE_ENV=$NODE_ENV
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
CLERK_SECRET_KEY=$CLERK_SECRET_KEY
NEXT_PUBLIC_CLERK_SIGN_IN_URL=$NEXT_PUBLIC_CLERK_SIGN_IN_URL
NEXT_PUBLIC_CLERK_SIGN_UP_URL=$NEXT_PUBLIC_CLERK_SIGN_UP_URL
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=$NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=$NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL
OPENAI_API_KEY=$OPENAI_API_KEY
BASE_URL=$BASE_URL
GOOGLE_CLOUD_PROJECT=$GOOGLE_CLOUD_PROJECT
EOF

# Parse command line arguments
USE_DOCKER=false
USE_PRODUCTION=false
CLEAR_DATA=false

for arg in "$@"
do
  case $arg in
    --docker)
      USE_DOCKER=true
      ;;
    --production)
      USE_PRODUCTION=true
      log "WARNING: Using production Firestore database. This should only be used for specific testing scenarios."
      ;;
    --clear-data)
      CLEAR_DATA=true
      ;;
    *)
      log "Unknown argument: $arg"
      log "Usage: ./RunLocally.sh [--docker] [--production] [--clear-data]"
      exit 1
      ;;
  esac
shift
done

# Set environment variables based on arguments
if [ "$USE_PRODUCTION" = true ]; then
  export USE_PRODUCTION_FIRESTORE=true
  log "Setting USE_PRODUCTION_FIRESTORE=true"
else
  export USE_PRODUCTION_FIRESTORE=false
  log "Using development Firestore database by default"
fi

# Clear data if requested
if [ "$CLEAR_DATA" = true ]; then
  log "Clearing database data..."
  node scripts/clear-database.js 2>&1 | tee -a "$LOG_FILE"
  log "Database clearing complete"
fi

# Verify database connection
log "Verifying database connection..."
node scripts/verify-database.js 2>&1 | tee -a "$LOG_FILE"

# Check if verification failed in production mode
if [ "$?" -ne 0 ] && [ "$USE_PRODUCTION" = true ]; then
  log "FATAL: Database connection verification failed in production mode"
  exit 1
fi

# Check if we should use Docker or run locally
if [ "$USE_DOCKER" = true ]; then
  # Force cleanup of any existing Docker images/containers for a fresh start
  log "Cleaning up Docker resources for a fresh build..."
  docker compose down 2>&1 | tee -a "$LOG_FILE"
  docker compose rm -f 2>&1 | tee -a "$LOG_FILE"

  # Build and start the development environment with Docker Compose
  # Use --no-cache to ensure all dependencies are freshly installed
  log "Building Docker container with fresh dependencies..."
  docker compose build --no-cache --build-arg NODE_ENV=$NODE_ENV 2>&1 | tee -a "$LOG_FILE" && \
  docker compose up 2>&1 | tee -a "$LOG_FILE"
else
  # Run locally without Docker
  log "Starting application locally with Socket.IO support..."
  # Run npm command and capture both stdout and stderr to log file while also displaying in terminal
  npm run dev 2>&1 | tee -a "$LOG_FILE"
fi

# Add a final log message (this will only be seen if the script exits normally)
log "Script completed at $(date)"