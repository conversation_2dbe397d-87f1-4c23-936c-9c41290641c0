---
description: 
globs: 
alwaysApply: true
---
- We are building using NextJS, <PERSON> for Auth and GCP for hosting
- Always check [package.json](mdc:package.json) to understand what version of packages we are using
- [Requirements.md](mdc:Requirements.md) contains a high level overview of the project please reference it as needed and suggest updates to the document based on our work together
- When things are ambiguous ask for clarifying questions
- Always outline the series of steps your going to take before taking them and get confirmation from me before proceeding
- Track your progress through each step as you complete them
- Please alert me whenever you are making a change that might remove functionality
