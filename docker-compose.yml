version: '3.8'

services:
  webapp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # Pass NODE_ENV to the Dockerfile
        NODE_ENV: ${NODE_ENV}
    # Override the CMD in Dockerfile to use our custom server
    command: node server.js
    ports:
      - "3000:3000"
    environment:
      # Core environment
      - NODE_ENV=${NODE_ENV}
      - BASE_URL=${BASE_URL}
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}

      # Clerk configuration
      - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      - CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
      - NEXT_PUBLIC_CLERK_SIGN_IN_URL=${NEXT_PUBLIC_CLERK_SIGN_IN_URL}
      - NEXT_PUBLIC_CLERK_SIGN_UP_URL=${NEXT_PUBLIC_CLERK_SIGN_UP_URL}
      - NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=${NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL}
      - NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=${NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL}

      # Note: Google OAuth is handled by Clerk - no client credentials needed

      # OpenAI
      - OPENAI_API_KEY=${OPENAI_API_KEY}

      # Firestore emulator configuration
      - FIRESTORE_EMULATOR_HOST=firestore:8080
      - FIREBASE_AUTH_EMULATOR_HOST=firestore:9099
    volumes:
      - .:/app
      - /app/node_modules
      # Mount the .env.local file
      - ./.env.local:/app/.env.local
    depends_on:
      - firestore

  firestore:
    image: gcr.io/google.com/cloudsdktool/google-cloud-cli:emulators
    command: gcloud beta emulators firestore start --host-port=0.0.0.0:8080
    environment:
      - FIRESTORE_PROJECT_ID=${GOOGLE_CLOUD_PROJECT}
      - FIRESTORE_EMULATOR_HOST=0.0.0.0:8080
    ports:
      - "8080:8080"
      - "9099:9099"