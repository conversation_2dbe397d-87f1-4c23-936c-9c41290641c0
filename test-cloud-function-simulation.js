#!/usr/bin/env node

/**
 * Cloud Function Simulation Test
 * 
 * This script simulates the Cloud Functions processing emails and sending
 * progress updates back to the webapp via the progress webhook.
 */

const https = require('https');
const http = require('http');

// Configuration
const WEBHOOK_URL = 'http://localhost:3000/api/emails/progress-webhook';
const USER_ID = 'user_2tNn6UJTsdVAr7uIgVSkswsC6iX';

// Sample email IDs from the latest run
const EMAIL_IDS = [
  '196e91897eb42b80',
  '196e8215d6e35b7f', 
  '196e9b3c34b01f6d',
  '196ea2b202d194dd',
  '196e9735036a286e',
  '196e8790dea6ecf7',
  '196e83e7f3e44d21',
  '196e9cf2437621e8',
  '196e9c92091ebd4f',
  '196e98f60e3f613e',
  '196ea6a96900f8bf',
  '196ea9064d53ba37',
  '196e640f1c119573',
  '196e89b7e86eb7bf',
  '196ea1f88aa021d5',
  '196e89e2f07e429a'
];

// Sample companies and email types for realistic simulation
const SAMPLE_COMPANIES = [
  'Google', 'Microsoft', 'Amazon', 'Apple', 'Meta',
  'Netflix', 'Tesla', 'Spotify', 'Airbnb', 'Uber',
  'LinkedIn', 'Salesforce', 'Adobe', 'Oracle', 'IBM'
];

const EMAIL_TYPES = [
  'job_application_confirmation',
  'interview_invitation', 
  'rejection_notification',
  'follow_up_request',
  'company_newsletter',
  'recruiter_outreach',
  'application_status_update'
];

function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function sendProgressUpdate(messageId, status, processed, total, analysis = null, error = null) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      clerkUserId: USER_ID,
      messageId: messageId,
      status: status,
      current: status === 'processing' ? `Analyzing email ${messageId}...` : 
               status === 'completed' ? `Completed analysis of email ${messageId}` :
               `Error processing email ${messageId}`,
      processed: processed,
      total: total,
      analysis: analysis,
      error: error
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/emails/progress-webhook',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(data)
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        console.log(`✅ Progress update sent for ${messageId}: ${status} (${res.statusCode})`);
        resolve(responseData);
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Error sending progress update for ${messageId}:`, error.message);
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

async function simulateCloudFunctionProcessing() {
  console.log('🚀 Starting Cloud Function Simulation');
  console.log(`📧 Processing ${EMAIL_IDS.length} emails for user ${USER_ID}`);
  console.log('⏱️  Simulating realistic processing delays...\n');

  const total = EMAIL_IDS.length;
  
  for (let i = 0; i < EMAIL_IDS.length; i++) {
    const emailId = EMAIL_IDS[i];
    const processed = i + 1;
    
    // Send processing update
    await sendProgressUpdate(emailId, 'processing', processed - 1, total);
    
    // Simulate processing time (1-3 seconds per email)
    const processingTime = Math.random() * 2000 + 1000;
    await new Promise(resolve => setTimeout(resolve, processingTime));
    
    // Generate realistic analysis result
    const analysis = {
      company_name: getRandomElement(SAMPLE_COMPANIES),
      email_type_category: getRandomElement(EMAIL_TYPES),
      is_related_to_job_search: Math.random() > 0.3 // 70% job search related
    };
    
    // Send completion update
    await sendProgressUpdate(emailId, 'completed', processed, total, analysis);
    
    console.log(`📊 Processed ${processed}/${total} emails`);
  }
  
  console.log('\n🎉 All emails processed successfully!');
  console.log('✅ Cloud Function simulation completed');
}

// Run the simulation
if (require.main === module) {
  simulateCloudFunctionProcessing().catch(console.error);
}

module.exports = { simulateCloudFunctionProcessing };
