#!/bin/bash

# Pre-commit hook to prevent committing sensitive files
# This hook checks for common patterns that might contain secrets

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "🔒 Running security checks..."

# List of sensitive file patterns to check
SENSITIVE_PATTERNS=(
    "\.env$"
    "\.env\.local$"
    "\.env\.production$"
    "\.env\.development$"
    "\.env\.staging$"
    "\.env\.test$"
    "\.key$"
    "\.pem$"
    "service-account.*\.json$"
    "credentials\.json$"
    "token\.json$"
    "firebase-adminsdk-.*\.json$"
)

# List of sensitive content patterns
CONTENT_PATTERNS=(
    "sk-[a-zA-Z0-9]{48}"  # OpenAI API keys (real ones)
    "pk_test_[a-zA-Z0-9]{20,}" # Clerk test keys (real ones)
    "pk_live_[a-zA-Z0-9]{20,}" # Clerk live keys (real ones)
    "sk_test_[a-zA-Z0-9]{20,}" # Clerk test secrets (real ones)
    "sk_live_[a-zA-Z0-9]{20,}" # Clerk live secrets (real ones)
    "AIza[0-9A-Za-z\\-_]{35}" # Google API keys
    "ya29\.[0-9A-Za-z\\-_]+" # Google OAuth tokens
    "AKIA[0-9A-Z]{16}" # AWS access keys
)

# Patterns to exclude (safe template values)
SAFE_PATTERNS=(
    "REPLACE_WITH_YOUR_ACTUAL"
    "YOUR_.*_KEY_HERE"
    "YOUR_.*_SECRET_HERE"
    "TEMPLATE_VALUE"
)

BLOCKED=false

# Check staged files for sensitive file patterns
echo "Checking for sensitive files..."
for pattern in "${SENSITIVE_PATTERNS[@]}"; do
    files=$(git diff --cached --name-only | grep -E "$pattern" || true)
    if [ -n "$files" ]; then
        echo -e "${RED}❌ BLOCKED: Attempting to commit sensitive file(s):${NC}"
        echo "$files"
        BLOCKED=true
    fi
done

# Check staged file contents for sensitive patterns
echo "Checking file contents for secrets..."
staged_files=$(git diff --cached --name-only)
for file in $staged_files; do
    if [ -f "$file" ]; then
        for pattern in "${CONTENT_PATTERNS[@]}"; do
            if grep -qE "$pattern" "$file" 2>/dev/null; then
                # Check if it's a safe template pattern
                is_safe=false
                for safe_pattern in "${SAFE_PATTERNS[@]}"; do
                    if grep -qE "$safe_pattern" "$file" 2>/dev/null; then
                        is_safe=true
                        break
                    fi
                done

                if [ "$is_safe" = false ]; then
                    echo -e "${RED}❌ BLOCKED: File '$file' contains potential secret matching pattern: $pattern${NC}"
                    BLOCKED=true
                fi
            fi
        done
    fi
done

# Check for common secret keywords in file contents
echo "Checking for secret keywords..."
SECRET_KEYWORDS=(
    "password\s*=\s*['\"][^'\"]{8,}['\"]"
    "secret\s*=\s*['\"][^'\"]{8,}['\"]"
    "api_key\s*=\s*['\"][^'\"]{8,}['\"]"
    "private_key"
    "BEGIN PRIVATE KEY"
    "BEGIN RSA PRIVATE KEY"
)

for file in $staged_files; do
    if [ -f "$file" ]; then
        for keyword in "${SECRET_KEYWORDS[@]}"; do
            if grep -qiE "$keyword" "$file" 2>/dev/null; then
                echo -e "${YELLOW}⚠️  WARNING: File '$file' contains potential secret keyword: $keyword${NC}"
                echo -e "${YELLOW}Please review this file carefully before committing.${NC}"
            fi
        done
    fi
done

if [ "$BLOCKED" = true ]; then
    echo ""
    echo -e "${RED}🚨 COMMIT BLOCKED: Sensitive files or content detected!${NC}"
    echo ""
    echo "To fix this:"
    echo "1. Remove sensitive files from staging: git reset HEAD <file>"
    echo "2. Add files to .gitignore if they should never be committed"
    echo "3. Use environment variable templates instead of actual secrets"
    echo "4. Review and clean up any secrets in file contents"
    echo ""
    echo "If you're sure this is safe, you can bypass this check with:"
    echo "git commit --no-verify"
    echo ""
    exit 1
fi

echo "✅ Security checks passed!"
exit 0
