# 🚀 Local Testing Guide - Cloud Functions Architecture

This guide walks you through testing the new Cloud Functions architecture locally using Firebase emulators.

## 📋 Prerequisites

1. **Node.js 18+** (Firebase Functions requirement)
2. **Firebase CLI** (already installed via npm)
3. **All dependencies installed** (`npm install`)

## 🏗️ Architecture Overview

The new architecture consists of:
- **📚 Shared Libraries**: `libs/shared`, `libs/email-core`
- **☁️ Cloud Functions**: 
  - `email-analysis-function` (Pub/Sub triggered)
  - `notification-handler-function` (HTTP triggered)
- **🌐 Next.js Webapp**: Updated to trigger Cloud Functions
- **🔄 Real-time Progress**: Webhook-based updates

## 🚀 Quick Start

### 1. Build Everything
```bash
# Build all libraries and Cloud Functions
npx nx run-many --target=build --projects=shared,email-core,email-analysis-function,notification-handler-function
```

### 2. Start Local Testing Environment
```bash
# Run the comprehensive test setup
./scripts/test-locally.sh
```

This script will:
- ✅ Build all projects
- ✅ Prepare Cloud Functions for deployment
- ✅ Set up environment variables
- ✅ Start Firebase emulators

### 3. Start the Webapp (in another terminal)
```bash
# Start the Next.js development server
npm run dev
```

## 🌐 Access Points

Once everything is running:

| Service | URL | Description |
|---------|-----|-------------|
| **Webapp** | http://localhost:3000 | Main application interface |
| **Firebase Emulator UI** | http://localhost:4000 | Monitor functions, Firestore, Pub/Sub |
| **Cloud Functions** | http://localhost:5001 | Direct function access |
| **Firestore Emulator** | http://localhost:8080 | Database emulator |
| **Pub/Sub Emulator** | http://localhost:8085 | Message queue emulator |

## 🧪 Testing Scenarios

### Scenario 1: End-to-End Email Analysis

1. **Open the webapp** at http://localhost:3000
2. **Navigate to email analysis** page
3. **Select a date range** and click "Analyze Emails"
4. **Watch real-time progress** updates
5. **Monitor in Firebase UI**:
   - Go to http://localhost:4000
   - Check "Functions" tab for execution logs
   - Check "Pub/Sub" tab for message flow

### Scenario 2: Manual Function Testing

```bash
# Test Cloud Functions directly
node scripts/test-functions.js
```

This will:
- 📤 Send test Pub/Sub messages
- 🔍 Trigger email analysis function
- 📊 Test notification handler
- 📋 Show results in emulator UI

### Scenario 3: Gmail Notification Simulation

```bash
# Simulate Gmail push notification
curl -X POST http://localhost:5001/ddjs-dev-458016/us-central1/handleGmailNotification \
  -H "Content-Type: application/json" \
  -d '{
    "message": {
      "data": "'$(echo '{"emailAddress":"<EMAIL>","historyId":"12345"}' | base64)'"
    }
  }'
```

## 📊 Monitoring & Debugging

### Firebase Emulator UI (http://localhost:4000)

**Functions Tab:**
- View function execution logs
- See invocation history
- Monitor performance metrics

**Pub/Sub Tab:**
- Track message publishing
- Monitor topic subscriptions
- View message delivery status

**Firestore Tab:**
- Inspect database collections
- View document changes
- Monitor read/write operations

### Webapp Console

- Real-time progress updates via Socket.IO
- API response monitoring
- Client-side error tracking

## 🔧 Environment Variables

The test script automatically sets:

```bash
export FIRESTORE_EMULATOR_HOST="localhost:8080"
export PUBSUB_EMULATOR_HOST="localhost:8085"
export FUNCTIONS_EMULATOR_HOST="localhost:5001"
export WEBAPP_PROGRESS_WEBHOOK_URL="http://localhost:3000/api/emails/progress-webhook"
```

## 🐛 Troubleshooting

### Build Issues
```bash
# Clean and rebuild
npx nx reset
npx nx run-many --target=build --projects=shared,email-core,email-analysis-function,notification-handler-function
```

### Emulator Issues
```bash
# Stop all emulators
firebase emulators:stop

# Clear emulator data
rm -rf .firebase/

# Restart emulators
./scripts/test-locally.sh
```

### Function Deployment Issues
```bash
# Check function build output
ls -la dist/apps/email-analysis-function/
ls -la dist/apps/notification-handler-function/

# Verify package.json exists in function directories
cat dist/apps/email-analysis-function/package.json
```

### Progress Updates Not Working
1. Check webapp is running on port 3000
2. Verify webhook URL in environment variables
3. Check browser console for Socket.IO connection
4. Monitor function logs for webhook call attempts

## 📝 Test Checklist

- [ ] All projects build successfully
- [ ] Firebase emulators start without errors
- [ ] Webapp loads at http://localhost:3000
- [ ] Emulator UI accessible at http://localhost:4000
- [ ] Email analysis triggers Cloud Functions
- [ ] Real-time progress updates work
- [ ] Function logs appear in emulator UI
- [ ] Pub/Sub messages flow correctly
- [ ] Webhook calls reach the webapp

## 🎯 Expected Behavior

### Successful Email Analysis Flow:

1. **User triggers analysis** in webapp
2. **Webapp publishes Pub/Sub messages** for each email
3. **Cloud Functions receive messages** and process emails
4. **Functions send progress updates** via webhook
5. **Webapp displays real-time progress** to user
6. **Analysis completes** with final status update

### Key Indicators of Success:

- ✅ No errors in function logs
- ✅ Pub/Sub messages delivered
- ✅ Progress updates appear in webapp
- ✅ Webhook calls successful (200 status)
- ✅ Database updates (if applicable)

## 🚀 Next Steps

Once local testing is successful:

1. **Deploy to staging** environment
2. **Configure production environment variables**
3. **Set up monitoring and alerting**
4. **Perform load testing**
5. **Update CI/CD pipelines**

## 📞 Need Help?

If you encounter issues:

1. Check the **Firebase Emulator UI logs**
2. Review **webapp console errors**
3. Verify **environment variables** are set
4. Ensure **all dependencies** are installed
5. Try **restarting the emulators**

Happy testing! 🎉
