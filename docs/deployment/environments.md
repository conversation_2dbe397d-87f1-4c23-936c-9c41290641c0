# 🌍 Environment Configuration

This document describes the different environments used in the DDJS application and their configurations.

## Environment Overview

The DDJS application uses a multi-environment setup to ensure safe development, testing, and production deployment.

```mermaid
graph TB
    subgraph "Development"
        Local[Local Development<br/>- Firebase Emulators<br/>- Local Database<br/>- Mock Services]
        DevCloud[Development Cloud<br/>- ddjs-dev-458016<br/>- dev.datadrivenjobsearch.com<br/>- Development Firestore]
    end
    
    subgraph "Production"
        ProdCloud[Production Cloud<br/>- data-driven-job-search<br/>- datadrivenjobsearch.com<br/>- Production Firestore]
    end
    
    subgraph "Shared Services"
        Clerk[Clerk Authentication<br/>- Separate instances per env]
        OpenAI[OpenAI API<br/>- Shared with usage tracking]
        Gmail[Gmail API<br/>- Separate OAuth apps]
    end
    
    Local --> DevCloud
    DevCloud --> ProdCloud
    
    Local --> Clerk
    DevCloud --> Clerk
    ProdCloud --> Clerk
    
    DevCloud --> OpenAI
    ProdCloud --> OpenAI
    
    DevCloud --> Gmail
    ProdCloud --> Gmail
```

## Local Development Environment

### Configuration
- **Database**: Firebase Emulators (Firestore + Auth)
- **Functions**: Local Firebase Functions Emulator
- **Domain**: `localhost:3000`
- **Authentication**: Clerk development instance
- **External APIs**: Development/sandbox endpoints where available

### Environment Variables
```bash
# .env.local
NODE_ENV=development
GOOGLE_CLOUD_PROJECT=ddjs-dev-458016

# Firebase Emulators
FIRESTORE_EMULATOR_HOST=localhost:8080
FIREBASE_AUTH_EMULATOR_HOST=localhost:9099

# Clerk Authentication (Development)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# OpenAI API
OPENAI_API_KEY=sk-proj-...

# Application URLs
BASE_URL=http://localhost:3000
WEBAPP_PROGRESS_WEBHOOK_URL=http://localhost:3000/api/emails/progress-webhook

# Gmail API Configuration
GMAIL_PUBSUB_TOPIC=projects/ddjs-dev-458016/topics/gmail-notifications
GMAIL_PUBSUB_SUBSCRIPTION=gmail-notifications-sub
FUNCTIONS_REGION=us-central1
```

### Setup Commands
```bash
# Start local development
./RunLocally.sh

# Or manually:
npm install
npx nx build shared
npx nx build email-core
npx nx build services
firebase emulators:start --only firestore,pubsub &
npm run dev
```

### Features
- **Hot Reload**: Automatic code reloading
- **Emulated Services**: No external dependencies
- **Debug Mode**: Detailed logging and error messages
- **Test Data**: Seeded with sample data for development

## Development Cloud Environment

### Configuration
- **GCP Project**: `ddjs-dev-458016`
- **Domain**: `dev.datadrivenjobsearch.com`
- **Database**: Development Firestore instance
- **Functions**: GCP Cloud Functions (development)
- **Authentication**: Clerk development instance with real OAuth

### Environment Variables
```bash
# Production environment variables for dev deployment
NODE_ENV=production
GOOGLE_CLOUD_PROJECT=ddjs-dev-458016
SECRETS_PROJECT_ID=data-driven-job-search

# Base URL
BASE_URL=https://dev.datadrivenjobsearch.com

# Clerk Authentication (Development)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# OpenAI API (from Secret Manager)
OPENAI_API_KEY=${OPENAI_API_KEY}

# Webhook URLs
WEBAPP_PROGRESS_WEBHOOK_URL=https://dev.datadrivenjobsearch.com/api/emails/progress-webhook

# Gmail API Configuration
GMAIL_PUBSUB_TOPIC=projects/ddjs-dev-458016/topics/gmail-notifications
GMAIL_PUBSUB_SUBSCRIPTION=gmail-notifications-sub
FUNCTIONS_REGION=us-central1
```

### Deployment
```bash
# Deploy to development environment
./scripts/deploy-to-gcp.sh

# Or deploy specific components
./scripts/deploy-webapp-only.sh
./scripts/deploy-functions.sh
```

### Features
- **Real Cloud Services**: Full GCP integration
- **Staging Environment**: Test production-like scenarios
- **Real Authentication**: Actual OAuth flows
- **Performance Testing**: Real-world performance metrics

## Production Environment

### Configuration
- **GCP Project**: `data-driven-job-search`
- **Domain**: `datadrivenjobsearch.com` (future)
- **Database**: Production Firestore instance
- **Functions**: GCP Cloud Functions (production)
- **Authentication**: Clerk production instance

### Environment Variables
```bash
# Production environment variables
NODE_ENV=production
GOOGLE_CLOUD_PROJECT=data-driven-job-search
SECRETS_PROJECT_ID=data-driven-job-search

# Base URL
BASE_URL=https://datadrivenjobsearch.com

# Clerk Authentication (Production)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# OpenAI API (from Secret Manager)
OPENAI_API_KEY=${OPENAI_API_KEY}

# Webhook URLs
WEBAPP_PROGRESS_WEBHOOK_URL=https://datadrivenjobsearch.com/api/emails/progress-webhook

# Gmail API Configuration
GMAIL_PUBSUB_TOPIC=projects/data-driven-job-search/topics/gmail-notifications
GMAIL_PUBSUB_SUBSCRIPTION=gmail-notifications-sub
FUNCTIONS_REGION=us-central1
```

### Deployment Process
1. **Testing**: All tests must pass in development
2. **Code Review**: Pull request approval required
3. **Staging**: Deploy to development environment first
4. **Production**: Manual deployment with approval
5. **Monitoring**: Post-deployment health checks

## Environment-Specific Configurations

### Database Configuration

#### Development
```typescript
const projectId = 'ddjs-dev-458016'
const databaseId = '(default)'
const settings = {
  ignoreUndefinedProperties: true,
  timestampsInSnapshots: true
}
```

#### Production
```typescript
const projectId = 'data-driven-job-search'
const databaseId = '(default)'
const settings = {
  ignoreUndefinedProperties: false,
  timestampsInSnapshots: true
}
```

### Logging Configuration

#### Development
```typescript
const logLevel = 'debug'
const logFormat = 'pretty'
const enableConsoleOutput = true
```

#### Production
```typescript
const logLevel = 'info'
const logFormat = 'json'
const enableConsoleOutput = false
```

### Security Configuration

#### Development
```typescript
const corsOrigins = ['http://localhost:3000', 'https://dev.datadrivenjobsearch.com']
const rateLimiting = {
  enabled: false,
  requests: 1000,
  windowMs: 60000
}
```

#### Production
```typescript
const corsOrigins = ['https://datadrivenjobsearch.com']
const rateLimiting = {
  enabled: true,
  requests: 100,
  windowMs: 60000
}
```

## Secret Management

### Development Secrets
Stored in `.env.local` file (not committed to git):
```bash
CLERK_SECRET_KEY=sk_test_...
OPENAI_API_KEY=sk-proj-...
```

### Production Secrets
Stored in GCP Secret Manager:
```bash
# Create secrets
gcloud secrets create clerk-secret-key --data-file=clerk_secret.txt
gcloud secrets create openai-api-key --data-file=openai_key.txt

# Access in application
const clerkSecret = await secretManager.accessSecretVersion({
  name: 'projects/data-driven-job-search/secrets/clerk-secret-key/versions/latest'
})
```

## Environment Switching

### Automatic Detection
```typescript
// config/environment.ts
export function getEnvironment(): 'local' | 'development' | 'production' {
  if (process.env.FIRESTORE_EMULATOR_HOST) {
    return 'local'
  }
  
  if (process.env.GOOGLE_CLOUD_PROJECT === 'ddjs-dev-458016') {
    return 'development'
  }
  
  return 'production'
}

export function getConfig() {
  const env = getEnvironment()
  
  switch (env) {
    case 'local':
      return localConfig
    case 'development':
      return developmentConfig
    case 'production':
      return productionConfig
  }
}
```

### Manual Override
```bash
# Force production database in development
USE_PRODUCTION_FIRESTORE=true npm run dev

# Use development project in production build
GOOGLE_CLOUD_PROJECT=ddjs-dev-458016 npm run build
```

## Monitoring & Alerting

### Development Environment
- **Logging**: Console output with debug level
- **Monitoring**: Basic health checks
- **Alerting**: Email notifications for critical errors

### Production Environment
- **Logging**: Structured JSON logs to Cloud Logging
- **Monitoring**: Comprehensive metrics and dashboards
- **Alerting**: Multi-channel alerts (email, Slack, SMS)

### Health Check Endpoints
```typescript
// app/api/health/route.ts
export async function GET() {
  const environment = getEnvironment()
  const checks = await Promise.allSettled([
    checkDatabase(),
    checkExternalAPIs(),
    checkSecretAccess()
  ])
  
  return NextResponse.json({
    status: 'healthy',
    environment,
    timestamp: new Date().toISOString(),
    checks: checks.map(result => ({
      status: result.status,
      ...(result.status === 'fulfilled' ? { data: result.value } : { error: result.reason })
    }))
  })
}
```

## Environment Migration

### Data Migration
```bash
# Export from development
gcloud firestore export gs://ddjs-dev-backup/$(date +%Y%m%d)

# Import to production
gcloud firestore import gs://ddjs-dev-backup/20240120 --project=data-driven-job-search
```

### Configuration Migration
```bash
# Copy secrets between projects
gcloud secrets versions access latest --secret="clerk-secret-key" --project=ddjs-dev-458016 | \
gcloud secrets create clerk-secret-key --data-file=- --project=data-driven-job-search
```

## Troubleshooting

### Common Issues

#### Environment Variable Mismatch
```bash
# Check current environment
echo $GOOGLE_CLOUD_PROJECT
echo $NODE_ENV

# Verify secret access
gcloud secrets versions access latest --secret="openai-api-key"
```

#### Database Connection Issues
```bash
# Test Firestore connection
gcloud firestore databases list --project=$GOOGLE_CLOUD_PROJECT

# Test emulator connection
curl http://localhost:8080
```

#### Authentication Problems
```bash
# Verify Clerk configuration
curl -H "Authorization: Bearer $CLERK_SECRET_KEY" https://api.clerk.dev/v1/users

# Test OAuth flow
# Check browser network tab for authentication redirects
```
