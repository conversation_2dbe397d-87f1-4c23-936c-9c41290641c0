# ⚙️ Backend Architecture

This document details the backend architecture of the DDJS application, including Cloud Functions, API design, and service integrations.

## Architecture Overview

The backend follows a serverless, event-driven architecture using Google Cloud Platform services. The system is designed for scalability, reliability, and cost-effectiveness.

### Core Components

```mermaid
graph TB
    subgraph "API Layer"
        NextAPI[Next.js API Routes<br/>- Authentication<br/>- Business Logic<br/>- Real-time Updates]
    end
    
    subgraph "Processing Layer"
        EmailFunc[Email Analysis Function<br/>- Pub/Sub Triggered<br/>- OpenAI Integration<br/>- Gmail API Access]
        
        NotifyFunc[Notification Handler<br/>- HTTP Triggered<br/>- Gmail Webhooks<br/>- Event Publishing]
    end
    
    subgraph "Message Queue"
        PubSub[Google Pub/Sub<br/>- email-analysis-requests<br/>- gmail-notifications]
    end
    
    subgraph "Data Services"
        Firestore[(Firestore Database<br/>- User Data<br/>- Analysis Results<br/>- Job Tracking)]
        
        SecretManager[Secret Manager<br/>- API Keys<br/>- OAuth Secrets<br/>- Configuration]
    end
    
    subgraph "External APIs"
        OpenAI[OpenAI API<br/>GPT-4 Turbo]
        Gmail[Gmail API<br/>Email Access]
        Clerk[Clerk API<br/>Authentication]
    end
    
    NextAPI --> PubSub
    NextAPI --> Firestore
    NextAPI --> SecretManager
    NextAPI --> Clerk
    
    PubSub --> EmailFunc
    PubSub --> NotifyFunc
    
    EmailFunc --> OpenAI
    EmailFunc --> Gmail
    EmailFunc --> Firestore
    EmailFunc --> SecretManager
    
    NotifyFunc --> PubSub
    NotifyFunc --> Firestore
    
    Gmail --> NotifyFunc
```

## Next.js API Routes

### Route Structure
```
/api/
├── emails/
│   ├── analyze/route.ts         # Trigger email analysis
│   ├── estimate/route.ts        # Estimate analysis cost
│   ├── progress/route.ts        # SSE progress stream
│   └── progress-webhook/route.ts # Cloud Function webhook
├── tokens/route.ts              # Token management
├── metrics/route.ts             # Analytics data
└── socket/route.ts              # Socket.IO endpoint info
```

### API Route Implementation

#### Email Analysis Endpoint
```typescript
// app/api/emails/analyze/route.ts
export async function POST(req: NextRequest) {
  // 1. Authentication
  const { userId } = await auth()
  if (!userId) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
  }

  // 2. Input validation
  const { startDate, endDate } = await req.json()
  if (!startDate || !endDate) {
    return NextResponse.json({ message: 'Missing required fields' }, { status: 400 })
  }

  // 3. Business logic
  const authService = new AuthService(logger)
  const gmailService = await authService.getGmailClientForUser(userId)
  const emailIds = await gmailService.getEmailsByDateRange(startDate, endDate)

  // 4. Async processing via Pub/Sub
  const pubsub = new PubSub()
  const analysisPromises = emailIds.map(async (emailId, index) => {
    const message = {
      clerkUserId: userId,
      messageId: emailId.id,
      monitoredEmail: gmailService.getMonitoredEmail(),
      jobId: uuidv4(),
      batchIndex: index,
      totalInBatch: emailIds.length
    }
    
    await pubsub.topic('email-analysis-requests').publishMessage({
      data: Buffer.from(JSON.stringify(message))
    })
  })

  await Promise.all(analysisPromises)
  
  return NextResponse.json({
    message: 'Analysis requests sent successfully',
    totalEmails: emailIds.length
  })
}
```

#### Server-Sent Events for Progress
```typescript
// app/api/emails/progress/route.ts
export function GET(req: NextRequest) {
  const encoder = new TextEncoder()
  
  const stream = new ReadableStream({
    start(controller) {
      // Send initial connection message
      const data = `data: ${JSON.stringify({ status: 'connected' })}\n\n`
      controller.enqueue(encoder.encode(data))
      
      // Set up progress monitoring
      const interval = setInterval(() => {
        // Check for progress updates from database
        // Send updates to client
      }, 1000)
      
      // Cleanup on close
      req.signal.addEventListener('abort', () => {
        clearInterval(interval)
        controller.close()
      })
    }
  })
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  })
}
```

## Cloud Functions

### Email Analysis Function

#### Function Configuration
```typescript
// apps/email-analysis-function/src/main.ts
const functions = require('@google-cloud/functions-framework')

// GCP Cloud Function that triggers on Pub/Sub messages
functions.cloudEvent('analyzeEmail', async (cloudEvent: any) => {
  const messageData = cloudEvent.data?.message?.data
  const decodedData = Buffer.from(messageData, 'base64').toString()
  const { clerkUserId, messageId, monitoredEmail, jobId } = JSON.parse(decodedData)
  
  // Initialize services
  const emailAnalyzer = new EmailAnalyzer()
  const { tokenService, gmailService } = await initializeServicesForUser(clerkUserId)
  
  // Process email
  const analysis = await emailAnalyzer.processEmail(
    { clerkUserId, messageId },
    gmailService,
    tokenService,
    await gmailService.getDDJSLabelIds()
  )
  
  // Update progress
  await updateJobProgress(jobId, {
    current: `Completed email analysis`,
    processed: batchIndex + 1
  })
  
  // Send webhook update
  await sendProgressUpdate(clerkUserId, messageId, 'completed', 'Analysis complete', analysis.parsed)
})
```

#### Service Initialization
```typescript
async function initializeServicesForUser(clerkUserId: string) {
  const authService = new AuthService(logger)
  const tokenService = new TokenService(database, logger)
  
  // Get Gmail service with user's OAuth tokens
  const gmailService = await authService.getGmailClientForUser(clerkUserId)
  
  return { tokenService, gmailService }
}
```

### Notification Handler Function

```typescript
// apps/notification-handler-function/src/main.ts
functions.http('handleGmailNotification', async (req: any, res: any) => {
  if (req.method !== 'POST') {
    res.status(405).send('Method Not Allowed')
    return
  }

  // Parse Gmail webhook payload
  const message = req.body.message
  const decodedData = Buffer.from(message.data, 'base64').toString()
  const { historyId, emailAddress } = JSON.parse(decodedData)

  // Get user mapping
  const userMappingDoc = await database.collection('emailUserMapping')
    .doc(emailAddress.replace(/[^a-zA-Z0-9@._-]/g, '_'))
    .get()

  if (!userMappingDoc.exists) {
    res.status(200).send('No user mapping found')
    return
  }

  const { clerkUserId } = userMappingDoc.data()

  // Check user preferences
  const preferencesDoc = await database.collection('notificationPreferences')
    .doc(clerkUserId)
    .get()

  const preferences = preferencesDoc.exists ? preferencesDoc.data() : {
    automaticAnalysis: false
  }

  if (!preferences.automaticAnalysis) {
    res.status(200).send('Automatic analysis disabled')
    return
  }

  // Publish analysis request
  const emailAnalysisMessage = {
    clerkUserId,
    messageId: `msg_${historyId}`,
    monitoredEmail: emailAddress,
    historyId
  }

  await pubsub.topic('email-analysis-requests').publishMessage({
    data: Buffer.from(JSON.stringify(emailAnalysisMessage))
  })

  res.status(200).send('OK')
})
```

## Service Layer Architecture

### Shared Libraries (Nx Workspace)

#### 1. Shared Library (`libs/shared`)
```typescript
// libs/shared/src/index.ts
export { Database } from './lib/database'
export { Logger } from './lib/logger'
export { sanitizeId, sanitizeEmail } from './lib/sanitize'
export { GMAIL_CONFIG } from './lib/config'
```

#### 2. Email Core Library (`libs/email-core`)
```typescript
// libs/email-core/src/index.ts
export { EmailAnalyzer } from './lib/email-analyzer'
export { OpenAIService } from './lib/openai-service'
export type { EmailContent, EmailAnalysisResult } from './lib/types'
```

#### 3. Services Library (`libs/services`)
```typescript
// libs/services/src/index.ts
export { AuthService } from './lib/auth.service'
export { GmailService } from './lib/gmail.service'
export { TokenService } from './lib/token.service'
export { EmailService } from './lib/email.service'
```

### Service Implementation Patterns

#### Database Service
```typescript
export class Database {
  public db!: admin.firestore.Firestore
  public databaseType!: 'emulator' | 'production' | 'development'

  constructor() {
    this._initializeDatabase()
  }

  private _initializeDatabase(): void {
    const useProduction = process.env['USE_PRODUCTION_FIRESTORE'] === 'true'
    const projectId = useProduction || process.env['NODE_ENV'] === 'production'
      ? 'data-driven-job-search'
      : 'ddjs-dev-458016'

    if (process.env['FIRESTORE_EMULATOR_HOST']) {
      // Emulator configuration
      admin.initializeApp({ projectId })
      const db = admin.firestore()
      db.settings({
        host: process.env['FIRESTORE_EMULATOR_HOST'],
        ssl: false
      })
      this.databaseType = 'emulator'
    } else {
      // Production/Development configuration
      admin.initializeApp({
        credential: admin.credential.applicationDefault(),
        projectId
      })
      this.databaseType = useProduction ? 'production' : 'development'
    }

    this.db = admin.firestore()
  }

  collection(name: string): admin.firestore.CollectionReference {
    return this.db.collection(name)
  }
}
```

#### Gmail Service
```typescript
export class GmailService {
  private gmail: gmail_v1.Gmail
  private monitoredEmail: string

  constructor(auth: OAuth2Client, monitoredEmail: string) {
    this.gmail = google.gmail({ version: 'v1', auth })
    this.monitoredEmail = monitoredEmail
  }

  async getEmailsByDateRange(startDate: string, endDate: string): Promise<EmailId[]> {
    const query = `after:${startDate} before:${endDate}`
    
    const response = await this.gmail.users.messages.list({
      userId: 'me',
      q: query,
      maxResults: 500
    })

    return response.data.messages || []
  }

  async getEmailContent(messageId: string): Promise<EmailContent> {
    const response = await this.gmail.users.messages.get({
      userId: 'me',
      id: messageId,
      format: 'full'
    })

    // Parse email content from Gmail API response
    return this.parseEmailContent(response.data)
  }

  async modifyMessage(messageId: string, options: {
    addLabelIds?: string[]
    removeLabelIds?: string[]
  }): Promise<void> {
    await this.gmail.users.messages.modify({
      userId: 'me',
      id: messageId,
      requestBody: options
    })
  }
}
```

## Error Handling & Resilience

### Retry Logic
```typescript
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      if (attempt === maxRetries) throw error
      
      logger.warn(`Attempt ${attempt} failed, retrying in ${delay}ms`, { error })
      await new Promise(resolve => setTimeout(resolve, delay))
      delay *= 2 // Exponential backoff
    }
  }
  throw new Error('Max retries exceeded')
}
```

### Circuit Breaker Pattern
```typescript
class CircuitBreaker {
  private failures = 0
  private lastFailureTime = 0
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN'
      } else {
        throw new Error('Circuit breaker is OPEN')
      }
    }

    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }
}
```

## Monitoring & Observability

### Structured Logging
```typescript
export class Logger {
  info(message: string, metadata?: Record<string, any>) {
    console.log(JSON.stringify({
      level: 'info',
      message,
      timestamp: new Date().toISOString(),
      ...metadata
    }))
  }

  error(message: string, metadata?: Record<string, any>) {
    console.error(JSON.stringify({
      level: 'error',
      message,
      timestamp: new Date().toISOString(),
      ...metadata
    }))
  }
}
```

### Performance Monitoring
```typescript
async function monitoredOperation<T>(
  operationName: string,
  operation: () => Promise<T>
): Promise<T> {
  const startTime = Date.now()
  
  try {
    const result = await operation()
    const duration = Date.now() - startTime
    
    logger.info(`Operation completed`, {
      operation: operationName,
      duration,
      status: 'success'
    })
    
    return result
  } catch (error) {
    const duration = Date.now() - startTime
    
    logger.error(`Operation failed`, {
      operation: operationName,
      duration,
      status: 'error',
      error: error instanceof Error ? error.message : String(error)
    })
    
    throw error
  }
}
```

## Security Implementation

### Input Validation
```typescript
import { z } from 'zod'

const AnalyzeEmailsSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/)
})

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { startDate, endDate } = AnalyzeEmailsSchema.parse(body)
    // Process validated input
  } catch (error) {
    return NextResponse.json({ message: 'Invalid input' }, { status: 400 })
  }
}
```

### Rate Limiting
```typescript
const rateLimiter = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(userId: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const userLimit = rateLimiter.get(userId)
  
  if (!userLimit || now > userLimit.resetTime) {
    rateLimiter.set(userId, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (userLimit.count >= limit) {
    return false
  }
  
  userLimit.count++
  return true
}
```
