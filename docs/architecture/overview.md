# 🏛️ System Architecture Overview

## High-Level Architecture

The Data Driven Job Search (DDJS) webapp follows a modern, cloud-native architecture built on Google Cloud Platform (GCP). The system is designed for scalability, reliability, and maintainability using microservices patterns and serverless technologies.

## Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        UI[Next.js Web App<br/>React + TypeScript]
        Browser[User Browser]
    end
    
    subgraph "Authentication"
        Clerk[Clerk Auth Service]
        Gmail[Gmail OAuth]
    end
    
    subgraph "Application Layer - GCP Cloud Run"
        WebApp[Next.js Server<br/>Port 8080]
        API[API Routes<br/>/api/*]
        SSE[Server-Sent Events<br/>/api/emails/progress]
    end
    
    subgraph "Processing Layer - GCP Cloud Functions"
        EmailFunc[Email Analysis Function<br/>Pub/Sub Triggered]
        NotifyFunc[Notification Handler<br/>HTTP Triggered]
    end
    
    subgraph "Data Layer"
        Firestore[(Firestore Database)]
        PubSub[Pub/Sub Topics]
        Secrets[Secret Manager]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API<br/>GPT-4]
        GmailAPI[Gmail API]
    end
    
    Browser --> UI
    UI --> Clerk
    Clerk --> Gmail
    UI --> WebApp
    WebApp --> API
    API --> SSE
    API --> PubSub
    PubSub --> EmailFunc
    GmailAPI --> NotifyFunc
    NotifyFunc --> PubSub
    EmailFunc --> Firestore
    EmailFunc --> OpenAI
    EmailFunc --> GmailAPI
    WebApp --> Firestore
    EmailFunc --> Secrets
    WebApp --> Secrets
```

## Core Components

### 1. Frontend Layer
- **Technology**: Next.js 14 with React 18 and TypeScript
- **Styling**: Tailwind CSS with custom design system
- **State Management**: React Context for global state
- **Real-time Updates**: Server-Sent Events (SSE) for progress tracking
- **Authentication**: Clerk React components

### 2. Backend Services

#### Next.js Application Server
- **Runtime**: Node.js 20
- **Deployment**: GCP Cloud Run (containerized)
- **Features**:
  - Server-side rendering (SSR)
  - API routes for business logic
  - Real-time progress streaming
  - Authentication middleware

#### Cloud Functions
- **Email Analysis Function**: Processes individual emails using OpenAI
- **Notification Handler**: Receives Gmail webhook notifications
- **Trigger Types**: Pub/Sub events and HTTP requests
- **Runtime**: Node.js 20

### 3. Data Storage

#### Firestore Database
- **Type**: NoSQL document database
- **Collections**:
  - `users` - User profiles and preferences
  - `emailAnalysis` - AI analysis results
  - `analysisJobs` - Batch processing jobs
  - `notificationPreferences` - User notification settings
  - `emailUserMapping` - Email to user ID mappings

#### Pub/Sub Messaging
- **Topics**:
  - `email-analysis-requests` - Triggers email processing
  - `gmail-notifications` - Gmail webhook events

### 4. External Integrations

#### Authentication (Clerk)
- **OAuth Management**: Handles Google OAuth flow
- **Token Storage**: Manages access and refresh tokens
- **User Management**: Registration, login, session handling

#### AI Processing (OpenAI)
- **Model**: GPT-4 for email analysis
- **Structured Output**: Zod schema validation
- **Token Management**: Usage tracking and limits

#### Email Access (Gmail API)
- **Permissions**: Read/modify email messages
- **Watch Notifications**: Real-time email updates
- **Label Management**: Automatic email categorization

## Data Flow Patterns

### 1. Email Analysis Workflow
```
User Request → API Route → Pub/Sub → Cloud Function → OpenAI → Firestore → SSE Update
```

### 2. Real-time Notifications
```
Gmail → Webhook → Notification Handler → Pub/Sub → Email Analysis → Progress Update
```

### 3. Authentication Flow
```
User → Clerk → Google OAuth → Token Storage → API Access
```

## Deployment Architecture

### Development Environment
- **Project ID**: `ddjs-dev-458016`
- **Domain**: `dev.datadrivenjobsearch.com`
- **Database**: Development Firestore instance

### Production Environment
- **Project ID**: `data-driven-job-search`
- **Database**: Production Firestore instance
- **Secrets**: Stored in GCP Secret Manager

## Security Considerations

### Authentication & Authorization
- **JWT Tokens**: Clerk-managed session tokens
- **API Protection**: All routes require authentication
- **OAuth Scopes**: Minimal required Gmail permissions

### Data Protection
- **Encryption**: All data encrypted at rest and in transit
- **Secret Management**: API keys stored in GCP Secret Manager
- **Access Control**: IAM-based resource permissions

### Network Security
- **HTTPS Only**: All communication encrypted
- **CORS Configuration**: Restricted origins
- **Rate Limiting**: API endpoint protection

## Scalability Features

### Horizontal Scaling
- **Cloud Run**: Auto-scaling based on traffic
- **Cloud Functions**: Concurrent execution
- **Firestore**: Automatic scaling

### Performance Optimization
- **Caching**: Analysis result caching
- **Batch Processing**: Efficient email handling
- **Connection Pooling**: Database optimization

## Monitoring & Observability

### Logging
- **Structured Logging**: JSON format with correlation IDs
- **Cloud Logging**: Centralized log aggregation
- **Error Tracking**: Automatic error reporting

### Metrics
- **Performance Metrics**: Response times and throughput
- **Business Metrics**: Analysis success rates
- **Resource Utilization**: CPU, memory, and storage

## Technology Stack Summary

| Layer | Technology | Purpose |
|-------|------------|---------|
| Frontend | Next.js 14, React 18, TypeScript | User interface and client logic |
| Styling | Tailwind CSS, Radix UI | Design system and components |
| Backend | Node.js 20, Express | Server-side logic and APIs |
| Database | Firestore | Document storage and real-time updates |
| Functions | GCP Cloud Functions | Serverless processing |
| Messaging | Pub/Sub | Asynchronous communication |
| Auth | Clerk | User authentication and OAuth |
| AI | OpenAI GPT-4 | Email content analysis |
| Deployment | Cloud Run, Docker | Container orchestration |
| Monitoring | Cloud Logging, Cloud Monitoring | Observability |

## Next Steps

- [Frontend Architecture Details](./frontend-architecture.md)
- [Backend Architecture Details](./backend-architecture.md)
- [Data Flow Documentation](./data-flow.md)
- [System Diagrams](./diagrams/)
