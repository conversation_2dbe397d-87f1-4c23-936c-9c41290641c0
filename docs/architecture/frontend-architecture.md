# 🎨 Frontend Architecture

This document details the frontend architecture of the DDJS Next.js application, including component structure, state management, and user interface patterns.

## Technology Stack

### Core Framework
- **Next.js 14**: React framework with App Router
- **React 18**: UI library with concurrent features
- **TypeScript**: Type-safe JavaScript development
- **Node.js 20**: Runtime environment

### Styling & UI
- **Tailwind CSS**: Utility-first CSS framework
- **Radix UI**: Accessible component primitives
- **Heroicons**: SVG icon library
- **Geist Font**: Modern typography
- **next-themes**: Dark/light mode support

### State Management
- **React Context**: Global state management
- **React Hooks**: Local component state
- **Server State**: Next.js server components

## Project Structure

```
apps/webapp/src/
├── app/                          # Next.js App Router
│   ├── layout.tsx               # Root layout with providers
│   ├── page.tsx                 # Dashboard home page
│   ├── globals.css              # Global styles and CSS variables
│   ├── providers.tsx            # Context providers wrapper
│   ├── error.tsx                # Global error boundary
│   ├── loading.tsx              # Global loading UI
│   │
│   ├── components/              # Shared components
│   │   ├── Navigation.tsx       # Main navigation bar
│   │   ├── EmailAnalysisForm.tsx
│   │   ├── EmailAnalysisProgress.tsx
│   │   ├── MetricsOverview.tsx
│   │   ├── EmailDateSelector.tsx
│   │   └── ui/                  # Reusable UI components
│   │       ├── Card.tsx
│   │       ├── LineChart.tsx
│   │       ├── Progress.tsx
│   │       ├── Select.tsx
│   │       └── MultiSelect.tsx
│   │
│   ├── api/                     # API routes
│   │   ├── emails/
│   │   │   ├── analyze/route.ts
│   │   │   ├── progress/route.ts
│   │   │   └── progress-webhook/route.ts
│   │   ├── tokens/route.ts
│   │   └── metrics/route.ts
│   │
│   ├── sign-in/[[...sign-in]]/  # Clerk authentication pages
│   ├── sign-up/[[...sign-up]]/
│   ├── analytics/               # Analytics dashboard
│   └── settings/                # User settings
│
├── lib/                         # Utility libraries
│   ├── auth.ts                  # Authentication helpers
│   ├── socketServer.ts          # Socket.IO server setup
│   └── progressUpdates.ts       # Progress update utilities
│
└── config/
    └── config.js                # Application configuration
```

## Component Architecture

### Component Hierarchy

```mermaid
graph TB
    subgraph "Root Layout"
        RootLayout[layout.tsx]
        Providers[providers.tsx]
        Navigation[Navigation.tsx]
    end
    
    subgraph "Page Components"
        HomePage[page.tsx - Dashboard]
        AnalyticsPage[analytics/page.tsx]
        SettingsPage[settings/page.tsx]
    end
    
    subgraph "Feature Components"
        EmailDateSelector[EmailDateSelector.tsx]
        MetricsOverview[MetricsOverview.tsx]
        EmailAnalysisProgress[EmailAnalysisProgress.tsx]
        PollingProgress[PollingEmailAnalysisProgress.tsx]
        SocketProgress[SocketEmailAnalysisProgress.tsx]
    end
    
    subgraph "UI Components"
        Card[Card.tsx]
        LineChart[LineChart.tsx]
        Progress[Progress.tsx]
        Select[Select.tsx]
        MultiSelect[MultiSelect.tsx]
    end
    
    subgraph "Context Providers"
        TokenProvider[TokenContext.tsx]
        SocketProvider[SocketContext.tsx]
        DateRangeProvider[DateRangeContext.tsx]
        ThemeProvider[next-themes]
    end
    
    RootLayout --> Providers
    Providers --> TokenProvider
    Providers --> SocketProvider
    Providers --> DateRangeProvider
    Providers --> ThemeProvider
    
    RootLayout --> Navigation
    RootLayout --> HomePage
    RootLayout --> AnalyticsPage
    RootLayout --> SettingsPage
    
    HomePage --> EmailDateSelector
    HomePage --> MetricsOverview
    HomePage --> EmailAnalysisProgress
    
    EmailAnalysisProgress --> PollingProgress
    EmailAnalysisProgress --> SocketProgress
    
    MetricsOverview --> Card
    MetricsOverview --> LineChart
    EmailAnalysisProgress --> Progress
    EmailDateSelector --> Select
    MetricsOverview --> MultiSelect
```

### Component Patterns

#### 1. Server Components (Default)
```typescript
// app/page.tsx - Server Component
import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'

export const dynamic = 'force-dynamic'

export default async function Home() {
  const { userId } = await auth()
  
  if (!userId) {
    redirect('/sign-in')
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Dashboard</h1>
      <MetricsOverview />
    </div>
  )
}
```

#### 2. Client Components
```typescript
// components/EmailAnalysisProgress.tsx - Client Component
'use client'

import { useState, useEffect } from 'react'
import { useTokens } from '../TokenContext'

export function EmailAnalysisProgress() {
  const [progress, setProgress] = useState(0)
  const { remainingTokens } = useTokens()
  
  // Component logic...
}
```

#### 3. Compound Components
```typescript
// components/ui/Card.tsx
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Card({ className, ...props }: CardProps) {
  return (
    <div
      className={`rounded-lg border bg-card text-card-foreground shadow-sm ${className}`}
      {...props}
    />
  )
}
```

## State Management

### Context Providers

#### 1. Token Context
```typescript
// TokenContext.tsx
interface TokenContextType {
  remainingTokens: number
  totalTokens: number
  isLoading: boolean
  refreshTokens: () => Promise<void>
}

export function TokenProvider({ children }: { children: React.ReactNode }) {
  const [remainingTokens, setRemainingTokens] = useState(0)
  const [totalTokens, setTotalTokens] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  // Token management logic...
  
  return (
    <TokenContext.Provider value={{ remainingTokens, totalTokens, isLoading, refreshTokens }}>
      {children}
    </TokenContext.Provider>
  )
}
```

#### 2. Socket Context
```typescript
// SocketContext.tsx
interface SocketContextType {
  socket: Socket | null
  connected: boolean
  progress: ProgressUpdate | null
}

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [connected, setConnected] = useState(false)
  
  // Socket.IO connection management...
}
```

#### 3. Date Range Context
```typescript
// DateRangeContext.tsx
interface DateRangeContextType {
  startDate: string
  endDate: string
  setDateRange: (start: string, end: string) => void
}
```

### State Flow Diagram

```mermaid
flowchart TD
    subgraph "Global State"
        TokenContext[Token Context<br/>- remainingTokens<br/>- totalTokens<br/>- isLoading]
        SocketContext[Socket Context<br/>- socket connection<br/>- progress updates<br/>- connection status]
        DateContext[Date Range Context<br/>- startDate<br/>- endDate<br/>- setDateRange]
        ThemeContext[Theme Context<br/>- theme mode<br/>- setTheme]
    end
    
    subgraph "Component State"
        LocalState[Local Component State<br/>- form inputs<br/>- UI state<br/>- temporary data]
        ServerState[Server State<br/>- user data<br/>- analysis results<br/>- cached responses]
    end
    
    subgraph "External State"
        ClerkAuth[Clerk Authentication<br/>- user session<br/>- JWT tokens<br/>- user profile]
        APIState[API Responses<br/>- email data<br/>- metrics<br/>- job status]
    end
    
    TokenContext --> LocalState
    SocketContext --> LocalState
    DateContext --> LocalState
    ThemeContext --> LocalState
    
    ClerkAuth --> TokenContext
    APIState --> SocketContext
    ServerState --> LocalState
```

## Real-time Communication

### Server-Sent Events (SSE)
```typescript
// EmailAnalysisProgress.tsx
useEffect(() => {
  const eventSource = new EventSource('/api/emails/progress')
  
  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data)
    setProgress(data)
  }
  
  eventSource.onerror = () => {
    // Handle connection errors
    setRetryCount(prev => prev + 1)
  }
  
  return () => eventSource.close()
}, [])
```

### Socket.IO Integration
```typescript
// SocketContext.tsx
useEffect(() => {
  const socketInstance = io({
    path: '/api/socket',
    auth: { token },
    reconnectionAttempts: 5
  })
  
  socketInstance.on('progress', (data) => {
    setProgress(data)
  })
  
  return () => socketInstance.disconnect()
}, [token])
```

## Styling Architecture

### CSS Custom Properties
```css
/* globals.css */
@layer base {
  :root {
    --background: #ffffff;
    --foreground: #111827;
    --primary: #2563eb;
    --primary-foreground: #ffffff;
    --muted: #9ca3af;
    --muted-foreground: #4b5563;
    --border: #e5e7eb;
    --ring: #2563eb;
  }

  .dark {
    --background: #0f172a;
    --foreground: #f8fafc;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --muted: #334155;
    --muted-foreground: #cbd5e1;
    --border: #334155;
    --ring: #3b82f6;
  }
}
```

### Component Styling Patterns
```typescript
// Consistent styling with Tailwind classes
const cardStyles = "rounded-lg border border-border bg-card text-card-foreground shadow-sm"
const buttonStyles = "inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90"
```

## Performance Optimizations

### Code Splitting
- **Dynamic Imports**: Lazy load heavy components
- **Route-based Splitting**: Automatic with Next.js App Router
- **Component Splitting**: Split large feature components

### Rendering Optimizations
```typescript
// Server Components for static content
export default async function MetricsPage() {
  const metrics = await getMetrics() // Server-side data fetching
  return <MetricsDisplay data={metrics} />
}

// Client Components only when needed
'use client'
export function InteractiveChart({ data }: { data: ChartData }) {
  // Interactive functionality
}
```

### Caching Strategy
- **Next.js Cache**: Automatic caching for static content
- **React Query**: Client-side data caching (if needed)
- **Browser Cache**: Leverage HTTP caching headers

## Error Handling

### Error Boundaries
```typescript
// app/error.tsx
'use client'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px]">
      <h1 className="text-4xl font-bold mb-4">Something went wrong!</h1>
      <button onClick={reset} className="bg-primary text-primary-foreground px-4 py-2 rounded">
        Try again
      </button>
    </div>
  )
}
```

### API Error Handling
```typescript
// Consistent error handling pattern
async function handleApiCall() {
  try {
    const response = await fetch('/api/emails/analyze', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    return await response.json()
  } catch (error) {
    console.error('API call failed:', error)
    throw error
  }
}
```

## Accessibility

### ARIA Implementation
- **Semantic HTML**: Use proper HTML elements
- **ARIA Labels**: Descriptive labels for screen readers
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Proper focus handling

### Radix UI Integration
```typescript
// Accessible components with Radix UI
import * as Select from '@radix-ui/react-select'

export function AccessibleSelect() {
  return (
    <Select.Root>
      <Select.Trigger aria-label="Select option">
        <Select.Value placeholder="Choose..." />
      </Select.Trigger>
      <Select.Content>
        <Select.Item value="option1">Option 1</Select.Item>
      </Select.Content>
    </Select.Root>
  )
}
```

## Testing Strategy

### Component Testing
- **Unit Tests**: Individual component testing
- **Integration Tests**: Component interaction testing
- **E2E Tests**: Full user workflow testing with Playwright

### Testing Patterns
```typescript
// Component test example
import { render, screen } from '@testing-library/react'
import { MetricsOverview } from './MetricsOverview'

test('displays metrics correctly', () => {
  render(<MetricsOverview />)
  expect(screen.getByText('Applications')).toBeInTheDocument()
})
```
