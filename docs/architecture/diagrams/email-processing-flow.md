# 📧 Email Processing Flow Diagrams

This document contains detailed diagrams showing how emails are processed through the DDJS system.

## Complete Email Processing Pipeline

```mermaid
flowchart TD
    subgraph "User Interaction"
        UserRequest[User Selects Date Range]
        UserSubmit[Submit Analysis Request]
    end
    
    subgraph "Frontend Processing"
        Validation[Input Validation]
        TokenCheck[Check User Tokens]
        UIUpdate[Update UI - Processing State]
    end
    
    subgraph "API Layer"
        AnalyzeAPI[/api/emails/analyze]
        EstimateAPI[/api/emails/estimate]
        ProgressAPI[/api/emails/progress]
    end
    
    subgraph "Gmail Integration"
        GmailAuth[Gmail OAuth Token]
        GmailAPI[Gmail API]
        EmailFetch[Fetch Email List]
        EmailContent[Get Email Content]
    end
    
    subgraph "Message Queue"
        PubSubTopic[email-analysis-requests Topic]
        MessagePublish[Publish Analysis Messages]
    end
    
    subgraph "Cloud Function Processing"
        EmailFunction[Email Analysis Function]
        ServiceInit[Initialize Services]
        ContentAnalysis[AI Content Analysis]
        ResultStorage[Store Results]
    end
    
    subgraph "AI Processing"
        OpenAIAPI[OpenAI GPT-4 API]
        StructuredOutput[Structured JSON Response]
        TokenUsage[Track Token Usage]
    end
    
    subgraph "Data Storage"
        Firestore[(Firestore Database)]
        AnalysisCollection[emailAnalysis Collection]
        JobTracking[analysisJobs Collection]
        UserTokens[User Token Balance]
    end
    
    subgraph "Real-time Updates"
        ProgressWebhook[Progress Webhook]
        SSEStream[Server-Sent Events]
        SocketIO[Socket.IO Updates]
    end
    
    %% User Flow
    UserRequest --> Validation
    UserSubmit --> AnalyzeAPI
    
    %% Frontend Processing
    Validation --> TokenCheck
    TokenCheck --> EstimateAPI
    EstimateAPI --> UIUpdate
    
    %% API Processing
    AnalyzeAPI --> GmailAuth
    GmailAuth --> GmailAPI
    GmailAPI --> EmailFetch
    EmailFetch --> MessagePublish
    
    %% Message Queue
    MessagePublish --> PubSubTopic
    PubSubTopic --> EmailFunction
    
    %% Cloud Function Processing
    EmailFunction --> ServiceInit
    ServiceInit --> EmailContent
    EmailContent --> ContentAnalysis
    
    %% AI Processing
    ContentAnalysis --> OpenAIAPI
    OpenAIAPI --> StructuredOutput
    StructuredOutput --> TokenUsage
    
    %% Data Storage
    TokenUsage --> ResultStorage
    ResultStorage --> AnalysisCollection
    ResultStorage --> JobTracking
    ResultStorage --> UserTokens
    
    %% Real-time Updates
    ResultStorage --> ProgressWebhook
    ProgressWebhook --> ProgressAPI
    ProgressAPI --> SSEStream
    ProgressAPI --> SocketIO
    SSEStream --> UIUpdate
    SocketIO --> UIUpdate
    
    %% Styling
    classDef userLayer fill:#e1f5fe
    classDef frontendLayer fill:#f3e5f5
    classDef apiLayer fill:#e8f5e8
    classDef processingLayer fill:#fff3e0
    classDef dataLayer fill:#fce4ec
    classDef aiLayer fill:#f1f8e9
    
    class UserRequest,UserSubmit userLayer
    class Validation,TokenCheck,UIUpdate frontendLayer
    class AnalyzeAPI,EstimateAPI,ProgressAPI apiLayer
    class EmailFunction,ServiceInit,ContentAnalysis,ResultStorage processingLayer
    class Firestore,AnalysisCollection,JobTracking,UserTokens dataLayer
    class OpenAIAPI,StructuredOutput,TokenUsage aiLayer
```

## Email Analysis Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant API as API Route
    participant G as Gmail API
    participant P as Pub/Sub
    participant CF as Cloud Function
    participant AI as OpenAI
    participant DB as Firestore
    participant W as Webhook
    
    Note over U,W: Email Analysis Request Flow
    
    U->>F: Select date range & submit
    F->>API: POST /api/emails/analyze
    
    API->>G: Authenticate with OAuth
    G->>API: Access token
    
    API->>G: Get emails in date range
    G->>API: Email list (IDs only)
    
    API->>DB: Create analysis job
    DB->>API: Job ID
    
    loop For each email
        API->>P: Publish analysis message
        P->>CF: Trigger function
        
        CF->>G: Get email content
        G->>CF: Full email data
        
        CF->>AI: Analyze email content
        AI->>CF: Structured analysis
        
        CF->>DB: Store analysis result
        CF->>W: Send progress update
        W->>F: Real-time update (SSE)
        F->>U: Update progress bar
    end
    
    CF->>DB: Mark job complete
    CF->>W: Send completion update
    W->>F: Final update
    F->>U: Show completion
```

## Gmail API Integration Flow

```mermaid
flowchart TD
    subgraph "Authentication"
        ClerkAuth[Clerk Authentication]
        OAuthToken[OAuth Access Token]
        TokenRefresh[Token Refresh Logic]
    end
    
    subgraph "Gmail API Operations"
        ListMessages[List Messages]
        GetMessage[Get Message Content]
        ModifyLabels[Modify Message Labels]
        WatchMailbox[Watch Mailbox Changes]
    end
    
    subgraph "Data Processing"
        ParseHeaders[Parse Email Headers]
        ExtractContent[Extract Email Body]
        CleanText[Clean & Format Text]
        MetadataExtract[Extract Metadata]
    end
    
    subgraph "Error Handling"
        RateLimitCheck[Rate Limit Check]
        RetryLogic[Exponential Backoff]
        ErrorLogging[Error Logging]
        FallbackStrategy[Fallback Strategy]
    end
    
    ClerkAuth --> OAuthToken
    OAuthToken --> ListMessages
    OAuthToken --> GetMessage
    OAuthToken --> ModifyLabels
    OAuthToken --> WatchMailbox
    
    ListMessages --> ParseHeaders
    GetMessage --> ExtractContent
    ExtractContent --> CleanText
    ParseHeaders --> MetadataExtract
    
    ListMessages --> RateLimitCheck
    GetMessage --> RateLimitCheck
    RateLimitCheck --> RetryLogic
    RetryLogic --> ErrorLogging
    ErrorLogging --> FallbackStrategy
    
    TokenRefresh --> OAuthToken
```

## AI Analysis Pipeline

```mermaid
flowchart TD
    subgraph "Input Processing"
        EmailContent[Raw Email Content]
        ContentCleaning[Clean HTML/Formatting]
        TextExtraction[Extract Plain Text]
        PromptConstruction[Construct AI Prompt]
    end
    
    subgraph "AI Processing"
        OpenAIRequest[OpenAI API Request]
        ModelProcessing[GPT-4 Processing]
        StructuredResponse[Structured JSON Response]
        ValidationCheck[Response Validation]
    end
    
    subgraph "Output Processing"
        SchemaValidation[Zod Schema Validation]
        DataTransformation[Transform to Internal Format]
        ConfidenceScoring[Calculate Confidence Scores]
        ResultStorage[Store Analysis Result]
    end
    
    subgraph "Error Handling"
        PromptInjectionCheck[Prompt Injection Detection]
        ContentFiltering[Content Filtering]
        RetryMechanism[Retry on Failure]
        FallbackAnalysis[Fallback Analysis]
    end
    
    EmailContent --> ContentCleaning
    ContentCleaning --> TextExtraction
    TextExtraction --> PromptConstruction
    PromptConstruction --> PromptInjectionCheck
    
    PromptInjectionCheck --> OpenAIRequest
    OpenAIRequest --> ModelProcessing
    ModelProcessing --> StructuredResponse
    StructuredResponse --> ValidationCheck
    
    ValidationCheck --> SchemaValidation
    SchemaValidation --> DataTransformation
    DataTransformation --> ConfidenceScoring
    ConfidenceScoring --> ResultStorage
    
    ValidationCheck --> ContentFiltering
    ContentFiltering --> RetryMechanism
    RetryMechanism --> FallbackAnalysis
```

## Real-time Progress Updates

```mermaid
sequenceDiagram
    participant CF as Cloud Function
    participant W as Webhook API
    participant SSE as SSE Stream
    participant WS as WebSocket
    participant UI as User Interface
    
    Note over CF,UI: Progress Update Flow
    
    CF->>W: POST /api/emails/progress-webhook
    Note right of CF: {status: "processing", current: "Analyzing email 1 of 10"}
    
    W->>SSE: Broadcast to SSE streams
    W->>WS: Emit to WebSocket connections
    
    SSE->>UI: Server-sent event
    WS->>UI: Socket.IO event
    
    UI->>UI: Update progress bar
    UI->>UI: Update status message
    
    Note over CF,UI: Multiple updates during processing
    
    loop For each email processed
        CF->>W: Progress update
        W->>SSE: Broadcast update
        W->>WS: Emit update
        SSE->>UI: Real-time update
        WS->>UI: Real-time update
        UI->>UI: Increment progress
    end
    
    CF->>W: Final completion update
    Note right of CF: {status: "completed", current: "Analysis complete"}
    
    W->>SSE: Final broadcast
    W->>WS: Final emit
    SSE->>UI: Completion event
    WS->>UI: Completion event
    UI->>UI: Show completion state
```

## Error Handling Flow

```mermaid
flowchart TD
    subgraph "Error Sources"
        APIError[API Errors]
        NetworkError[Network Errors]
        AuthError[Authentication Errors]
        ValidationError[Validation Errors]
        ProcessingError[Processing Errors]
    end
    
    subgraph "Error Detection"
        ErrorCapture[Error Capture]
        ErrorClassification[Error Classification]
        SeverityAssessment[Severity Assessment]
    end
    
    subgraph "Error Handling"
        RetryLogic[Retry Logic]
        FallbackMechanism[Fallback Mechanism]
        UserNotification[User Notification]
        ErrorLogging[Error Logging]
    end
    
    subgraph "Recovery Actions"
        TokenRefresh[Refresh Auth Token]
        AlternativeAPI[Use Alternative API]
        CachedResults[Return Cached Results]
        GracefulDegradation[Graceful Degradation]
    end
    
    APIError --> ErrorCapture
    NetworkError --> ErrorCapture
    AuthError --> ErrorCapture
    ValidationError --> ErrorCapture
    ProcessingError --> ErrorCapture
    
    ErrorCapture --> ErrorClassification
    ErrorClassification --> SeverityAssessment
    
    SeverityAssessment --> RetryLogic
    SeverityAssessment --> FallbackMechanism
    SeverityAssessment --> UserNotification
    SeverityAssessment --> ErrorLogging
    
    RetryLogic --> TokenRefresh
    FallbackMechanism --> AlternativeAPI
    FallbackMechanism --> CachedResults
    UserNotification --> GracefulDegradation
```

## Batch Processing Optimization

```mermaid
flowchart TD
    subgraph "Input Analysis"
        EmailList[Email List]
        CacheCheck[Check Cached Results]
        TokenEstimate[Estimate Token Usage]
        BatchSizing[Determine Batch Size]
    end
    
    subgraph "Batch Processing"
        BatchCreation[Create Processing Batches]
        ParallelProcessing[Parallel Function Execution]
        ProgressTracking[Track Batch Progress]
        ResultAggregation[Aggregate Results]
    end
    
    subgraph "Optimization Strategies"
        LoadBalancing[Load Balancing]
        RateLimit[Rate Limit Management]
        CostOptimization[Cost Optimization]
        PerformanceMonitoring[Performance Monitoring]
    end
    
    subgraph "Quality Control"
        ResultValidation[Validate Results]
        ConsistencyCheck[Consistency Check]
        ErrorRecovery[Error Recovery]
        QualityMetrics[Quality Metrics]
    end
    
    EmailList --> CacheCheck
    CacheCheck --> TokenEstimate
    TokenEstimate --> BatchSizing
    BatchSizing --> BatchCreation
    
    BatchCreation --> ParallelProcessing
    ParallelProcessing --> ProgressTracking
    ProgressTracking --> ResultAggregation
    
    ParallelProcessing --> LoadBalancing
    LoadBalancing --> RateLimit
    RateLimit --> CostOptimization
    CostOptimization --> PerformanceMonitoring
    
    ResultAggregation --> ResultValidation
    ResultValidation --> ConsistencyCheck
    ConsistencyCheck --> ErrorRecovery
    ErrorRecovery --> QualityMetrics
```

## Data Flow Summary

```mermaid
graph LR
    subgraph "Input"
        A[User Request]
        B[Date Range]
    end
    
    subgraph "Processing"
        C[Gmail API]
        D[Email Content]
        E[AI Analysis]
        F[Structured Data]
    end
    
    subgraph "Storage"
        G[Firestore]
        H[Analysis Results]
    end
    
    subgraph "Output"
        I[Real-time Updates]
        J[Dashboard Metrics]
        K[Analytics]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    H --> J
    H --> K
```
