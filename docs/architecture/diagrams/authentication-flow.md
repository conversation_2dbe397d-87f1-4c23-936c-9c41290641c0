# 🔐 Authentication Flow Diagrams

This document contains detailed diagrams showing the authentication and authorization flows in the DDJS system.

## Complete Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant B as Browser
    participant W as WebApp
    participant C as Clerk
    participant G as Google OAuth
    participant GA as Gmail API
    participant F as Firestore
    
    Note over U,F: Initial Authentication Flow
    
    U->>B: Navigate to App
    B->>W: Request Page
    W->>B: Redirect to Sign-in
    B->>C: Load Clerk Sign-in
    
    U->>C: Click "Sign in with Google"
    C->>G: Initiate OAuth Flow
    G->>U: Google Consent Screen
    U->>G: Grant Permissions
    
    Note over G: Permissions Granted:<br/>- Gmail Read/Write<br/>- Profile Information
    
    G->>C: Authorization Code
    C->>G: Exchange for Tokens
    G->>C: Access & Refresh Tokens
    
    C->>F: Store User Profile
    C->>W: Authentication Success
    W->>B: Redirect to Dashboard
    
    Note over U,F: Subsequent API Requests
    
    U->>W: Request Email Analysis
    W->>C: Validate Session
    C->>W: JWT Token
    
    W->>C: Get OAuth Token for Gmail
    C->>W: Gmail Access Token
    W->>GA: API Request with Token
    GA->>W: Email Data
    W->>U: Analysis Results
```

## OAuth Token Management

```mermaid
graph TB
    subgraph "Clerk Token Management"
        ClerkAuth[Clerk Authentication]
        TokenStore[Token Storage]
        RefreshLogic[Token Refresh Logic]
    end
    
    subgraph "Google OAuth Flow"
        GoogleAuth[Google OAuth 2.0]
        ConsentScreen[User Consent]
        TokenExchange[Token Exchange]
    end
    
    subgraph "Application Access"
        WebApp[Next.js WebApp]
        CloudFunc[Cloud Functions]
        GmailAPI[Gmail API]
    end
    
    subgraph "Token Types"
        AccessToken[Access Token<br/>Short-lived (1 hour)]
        RefreshToken[Refresh Token<br/>Long-lived (6 months)]
        JWTToken[JWT Session Token<br/>Clerk managed]
    end
    
    User --> ClerkAuth
    ClerkAuth --> GoogleAuth
    GoogleAuth --> ConsentScreen
    ConsentScreen --> TokenExchange
    
    TokenExchange --> AccessToken
    TokenExchange --> RefreshToken
    TokenExchange --> JWTToken
    
    AccessToken --> TokenStore
    RefreshToken --> TokenStore
    JWTToken --> TokenStore
    
    TokenStore --> RefreshLogic
    RefreshLogic --> GoogleAuth
    
    WebApp --> ClerkAuth
    CloudFunc --> ClerkAuth
    ClerkAuth --> GmailAPI
    
    classDef tokenType fill:#e3f2fd
    classDef clerkService fill:#f3e5f5
    classDef googleService fill:#e8f5e8
    classDef appService fill:#fff3e0
    
    class AccessToken,RefreshToken,JWTToken tokenType
    class ClerkAuth,TokenStore,RefreshLogic clerkService
    class GoogleAuth,ConsentScreen,TokenExchange googleService
    class WebApp,CloudFunc,GmailAPI appService
```

## Session Management Flow

```mermaid
stateDiagram-v2
    [*] --> Unauthenticated
    
    Unauthenticated --> Authenticating : User clicks sign-in
    Authenticating --> GoogleOAuth : Redirect to Google
    GoogleOAuth --> ConsentPending : Show consent screen
    ConsentPending --> TokenExchange : User grants permission
    TokenExchange --> Authenticated : Tokens received
    
    Authenticated --> Active : Valid session
    Active --> TokenRefresh : Token expires
    TokenRefresh --> Active : Refresh successful
    TokenRefresh --> Unauthenticated : Refresh failed
    
    Active --> Unauthenticated : User signs out
    Active --> Unauthenticated : Session timeout
    
    note right of GoogleOAuth
        Scopes requested:
        - https://www.googleapis.com/auth/gmail.readonly
        - https://www.googleapis.com/auth/gmail.modify
        - https://www.googleapis.com/auth/userinfo.email
        - https://www.googleapis.com/auth/userinfo.profile
    end note
    
    note right of TokenExchange
        Clerk stores:
        - Access token
        - Refresh token
        - Token expiry
        - User profile
    end note
```

## API Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant M as Auth Middleware
    participant CL as Clerk
    participant API as API Handler
    participant DB as Database
    
    C->>M: API Request + JWT
    
    alt Valid JWT
        M->>CL: Verify JWT
        CL->>M: User ID + Claims
        M->>API: Authorized Request
        API->>DB: Query with User Context
        DB->>API: User Data
        API->>C: Success Response
    else Invalid JWT
        M->>C: 401 Unauthorized
    else Expired JWT
        M->>CL: Refresh Token
        alt Refresh Success
            CL->>M: New JWT
            M->>C: 401 + New Token Header
        else Refresh Failed
            M->>C: 401 + Redirect to Login
        end
    end
```

## Gmail API Authorization

```mermaid
graph TB
    subgraph "Authorization Layers"
        UserAuth[User Authentication<br/>Clerk JWT]
        OAuthToken[OAuth Access Token<br/>Gmail API]
        APIKey[API Key<br/>Google Cloud Project]
    end
    
    subgraph "Permission Scopes"
        ReadScope[gmail.readonly<br/>Read email content]
        ModifyScope[gmail.modify<br/>Add/remove labels]
        ProfileScope[userinfo.profile<br/>User information]
    end
    
    subgraph "API Operations"
        ListEmails[List Messages]
        GetEmail[Get Message Content]
        ModifyLabels[Modify Message Labels]
        WatchMailbox[Set Push Notifications]
    end
    
    UserAuth --> OAuthToken
    OAuthToken --> ReadScope
    OAuthToken --> ModifyScope
    OAuthToken --> ProfileScope
    
    ReadScope --> ListEmails
    ReadScope --> GetEmail
    ModifyScope --> ModifyLabels
    ModifyScope --> WatchMailbox
    
    APIKey --> ListEmails
    APIKey --> GetEmail
    APIKey --> ModifyLabels
    APIKey --> WatchMailbox
```

## Security Token Flow

```mermaid
flowchart TD
    subgraph "Token Lifecycle"
        Issue[Token Issued]
        Store[Secure Storage]
        Use[Token Usage]
        Validate[Validation]
        Refresh[Token Refresh]
        Expire[Token Expiry]
        Revoke[Token Revocation]
    end
    
    subgraph "Security Measures"
        Encrypt[Encryption at Rest]
        HTTPS[HTTPS Transport]
        Rotation[Regular Rotation]
        Audit[Audit Logging]
    end
    
    Issue --> Store
    Store --> Use
    Use --> Validate
    Validate --> Refresh
    Refresh --> Use
    Use --> Expire
    Expire --> Issue
    
    Store --> Encrypt
    Use --> HTTPS
    Refresh --> Rotation
    Validate --> Audit
    
    classDef security fill:#ffebee
    classDef lifecycle fill:#e8f5e8
    
    class Encrypt,HTTPS,Rotation,Audit security
    class Issue,Store,Use,Validate,Refresh,Expire,Revoke lifecycle
```

## Multi-Environment Authentication

```mermaid
graph TB
    subgraph "Development Environment"
        DevClerk[Clerk Dev Instance]
        DevKeys[Dev OAuth Keys]
        DevUsers[Test Users]
    end
    
    subgraph "Production Environment"
        ProdClerk[Clerk Prod Instance]
        ProdKeys[Prod OAuth Keys]
        ProdUsers[Real Users]
    end
    
    subgraph "Shared Configuration"
        ClerkConfig[Clerk Configuration]
        OAuthScopes[OAuth Scopes]
        SecurityPolicies[Security Policies]
    end
    
    subgraph "Environment Isolation"
        DevSecrets[Dev Secret Manager]
        ProdSecrets[Prod Secret Manager]
        EnvVars[Environment Variables]
    end
    
    ClerkConfig --> DevClerk
    ClerkConfig --> ProdClerk
    
    OAuthScopes --> DevKeys
    OAuthScopes --> ProdKeys
    
    SecurityPolicies --> DevClerk
    SecurityPolicies --> ProdClerk
    
    DevClerk --> DevSecrets
    ProdClerk --> ProdSecrets
    
    DevSecrets --> EnvVars
    ProdSecrets --> EnvVars
```

## Error Handling in Authentication

```mermaid
flowchart TD
    AuthRequest[Authentication Request]
    
    AuthRequest --> ValidateInput{Valid Input?}
    ValidateInput -->|No| InputError[400 Bad Request]
    ValidateInput -->|Yes| CheckSession{Session Valid?}
    
    CheckSession -->|No| RedirectLogin[Redirect to Login]
    CheckSession -->|Yes| CheckToken{Token Valid?}
    
    CheckToken -->|No| RefreshToken{Can Refresh?}
    CheckToken -->|Yes| Authorized[Authorized Access]
    
    RefreshToken -->|No| ForceLogin[Force Re-login]
    RefreshToken -->|Yes| NewToken[Issue New Token]
    NewToken --> Authorized
    
    RefreshToken -->|Error| TokenError[Token Service Error]
    CheckToken -->|Error| ValidationError[Validation Error]
    
    InputError --> LogError[Log Error]
    TokenError --> LogError
    ValidationError --> LogError
    
    LogError --> ReturnError[Return Error Response]
```
