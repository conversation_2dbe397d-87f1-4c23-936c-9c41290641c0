# 🏗️ System Architecture Diagrams

This document contains visual representations of the DDJS system architecture using Mermaid diagrams.

## Complete System Architecture

```mermaid
graph TB
    subgraph "User Interface"
        Browser[Web Browser]
        Mobile[Mobile Browser]
    end
    
    subgraph "CDN & Load Balancing"
        CloudFlare[Cloudflare CDN]
        LB[GCP Load Balancer]
    end
    
    subgraph "Authentication Layer"
        Clerk[Clerk Authentication]
        OAuth[Google OAuth 2.0]
    end
    
    subgraph "Application Tier - Cloud Run"
        WebApp[Next.js Application<br/>- SSR/SSG<br/>- API Routes<br/>- Real-time SSE]
        
        subgraph "API Endpoints"
            AuthAPI[/api/auth/*]
            EmailAPI[/api/emails/*]
            MetricsAPI[/api/metrics]
            TokenAPI[/api/tokens]
            ProgressAPI[/api/emails/progress]
        end
    end
    
    subgraph "Processing Tier - Cloud Functions"
        EmailAnalysis[Email Analysis Function<br/>- OpenAI Integration<br/>- Gmail API<br/>- Pub/Sub Triggered]
        
        NotificationHandler[Notification Handler<br/>- Gmail Webhooks<br/>- HTTP Triggered<br/>- Pub/Sub Publisher]
    end
    
    subgraph "Message Queue"
        PubSubAnalysis[email-analysis-requests<br/>Topic]
        PubSubNotify[gmail-notifications<br/>Topic]
    end
    
    subgraph "Data Layer"
        Firestore[(Firestore Database<br/>- User Data<br/>- Email Analysis<br/>- Job Tracking)]
        
        SecretManager[Secret Manager<br/>- API Keys<br/>- OAuth Secrets<br/>- Configuration]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API<br/>GPT-4 Turbo]
        Gmail[Gmail API<br/>- Email Access<br/>- Push Notifications]
        ClerkService[Clerk Service<br/>- User Management<br/>- OAuth Token Storage]
    end
    
    subgraph "Monitoring & Logging"
        CloudLogging[Cloud Logging]
        CloudMonitoring[Cloud Monitoring]
        ErrorReporting[Error Reporting]
    end
    
    %% User Flow
    Browser --> CloudFlare
    Mobile --> CloudFlare
    CloudFlare --> LB
    LB --> WebApp
    
    %% Authentication Flow
    WebApp --> Clerk
    Clerk --> OAuth
    OAuth --> Gmail
    Clerk --> ClerkService
    
    %% API Flow
    WebApp --> AuthAPI
    WebApp --> EmailAPI
    WebApp --> MetricsAPI
    WebApp --> TokenAPI
    WebApp --> ProgressAPI
    
    %% Processing Flow
    EmailAPI --> PubSubAnalysis
    PubSubAnalysis --> EmailAnalysis
    EmailAnalysis --> OpenAI
    EmailAnalysis --> Gmail
    EmailAnalysis --> Firestore
    
    %% Notification Flow
    Gmail --> NotificationHandler
    NotificationHandler --> PubSubAnalysis
    
    %% Data Access
    WebApp --> Firestore
    EmailAnalysis --> SecretManager
    WebApp --> SecretManager
    
    %% Monitoring
    WebApp --> CloudLogging
    EmailAnalysis --> CloudLogging
    NotificationHandler --> CloudLogging
    CloudLogging --> CloudMonitoring
    CloudLogging --> ErrorReporting
    
    %% Styling
    classDef userLayer fill:#e1f5fe
    classDef authLayer fill:#f3e5f5
    classDef appLayer fill:#e8f5e8
    classDef processLayer fill:#fff3e0
    classDef dataLayer fill:#fce4ec
    classDef externalLayer fill:#f1f8e9
    classDef monitorLayer fill:#e0f2f1
    
    class Browser,Mobile userLayer
    class Clerk,OAuth authLayer
    class WebApp,AuthAPI,EmailAPI,MetricsAPI,TokenAPI,ProgressAPI appLayer
    class EmailAnalysis,NotificationHandler,PubSubAnalysis,PubSubNotify processLayer
    class Firestore,SecretManager dataLayer
    class OpenAI,Gmail,ClerkService externalLayer
    class CloudLogging,CloudMonitoring,ErrorReporting monitorLayer
```

## Component Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant W as WebApp
    participant C as Clerk
    participant F as Firestore
    participant P as Pub/Sub
    participant E as Email Function
    participant O as OpenAI
    participant G as Gmail API
    
    U->>W: Login Request
    W->>C: Authenticate User
    C->>W: JWT Token
    
    U->>W: Request Email Analysis
    W->>F: Check User Tokens
    F->>W: Token Count
    
    W->>P: Publish Analysis Request
    P->>E: Trigger Function
    
    E->>G: Fetch Email Content
    G->>E: Email Data
    
    E->>O: Analyze Email
    O->>E: Analysis Result
    
    E->>F: Store Analysis
    E->>W: Progress Update (Webhook)
    W->>U: Real-time Update (SSE)
    
    Note over U,G: Process repeats for each email
```

## Data Flow Architecture

```mermaid
flowchart TD
    subgraph "Input Sources"
        UserInput[User Date Selection]
        GmailWebhook[Gmail Push Notifications]
    end
    
    subgraph "Processing Pipeline"
        Validation[Input Validation]
        TokenCheck[Token Verification]
        EmailFetch[Email Retrieval]
        AIAnalysis[OpenAI Processing]
        DataStorage[Result Storage]
    end
    
    subgraph "Output Channels"
        SSEStream[Server-Sent Events]
        Dashboard[Analytics Dashboard]
        APIResponse[REST API Response]
    end
    
    subgraph "Data Stores"
        UserDB[(User Profiles)]
        AnalysisDB[(Email Analysis)]
        JobDB[(Processing Jobs)]
        CacheDB[(Analysis Cache)]
    end
    
    UserInput --> Validation
    GmailWebhook --> Validation
    
    Validation --> TokenCheck
    TokenCheck --> EmailFetch
    EmailFetch --> AIAnalysis
    AIAnalysis --> DataStorage
    
    DataStorage --> UserDB
    DataStorage --> AnalysisDB
    DataStorage --> JobDB
    DataStorage --> CacheDB
    
    DataStorage --> SSEStream
    AnalysisDB --> Dashboard
    AnalysisDB --> APIResponse
    
    CacheDB --> AIAnalysis
```

## Deployment Architecture

```mermaid
graph TB
    subgraph "Development Environment"
        DevProject[ddjs-dev-458016]
        DevDomain[dev.datadrivenjobsearch.com]
        DevFirestore[(Dev Firestore)]
        DevSecrets[Dev Secret Manager]
    end
    
    subgraph "Production Environment"
        ProdProject[data-driven-job-search]
        ProdDomain[datadrivenjobsearch.com]
        ProdFirestore[(Prod Firestore)]
        ProdSecrets[Prod Secret Manager]
    end
    
    subgraph "CI/CD Pipeline"
        GitHub[GitHub Repository]
        Build[Build Process]
        Test[Test Suite]
        Deploy[Deployment Scripts]
    end
    
    subgraph "Shared Services"
        Clerk[Clerk Authentication]
        OpenAI[OpenAI API]
        Namecheap[Namecheap DNS]
    end
    
    GitHub --> Build
    Build --> Test
    Test --> Deploy
    
    Deploy --> DevProject
    Deploy --> ProdProject
    
    DevProject --> DevDomain
    DevProject --> DevFirestore
    DevProject --> DevSecrets
    
    ProdProject --> ProdDomain
    ProdProject --> ProdFirestore
    ProdProject --> ProdSecrets
    
    DevProject --> Clerk
    ProdProject --> Clerk
    DevProject --> OpenAI
    ProdProject --> OpenAI
    
    DevDomain --> Namecheap
    ProdDomain --> Namecheap
```

## Security Architecture

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Network Security"
            HTTPS[HTTPS/TLS 1.3]
            CORS[CORS Policy]
            Firewall[Cloud Armor]
        end
        
        subgraph "Authentication"
            JWT[JWT Tokens]
            OAuth2[OAuth 2.0]
            SessionMgmt[Session Management]
        end
        
        subgraph "Authorization"
            RBAC[Role-Based Access]
            APIKeys[API Key Management]
            Scopes[OAuth Scopes]
        end
        
        subgraph "Data Protection"
            Encryption[Data Encryption]
            SecretMgmt[Secret Management]
            Audit[Audit Logging]
        end
    end
    
    subgraph "Threat Mitigation"
        RateLimit[Rate Limiting]
        InputValid[Input Validation]
        SQLInject[SQL Injection Prevention]
        XSS[XSS Protection]
    end
    
    HTTPS --> JWT
    JWT --> RBAC
    RBAC --> Encryption
    
    CORS --> OAuth2
    OAuth2 --> APIKeys
    APIKeys --> SecretMgmt
    
    Firewall --> SessionMgmt
    SessionMgmt --> Scopes
    Scopes --> Audit
    
    RateLimit --> InputValid
    InputValid --> SQLInject
    SQLInject --> XSS
```

## Monitoring Architecture

```mermaid
graph TB
    subgraph "Application Metrics"
        AppLogs[Application Logs]
        ErrorLogs[Error Logs]
        PerfMetrics[Performance Metrics]
        BusinessMetrics[Business Metrics]
    end
    
    subgraph "Infrastructure Metrics"
        CPUMetrics[CPU Usage]
        MemoryMetrics[Memory Usage]
        NetworkMetrics[Network I/O]
        StorageMetrics[Storage Usage]
    end
    
    subgraph "Collection Layer"
        CloudLogging[Cloud Logging]
        CloudMonitoring[Cloud Monitoring]
        ErrorReporting[Error Reporting]
    end
    
    subgraph "Analysis & Alerting"
        LogAnalysis[Log Analysis]
        MetricAnalysis[Metric Analysis]
        Alerting[Alert Manager]
        Dashboard[Monitoring Dashboard]
    end
    
    AppLogs --> CloudLogging
    ErrorLogs --> ErrorReporting
    PerfMetrics --> CloudMonitoring
    BusinessMetrics --> CloudMonitoring
    
    CPUMetrics --> CloudMonitoring
    MemoryMetrics --> CloudMonitoring
    NetworkMetrics --> CloudMonitoring
    StorageMetrics --> CloudMonitoring
    
    CloudLogging --> LogAnalysis
    CloudMonitoring --> MetricAnalysis
    ErrorReporting --> Alerting
    
    LogAnalysis --> Dashboard
    MetricAnalysis --> Dashboard
    Alerting --> Dashboard
```
