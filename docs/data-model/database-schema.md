# 🗄️ Database Schema Documentation

This document describes the Firestore database schema used in the DDJS application, including collections, document structures, and relationships.

## Database Overview

The application uses Google Cloud Firestore, a NoSQL document database. Data is organized into collections containing documents with flexible schemas.

### Database Configuration
- **Type**: Firestore (NoSQL Document Database)
- **Development Project**: `ddjs-dev-458016`
- **Production Project**: `data-driven-job-search`
- **Emulator**: Available for local development

## Collections Schema

### 1. `users` Collection

Stores user profile information and preferences.

```typescript
interface UserDocument {
  userId: string;           // Clerk user ID (document ID)
  email: string;           // Primary email address
  createdAt: string;       // ISO timestamp
  updatedAt: string;       // ISO timestamp
  profile?: {
    firstName?: string;
    lastName?: string;
    imageUrl?: string;
  };
  preferences?: {
    emailNotifications: boolean;
    automaticAnalysis: boolean;
    theme: 'light' | 'dark' | 'system';
  };
  subscription?: {
    plan: 'free' | 'premium';
    tokensRemaining: number;
    tokensTotal: number;
    renewalDate?: string;
  };
}
```

**Example Document:**
```json
{
  "userId": "user_2abc123def456",
  "email": "<EMAIL>",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-20T14:22:00Z",
  "profile": {
    "firstName": "John",
    "lastName": "Doe",
    "imageUrl": "https://img.clerk.com/..."
  },
  "preferences": {
    "emailNotifications": true,
    "automaticAnalysis": false,
    "theme": "system"
  },
  "subscription": {
    "plan": "free",
    "tokensRemaining": 4850,
    "tokensTotal": 5000,
    "renewalDate": "2024-02-15T00:00:00Z"
  }
}
```

### 2. `emailAnalysis` Collection

Stores AI analysis results for processed emails.

```typescript
interface EmailAnalysisDocument {
  // Document ID format: {clerkUserId}_{sanitizedEmail}_{messageId}
  clerkUserId: string;
  messageId: string;
  monitoredEmail: string;
  date: string;                    // Email date
  version: string;                 // Analysis version
  lastUpdated: string;             // ISO timestamp
  parsed: {
    was_prompt_injection_detected_in_the_email: boolean;
    is_related_to_job_search: boolean;
    company_name: string;
    role_name: string;
    ATS_name: string;
    email_type_category: 'application_confirmation' | 'interview_invitation' | 
                        'rejection' | 'offer' | 'follow_up' | 'other';
    what_does_the_receiver_of_the_email_need_to_do_in_less_than_10_words: string;
  };
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}
```

**Example Document:**
```json
{
  "clerkUserId": "user_2abc123def456",
  "messageId": "msg_18f2a3b4c5d6e7f8",
  "monitoredEmail": "<EMAIL>",
  "date": "2024-01-20T09:15:00Z",
  "version": "v2.1",
  "lastUpdated": "2024-01-20T09:20:00Z",
  "parsed": {
    "was_prompt_injection_detected_in_the_email": false,
    "is_related_to_job_search": true,
    "company_name": "TechCorp Inc",
    "role_name": "Senior Software Engineer",
    "ATS_name": "Greenhouse",
    "email_type_category": "interview_invitation",
    "what_does_the_receiver_of_the_email_need_to_do_in_less_than_10_words": "Schedule interview by replying with availability"
  },
  "usage": {
    "prompt_tokens": 1250,
    "completion_tokens": 180,
    "total_tokens": 1430
  }
}
```

### 3. `analysisJobs` Collection

Tracks batch email analysis jobs and their progress.

```typescript
interface AnalysisJobDocument {
  jobId: string;               // UUID (document ID)
  clerkUserId: string;
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  current: string;             // Current status message
  total: number;               // Total emails to process
  processed: number;           // Emails processed so far
  cachedCount: number;         // Emails with cached results
  newAnalysisCount: number;    // Emails requiring new analysis
  remainingTokens: number;     // User's remaining tokens
  startDate: string;           // Analysis date range start
  endDate: string;             // Analysis date range end
  monitoredEmail: string;      // Email being monitored
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  error?: string;              // Error message if failed
}
```

### 4. `notificationPreferences` Collection

User preferences for email notifications and automatic processing.

```typescript
interface NotificationPreferencesDocument {
  // Document ID: clerkUserId
  clerkUserId: string;
  automaticAnalysis: boolean;
  skipInbox: {
    confirmations: boolean;
    rejections: boolean;
  };
  emailNotifications: {
    analysisComplete: boolean;
    weeklyDigest: boolean;
    errorAlerts: boolean;
  };
  updatedAt: string;
}
```

### 5. `emailUserMapping` Collection

Maps email addresses to Clerk user IDs for webhook processing.

```typescript
interface EmailUserMappingDocument {
  // Document ID: sanitized email address
  emailAddress: string;
  clerkUserId: string;
  createdAt: string;
  lastUsed: string;
  active: boolean;
}
```

### 6. `watchDetails` Collection

Stores Gmail watch notification details for push notifications.

```typescript
interface WatchDetailsDocument {
  // Document ID: clerkUserId
  clerkUserId: string;
  emailAddress: string;
  historyId: string;
  expiration: number;          // Unix timestamp
  active: boolean;
  renewalAttempts: number;
  lastRenewal: string;
  createdAt: string;
}
```

## Indexes

The following Firestore indexes are configured for optimal query performance:

### Composite Indexes

```json
{
  "indexes": [
    {
      "collectionGroup": "emailAnalysis",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "clerkUserId", "order": "ASCENDING" },
        { "fieldPath": "date", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "emailAnalysis",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "clerkUserId", "order": "ASCENDING" },
        { "fieldPath": "parsed.is_related_to_job_search", "order": "ASCENDING" },
        { "fieldPath": "date", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "analysisJobs",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "clerkUserId", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "watchDetails",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "active", "order": "ASCENDING" },
        { "fieldPath": "expiration", "order": "ASCENDING" },
        { "fieldPath": "renewalAttempts", "order": "ASCENDING" }
      ]
    }
  ]
}
```

## Data Relationships

### User-Centric Design
```mermaid
erDiagram
    USERS ||--o{ EMAIL_ANALYSIS : "has many"
    USERS ||--o{ ANALYSIS_JOBS : "creates"
    USERS ||--o| NOTIFICATION_PREFERENCES : "has one"
    USERS ||--o| EMAIL_USER_MAPPING : "maps to"
    USERS ||--o| WATCH_DETAILS : "has one"
    
    USERS {
        string userId PK
        string email
        object profile
        object preferences
        object subscription
    }
    
    EMAIL_ANALYSIS {
        string documentId PK
        string clerkUserId FK
        string messageId
        string monitoredEmail
        object parsed
        object usage
    }
    
    ANALYSIS_JOBS {
        string jobId PK
        string clerkUserId FK
        string status
        number total
        number processed
    }
    
    NOTIFICATION_PREFERENCES {
        string clerkUserId PK
        boolean automaticAnalysis
        object skipInbox
        object emailNotifications
    }
    
    EMAIL_USER_MAPPING {
        string emailAddress PK
        string clerkUserId FK
        boolean active
    }
    
    WATCH_DETAILS {
        string clerkUserId PK
        string emailAddress
        string historyId
        number expiration
        boolean active
    }
```

## Security Rules

Firestore security rules ensure data access control:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Email analysis restricted to document owner
    match /emailAnalysis/{docId} {
      allow read, write: if 
        request.auth.uid == docId.split('_')[0] // Clerk user ID match
        && resource.data.monitoredEmail == request.auth.token.email; // Email ownership
    }
    
    // Analysis jobs restricted to creator
    match /analysisJobs/{jobId} {
      allow read, write: if request.auth.uid == resource.data.clerkUserId;
    }
    
    // Notification preferences restricted to user
    match /notificationPreferences/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Email user mapping restricted to mapped user
    match /emailUserMapping/{email} {
      allow read, write: if request.auth.uid == resource.data.clerkUserId;
    }
    
    // Watch details restricted to user
    match /watchDetails/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## Data Migration & Versioning

### Version Control
- **Analysis Version**: Stored in `emailAnalysis.version` field
- **Schema Changes**: Handled through application logic
- **Backward Compatibility**: Maintained for at least 2 versions

### Migration Strategy
1. **Additive Changes**: New fields added with default values
2. **Breaking Changes**: Versioned with migration scripts
3. **Data Cleanup**: Automated cleanup of old versions

## Performance Considerations

### Query Optimization
- Use composite indexes for multi-field queries
- Limit query results with pagination
- Cache frequently accessed data

### Storage Optimization
- Use subcollections for large datasets
- Implement data archiving for old records
- Compress large text fields when possible

### Cost Management
- Monitor read/write operations
- Implement query result caching
- Use batch operations for bulk updates
