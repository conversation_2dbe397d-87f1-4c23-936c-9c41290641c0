# 🔌 API Interfaces & Data Contracts

This document defines the TypeScript interfaces and data contracts used throughout the DDJS application.

## Core Data Types

### User & Authentication

```typescript
// User profile from Clerk
interface ClerkUser {
  id: string;
  emailAddresses: Array<{
    id: string;
    emailAddress: string;
    verification: {
      status: 'verified' | 'unverified';
    };
  }>;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
  createdAt: number;
  updatedAt: number;
}

// Internal user representation
interface User {
  userId: string;           // Clerk user ID
  email: string;           // Primary email address
  createdAt: string;       // ISO timestamp
  updatedAt: string;       // ISO timestamp
  profile?: UserProfile;
  preferences?: UserPreferences;
  subscription?: UserSubscription;
}

interface UserProfile {
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
}

interface UserPreferences {
  emailNotifications: boolean;
  automaticAnalysis: boolean;
  theme: 'light' | 'dark' | 'system';
}

interface UserSubscription {
  plan: 'free' | 'premium';
  tokensRemaining: number;
  tokensTotal: number;
  renewalDate?: string;
}
```

### Email Analysis

```typescript
// Email content structure
interface EmailContent {
  subject: string;
  body: string;
  from: string;
  to: string;
  date: string;           // ISO timestamp
}

// AI analysis metadata
interface EmailMetadata {
  was_prompt_injection_detected_in_the_email: boolean;
  is_related_to_job_search: boolean;
  company_name: string;
  role_name: string;
  ATS_name: string;
  email_type_category: EmailTypeCategory;
  what_does_the_receiver_of_the_email_need_to_do_in_less_than_10_words: string;
}

// Email type categories
type EmailTypeCategory = 
  | 'application_confirmation'
  | 'interview_invitation'
  | 'rejection'
  | 'offer'
  | 'follow_up'
  | 'other';

// Complete analysis result
interface EmailAnalysisResult {
  parsed: EmailMetadata;
  usage?: OpenAIUsage;
  date?: string;
  version?: string;
  lastUpdated?: string;
}

interface OpenAIUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

// Database document structure
interface EmailAnalysisDocument extends EmailAnalysisResult {
  clerkUserId: string;
  messageId: string;
  monitoredEmail: string;
}
```

### Job Processing

```typescript
// Analysis job tracking
interface AnalysisJob {
  jobId: string;               // UUID
  clerkUserId: string;
  status: JobStatus;
  current: string;             // Current status message
  total: number;               // Total emails to process
  processed: number;           // Emails processed so far
  cachedCount: number;         // Emails with cached results
  newAnalysisCount: number;    // Emails requiring new analysis
  remainingTokens: number;     // User's remaining tokens
  startDate: string;           // Analysis date range start
  endDate: string;             // Analysis date range end
  monitoredEmail: string;      // Email being monitored
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  error?: string;              // Error message if failed
}

type JobStatus = 'processing' | 'completed' | 'error' | 'cancelled';

// Progress update structure
interface ProgressUpdate {
  status: 'idle' | 'processing' | 'completed' | 'error';
  current: string;
  processed: number;
  total: number;
  cachedCount?: number;
  remainingTokens?: number;
  error?: string;
}
```

## API Request/Response Interfaces

### Email Analysis API

```typescript
// POST /api/emails/analyze
interface AnalyzeEmailsRequest {
  startDate: string;          // Format: YYYY-MM-DD
  endDate: string;            // Format: YYYY-MM-DD
}

interface AnalyzeEmailsResponse {
  message: string;
  status: JobStatus;
  jobId: string;
  totalEmails: number;
}

// POST /api/emails/estimate
interface EstimateRequest {
  startDate: string;
  endDate: string;
}

interface EstimateResponse {
  totalEmails: number;
  emailsNeedingTokens: number;
  hasEnoughTokens: boolean;
  remainingTokens: number;
  estimatedCost: {
    tokens: number;
    usdCost: number;
  };
}

// POST /api/emails/progress-webhook
interface ProgressWebhookRequest {
  clerkUserId: string;
  messageId: string;
  status: 'processing' | 'completed' | 'error';
  current?: string;
  processed?: number;
  total?: number;
  analysis?: Partial<EmailMetadata>;
  error?: string;
}

interface ProgressWebhookResponse {
  message: string;
}
```

### Token Management API

```typescript
// GET /api/tokens
interface TokenResponse {
  remaining: number;
  total: number;
  used?: number;
  resetDate?: string;
  error?: string;
}
```

### Analytics API

```typescript
// GET /api/metrics
interface MetricsRequest {
  startDate?: string;         // Optional date filter
  endDate?: string;           // Optional date filter
  type?: 'applications' | 'interviews' | 'all';
}

interface MetricsResponse {
  summary: MetricsSummary;
  timeline: TimelineData[];
  categories: CategoryBreakdown;
  companies: CompanyMetrics[];
}

interface MetricsSummary {
  totalApplications: number;
  totalInterviews: number;
  responseRate: number;        // Percentage
  averageResponseTime: number; // Days
}

interface TimelineData {
  date: string;               // YYYY-MM-DD
  applications: number;
  interviews: number;
}

type CategoryBreakdown = Record<EmailTypeCategory, number>;

interface CompanyMetrics {
  name: string;
  applications: number;
  interviews: number;
  status: 'active' | 'inactive' | 'completed';
}
```

## Gmail API Interfaces

```typescript
// Gmail API message structure
interface GmailMessage {
  id: string;
  threadId: string;
  labelIds: string[];
  snippet: string;
  historyId: string;
  internalDate: string;
  payload: GmailPayload;
  sizeEstimate: number;
}

interface GmailPayload {
  partId: string;
  mimeType: string;
  filename: string;
  headers: GmailHeader[];
  body: GmailBody;
  parts?: GmailPayload[];
}

interface GmailHeader {
  name: string;
  value: string;
}

interface GmailBody {
  attachmentId?: string;
  size: number;
  data?: string;              // Base64 encoded
}

// Gmail API list response
interface GmailListResponse {
  messages: Array<{
    id: string;
    threadId: string;
  }>;
  nextPageToken?: string;
  resultSizeEstimate: number;
}

// Gmail watch request/response
interface GmailWatchRequest {
  labelIds?: string[];
  labelFilterAction?: 'include' | 'exclude';
  topicName: string;
}

interface GmailWatchResponse {
  historyId: string;
  expiration: string;
}
```

## Cloud Function Interfaces

```typescript
// Pub/Sub message structure
interface PubSubMessage {
  data: string;               // Base64 encoded JSON
  attributes?: Record<string, string>;
  messageId: string;
  publishTime: string;
}

// Email analysis request message
interface EmailAnalysisMessage {
  clerkUserId: string;
  messageId: string;
  monitoredEmail: string;
  jobId: string;
  batchIndex: number;
  totalInBatch: number;
  historyId?: string;
}

// Gmail notification message
interface GmailNotificationMessage {
  emailAddress: string;
  historyId: string;
}

// Cloud Function context
interface CloudFunctionContext {
  eventId: string;
  timestamp: string;
  eventType: string;
  resource: string;
}
```

## Service Layer Interfaces

```typescript
// Gmail service interface
interface IGmailService {
  getMonitoredEmail(): string;
  getEmailsByDateRange(startDate: string, endDate: string): Promise<EmailId[]>;
  getEmailContent(messageId: string): Promise<EmailContent>;
  modifyMessage(messageId: string, options: ModifyMessageOptions): Promise<void>;
  getDDJSLabelIds(): Promise<LabelIds>;
}

interface EmailId {
  id: string;
  threadId: string;
}

interface ModifyMessageOptions {
  addLabelIds?: string[];
  removeLabelIds?: string[];
}

interface LabelIds {
  applications: string;
  interviews: string;
  rejections: string;
  offers: string;
  followUps: string;
}

// Token service interface
interface ITokenService {
  getRemainingTokens(userId: string): Promise<number>;
  deductTokens(userId: string, amount: number): Promise<void>;
  addTokens(userId: string, amount: number): Promise<void>;
  resetTokens(userId: string): Promise<void>;
}

// Email analyzer interface
interface IEmailAnalyzer {
  processEmail(
    emailInfo: { clerkUserId: string; messageId: string },
    gmailService: IGmailService,
    tokenService: ITokenService,
    labelIds: LabelIds,
    options?: AnalysisOptions
  ): Promise<EmailAnalysisResult>;
  
  getCachedAnalysis(
    clerkUserId: string,
    messageId: string,
    monitoredEmail: string
  ): Promise<EmailAnalysisResult | null>;
}

interface AnalysisOptions {
  skipTokenCheck?: boolean;
  forceReanalysis?: boolean;
}
```

## Database Interfaces

```typescript
// Firestore document interfaces
interface FirestoreDocument {
  id: string;
  data(): any;
  exists: boolean;
  createTime: Date;
  updateTime: Date;
}

interface FirestoreQuery {
  where(field: string, operator: string, value: any): FirestoreQuery;
  orderBy(field: string, direction?: 'asc' | 'desc'): FirestoreQuery;
  limit(limit: number): FirestoreQuery;
  get(): Promise<FirestoreQuerySnapshot>;
}

interface FirestoreQuerySnapshot {
  docs: FirestoreDocument[];
  size: number;
  empty: boolean;
}

// Database service interface
interface IDatabase {
  collection(name: string): FirestoreCollectionReference;
  runTransaction<T>(updateFunction: (transaction: FirestoreTransaction) => Promise<T>): Promise<T>;
}

interface FirestoreCollectionReference {
  doc(id?: string): FirestoreDocumentReference;
  add(data: any): Promise<FirestoreDocumentReference>;
  where(field: string, operator: string, value: any): FirestoreQuery;
}

interface FirestoreDocumentReference {
  id: string;
  get(): Promise<FirestoreDocument>;
  set(data: any, options?: { merge?: boolean }): Promise<void>;
  update(data: any): Promise<void>;
  delete(): Promise<void>;
}
```

## Error Interfaces

```typescript
// Standard error response
interface APIError {
  message: string;
  error?: string;
  code?: string;
  details?: any;
  timestamp?: string;
}

// Service error types
interface ServiceError extends Error {
  code: string;
  statusCode?: number;
  details?: any;
}

// Gmail API errors
interface GmailAPIError extends ServiceError {
  code: 'GMAIL_API_ERROR';
  gmailError: {
    code: number;
    message: string;
    status: string;
  };
}

// OpenAI API errors
interface OpenAIError extends ServiceError {
  code: 'OPENAI_API_ERROR';
  openaiError: {
    type: string;
    code: string;
    message: string;
  };
}

// Authentication errors
interface AuthError extends ServiceError {
  code: 'AUTH_ERROR';
  authType: 'clerk' | 'gmail' | 'openai';
}
```

## Validation Schemas

```typescript
// Zod schemas for runtime validation
import { z } from 'zod';

export const EmailMetadataSchema = z.object({
  was_prompt_injection_detected_in_the_email: z.boolean(),
  is_related_to_job_search: z.boolean(),
  company_name: z.string(),
  role_name: z.string(),
  ATS_name: z.string(),
  email_type_category: z.enum([
    'application_confirmation',
    'interview_invitation',
    'rejection',
    'offer',
    'follow_up',
    'other'
  ]),
  what_does_the_receiver_of_the_email_need_to_do_in_less_than_10_words: z.string()
});

export const AnalyzeEmailsRequestSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/)
});

export const ProgressWebhookRequestSchema = z.object({
  clerkUserId: z.string(),
  messageId: z.string(),
  status: z.enum(['processing', 'completed', 'error']),
  current: z.string().optional(),
  processed: z.number().optional(),
  total: z.number().optional(),
  analysis: EmailMetadataSchema.partial().optional(),
  error: z.string().optional()
});
```

## Type Guards

```typescript
// Type guard functions
export function isEmailAnalysisResult(obj: any): obj is EmailAnalysisResult {
  return obj && 
         typeof obj === 'object' &&
         obj.parsed &&
         typeof obj.parsed.is_related_to_job_search === 'boolean';
}

export function isProgressUpdate(obj: any): obj is ProgressUpdate {
  return obj &&
         typeof obj === 'object' &&
         typeof obj.status === 'string' &&
         typeof obj.current === 'string' &&
         typeof obj.processed === 'number' &&
         typeof obj.total === 'number';
}

export function isAPIError(obj: any): obj is APIError {
  return obj &&
         typeof obj === 'object' &&
         typeof obj.message === 'string';
}
```
