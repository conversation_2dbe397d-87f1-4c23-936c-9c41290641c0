# 🔌 API Specification for Email Analysis Status Page

## Overview

This document specifies the new API endpoints required for the Email Analysis Status page, including request/response schemas, error handling, and integration patterns.

## New API Endpoints

### GET /api/analysis-runs

Retrieves a paginated list of email analysis runs for the authenticated user.

#### Request

```http
GET /api/analysis-runs?page=1&limit=20&sourceType=manual&status=completed
Authorization: Bearer <token>
```

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | number | No | 1 | Page number (1-based) |
| `limit` | number | No | 20 | Items per page (max 100) |
| `sourceType` | string | No | - | Filter by 'manual' or 'automatic' |
| `status` | string | No | - | Filter by analysis run status |
| `startDate` | string | No | - | Filter runs created after date (YYYY-MM-DD) |
| `endDate` | string | No | - | Filter runs created before date (YYYY-MM-DD) |
| `sortBy` | string | No | 'createdAt' | Sort field ('createdAt', 'status', 'totalEmails') |
| `sortOrder` | string | No | 'desc' | Sort order ('asc', 'desc') |

#### Response Schema

```typescript
interface ListAnalysisRunsResponse {
  analysisRuns: AnalysisRunSummary[];
  pagination: PaginationInfo;
  filters: AppliedFilters;
}

interface AnalysisRunSummary {
  jobId: string;
  sourceType: 'manual' | 'automatic';
  status: 'processing' | 'completed' | 'error' | 'cancelled';

  // Email counts (based on actual data model)
  totalEmails: number;          // total: emails found in Gmail date range
  cachedEmails: number;         // cachedCount: emails with existing analysis
  pendingEmails: number;        // calculated: total - processed
  successfullyAnalyzed: number; // processed: completed emails (new + cached)

  // Date information
  emailDateRange: {             // startDate/endDate: email date range analyzed
    start: string;  // YYYY-MM-DD format
    end: string;    // YYYY-MM-DD format
  };
  analysisStartTime: string;    // createdAt: when analysis run started
  lastUpdated: string;          // updatedAt: last progress update
  completedAt?: string;         // completedAt: when analysis finished

  // Status information
  currentStatus: string;        // current: human-readable status message
  error?: string;               // error: failure message if any

  // Progress information (for active analysis runs)
  progress?: {
    percentage: number;         // calculated: (processed / total) * 100
    estimatedTimeRemaining?: number; // seconds (future enhancement)
  };
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalJobs: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  pageSize: number;
}

interface AppliedFilters {
  sourceType?: 'manual' | 'automatic';
  status?: string;
  startDate?: string;
  endDate?: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}
```

#### Success Response

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "analysisRuns": [
    {
      "jobId": "550e8400-e29b-41d4-a716-446655440000",
      "sourceType": "manual",
      "status": "completed",
      "totalEmails": 150,
      "cachedEmails": 45,
      "pendingEmails": 0,
      "successfullyAnalyzed": 150,
      "emailDateRange": {
        "start": "2024-01-01",
        "end": "2024-01-31"
      },
      "analysisStartTime": "2024-01-15T10:30:00Z",
      "lastUpdated": "2024-01-15T11:45:00Z",
      "completedAt": "2024-01-15T11:45:00Z",
      "currentStatus": "Analysis completed successfully"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalRuns": 87,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "pageSize": 20
  },
  "filters": {
    "sourceType": "manual",
    "sortBy": "createdAt",
    "sortOrder": "desc"
  }
}
```

#### Error Responses

```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Invalid query parameters",
  "details": {
    "limit": "Must be between 1 and 100",
    "sortBy": "Must be one of: createdAt, status, totalEmails"
  }
}
```

```http
HTTP/1.1 401 Unauthorized
Content-Type: application/json

{
  "error": "Authentication required",
  "message": "Please provide a valid authorization token"
}
```

## Enhanced Existing Endpoints

### GET /api/jobs/[jobId]/status

Enhanced to include additional fields for the status page.

#### Enhanced Response Schema

```typescript
interface EnhancedJobStatusResponse {
  jobId: string;
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  sourceType: 'manual' | 'automatic';  // NEW FIELD
  
  progress: {
    total: number;
    processed: number;
    current: string;
    cachedCount: number;
    newAnalysisCount: number;
    remainingTokens?: number;
    error?: string;
    percentage: number;  // NEW: calculated field
  };
  
  // Enhanced timing information
  timing: {  // NEW SECTION
    createdAt: string;
    updatedAt: string;
    completedAt?: string;
    duration?: number;  // seconds
    estimatedTimeRemaining?: number;  // seconds for active jobs
  };
  
  // Email analysis details
  emailDetails: {  // NEW SECTION
    dateRange: {
      start: string;
      end: string;
    };
    monitoredEmail: string;
    totalEmails: number;
    pendingEmails: number;
  };
  
  result?: any;  // Existing field
}
```

## Implementation Details

### Database Query Optimization

```typescript
// Efficient pagination query for analysis runs
async function getAnalysisRuns(userId: string, options: QueryOptions) {
  let query = database
    .collection('analysisJobs')
    .where('clerkUserId', '==', userId);

  // Apply filters
  if (options.sourceType) {
    query = query.where('sourceType', '==', options.sourceType);
  }

  if (options.status) {
    query = query.where('status', '==', options.status);
  }

  // Apply sorting
  query = query.orderBy(options.sortBy || 'createdAt', options.sortOrder || 'desc');

  // Apply pagination
  if (options.cursor) {
    query = query.startAfter(options.cursor);
  }

  query = query.limit(options.limit || 20);

  return await query.get();
}
```

### Real-time Updates Integration

```typescript
// Polling-based updates for analysis runs
interface AnalysisRunUpdateMessage {
  type: 'analysis_status_update';
  jobId: string;
  status: string;
  progress: {
    processed: number;
    total: number;
    percentage: number;
  };
  timestamp: string;
}

// Client-side polling for active analysis runs
async function pollAnalysisRunUpdates(jobIds: string[]) {
  const updates = await Promise.all(
    jobIds.map(id => fetch(`/api/jobs/${id}/status`))
  );

  return updates.map(response => response.json());
}
```

### Error Handling Strategy

```typescript
// Standardized error responses
interface APIError {
  error: string;
  message: string;
  code?: string;
  details?: Record<string, any>;
  timestamp: string;
  requestId: string;
}

// Error handling middleware
function handleAPIError(error: Error, req: Request): APIError {
  const requestId = req.headers['x-request-id'] || generateRequestId();
  
  if (error instanceof ValidationError) {
    return {
      error: 'Validation Error',
      message: error.message,
      code: 'VALIDATION_FAILED',
      details: error.details,
      timestamp: new Date().toISOString(),
      requestId
    };
  }
  
  // Handle other error types...
  return {
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    timestamp: new Date().toISOString(),
    requestId
  };
}
```

## Rate Limiting

### API Rate Limits

| Endpoint | Rate Limit | Window |
|----------|------------|--------|
| `GET /api/analysis-jobs` | 60 requests | 1 minute |
| `GET /api/jobs/[id]/status` | 120 requests | 1 minute |
| `POST /api/emails/analyze` | 10 requests | 1 minute |

### Implementation

```typescript
// Rate limiting middleware
const rateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // limit each IP to 60 requests per windowMs
  message: {
    error: 'Too Many Requests',
    message: 'Rate limit exceeded. Please try again later.',
    retryAfter: 60
  }
});
```

## Data Caching Strategy

### Email Analysis Result Caching

The term "caching" in our system refers to storing previous AI analysis results in Firestore to avoid re-analyzing the same emails:

```typescript
// Email analysis caching logic (already implemented)
const docId = `${clerkUserId}_${monitoredEmail}_${messageId}`;
const analysisDoc = await database.collection('emailAnalysis').doc(docId).get();

// Check if cached analysis exists and is current version
if (analysisDoc.exists && analysisDoc.data()?.version === currentAnalysisVersion) {
  // Use cached analysis (no AI tokens consumed)
  return analysisDoc.data();
} else {
  // Perform new AI analysis (consumes tokens)
  const newAnalysis = await performAIAnalysis(email);
  await database.collection('emailAnalysis').doc(docId).set(newAnalysis);
  return newAnalysis;
}
```

**Note**: This is different from HTTP response caching. Our "cached emails" are emails that have already been analyzed by AI and stored in Firestore.

## Testing Strategy

### API Test Cases

```typescript
describe('GET /api/analysis-runs', () => {
  test('should return paginated analysis runs for authenticated user', async () => {
    const response = await request(app)
      .get('/api/analysis-runs?page=1&limit=10')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('analysisRuns');
    expect(response.body).toHaveProperty('pagination');
    expect(response.body.analysisRuns).toHaveLength(10);
  });

  test('should filter by source type', async () => {
    const response = await request(app)
      .get('/api/analysis-runs?sourceType=manual')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200);

    response.body.analysisRuns.forEach(run => {
      expect(run.sourceType).toBe('manual');
    });
  });

  test('should handle invalid pagination parameters', async () => {
    await request(app)
      .get('/api/analysis-runs?page=0&limit=101')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(400);
  });
});
```

---

*This API specification provides the foundation for the Email Analysis Status page backend functionality.*
