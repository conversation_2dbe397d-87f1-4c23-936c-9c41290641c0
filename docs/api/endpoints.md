# 🔌 API Endpoints Documentation

This document provides comprehensive documentation for all API endpoints in the DDJS application.

## Base URLs

- **Local Development**: `http://localhost:3000`
- **Development Environment**: `https://dev.datadrivenjobsearch.com`
- **Production Environment**: `https://datadrivenjobsearch.com`

## Authentication

All API endpoints require authentication via Clerk JWT tokens, except for webhooks and health checks.

### Authentication Header
```http
Authorization: Bearer <clerk_jwt_token>
```

### Getting Authentication Token
```javascript
// Client-side (React)
import { useAuth } from '@clerk/nextjs'

const { getToken } = useAuth()
const token = await getToken()

// Server-side (API routes)
import { auth } from '@clerk/nextjs/server'

const { userId } = await auth()
```

## Email Analysis Endpoints

### POST /api/emails/analyze

Triggers email analysis for a specified date range.

#### Request
```http
POST /api/emails/analyze
Content-Type: application/json
Authorization: Bearer <token>

{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

#### Request Schema
```typescript
interface AnalyzeEmailsRequest {
  startDate: string;  // Format: YYYY-MM-DD
  endDate: string;    // Format: YYYY-MM-DD
}
```

#### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "message": "Analysis requests sent to Cloud Functions successfully",
  "status": "processing",
  "jobId": "550e8400-e29b-41d4-a716-446655440000",
  "totalEmails": 25
}
```

#### Response Schema
```typescript
interface AnalyzeEmailsResponse {
  message: string;
  status: 'processing' | 'completed' | 'error';
  jobId: string;
  totalEmails: number;
}
```

#### Error Responses
```http
HTTP/1.1 400 Bad Request
{
  "message": "Missing required fields"
}

HTTP/1.1 401 Unauthorized
{
  "message": "Unauthorized"
}

HTTP/1.1 500 Internal Server Error
{
  "message": "Failed to trigger email analysis",
  "error": "Detailed error message"
}
```

### POST /api/emails/estimate

Estimates the cost and token usage for email analysis.

#### Request
```http
POST /api/emails/estimate
Content-Type: application/json
Authorization: Bearer <token>

{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

#### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "totalEmails": 25,
  "emailsNeedingTokens": 15,
  "hasEnoughTokens": true,
  "remainingTokens": 4850,
  "estimatedCost": {
    "tokens": 15,
    "usdCost": 0.75
  }
}
```

#### Response Schema
```typescript
interface EstimateResponse {
  totalEmails: number;
  emailsNeedingTokens: number;
  hasEnoughTokens: boolean;
  remainingTokens: number;
  estimatedCost: {
    tokens: number;
    usdCost: number;
  };
}
```

### GET /api/emails/progress

Server-Sent Events endpoint for real-time progress updates.

#### Request
```http
GET /api/emails/progress
Authorization: Bearer <token>
Accept: text/event-stream
```

#### Response
```http
HTTP/1.1 200 OK
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

data: {"status":"connected","current":"Ready for analysis","processed":0,"total":0}

data: {"status":"processing","current":"Analyzing email 1 of 25","processed":1,"total":25}

data: {"status":"completed","current":"Analysis complete","processed":25,"total":25}
```

#### Event Data Schema
```typescript
interface ProgressUpdate {
  status: 'idle' | 'processing' | 'completed' | 'error';
  current: string;
  processed: number;
  total: number;
  cachedCount?: number;
  remainingTokens?: number;
  error?: string;
}
```

### POST /api/emails/progress-webhook

Webhook endpoint for Cloud Functions to send progress updates.

#### Request
```http
POST /api/emails/progress-webhook
Content-Type: application/json

{
  "clerkUserId": "user_2abc123def456",
  "messageId": "msg_18f2a3b4c5d6e7f8",
  "status": "completed",
  "current": "Email analysis completed",
  "analysis": {
    "company_name": "TechCorp Inc",
    "email_type_category": "interview_invitation",
    "is_related_to_job_search": true
  }
}
```

#### Request Schema
```typescript
interface ProgressWebhookRequest {
  clerkUserId: string;
  messageId: string;
  status: 'processing' | 'completed' | 'error';
  current?: string;
  processed?: number;
  total?: number;
  analysis?: {
    company_name?: string;
    email_type_category?: string;
    is_related_to_job_search?: boolean;
  };
  error?: string;
}
```

## Token Management Endpoints

### GET /api/tokens

Retrieves user's token balance and usage information.

#### Request
```http
GET /api/tokens
Authorization: Bearer <token>
```

#### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "remaining": 4850,
  "total": 5000,
  "used": 150,
  "resetDate": "2024-02-01T00:00:00Z"
}
```

#### Response Schema
```typescript
interface TokenResponse {
  remaining: number;
  total: number;
  used?: number;
  resetDate?: string;
  error?: string;
}
```

## Analytics Endpoints

### GET /api/metrics

Retrieves analytics data for the dashboard.

#### Request
```http
GET /api/metrics
Authorization: Bearer <token>
```

#### Query Parameters
- `startDate` (optional): Start date for metrics (YYYY-MM-DD)
- `endDate` (optional): End date for metrics (YYYY-MM-DD)
- `type` (optional): Metric type ('applications' | 'interviews' | 'all')

#### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "summary": {
    "totalApplications": 45,
    "totalInterviews": 12,
    "responseRate": 26.7,
    "averageResponseTime": 3.2
  },
  "timeline": [
    {
      "date": "2024-01-01",
      "applications": 3,
      "interviews": 1
    }
  ],
  "categories": {
    "application_confirmation": 25,
    "interview_invitation": 12,
    "rejection": 8
  },
  "companies": [
    {
      "name": "TechCorp Inc",
      "applications": 2,
      "interviews": 1,
      "status": "active"
    }
  ]
}
```

#### Response Schema
```typescript
interface MetricsResponse {
  summary: {
    totalApplications: number;
    totalInterviews: number;
    responseRate: number;
    averageResponseTime: number;
  };
  timeline: Array<{
    date: string;
    applications: number;
    interviews: number;
  }>;
  categories: Record<string, number>;
  companies: Array<{
    name: string;
    applications: number;
    interviews: number;
    status: string;
  }>;
}
```

## Health Check Endpoints

### GET /api/health

System health check endpoint (no authentication required).

#### Request
```http
GET /api/health
```

#### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "status": "healthy",
  "timestamp": "2024-01-20T10:30:00Z",
  "environment": "development",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "authentication": "healthy",
    "externalAPIs": "healthy"
  },
  "uptime": 3600
}
```

## WebSocket/Socket.IO Endpoints

### GET /api/socket

Socket.IO connection endpoint for real-time communication.

#### Connection
```javascript
import { io } from 'socket.io-client'

const socket = io({
  path: '/api/socket',
  auth: { token: clerkToken }
})

// Listen for progress updates
socket.on('progress', (data) => {
  console.log('Progress update:', data)
})

// Handle connection events
socket.on('connect', () => {
  console.log('Connected to server')
})

socket.on('disconnect', () => {
  console.log('Disconnected from server')
})
```

#### Events

##### Client → Server
```typescript
// No client-to-server events currently implemented
```

##### Server → Client
```typescript
interface ProgressEvent {
  status: 'idle' | 'processing' | 'completed' | 'error';
  current: string;
  processed: number;
  total: number;
  cachedCount?: number;
}

socket.on('progress', (data: ProgressEvent) => {
  // Handle progress update
})
```

## Error Handling

### Standard Error Response
```typescript
interface ErrorResponse {
  message: string;
  error?: string;
  code?: string;
  details?: any;
}
```

### HTTP Status Codes
- **200 OK**: Successful request
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### Error Examples
```http
HTTP/1.1 400 Bad Request
{
  "message": "Invalid date format",
  "error": "startDate must be in YYYY-MM-DD format",
  "code": "INVALID_DATE_FORMAT"
}

HTTP/1.1 401 Unauthorized
{
  "message": "Unauthorized",
  "error": "Invalid or expired token",
  "code": "INVALID_TOKEN"
}

HTTP/1.1 429 Too Many Requests
{
  "message": "Rate limit exceeded",
  "error": "Too many requests, please try again later",
  "code": "RATE_LIMIT_EXCEEDED"
}
```

## Rate Limiting

### Limits
- **General API**: 100 requests per minute per user
- **Analysis Endpoints**: 10 requests per minute per user
- **Progress Endpoints**: No limit (real-time updates)

### Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642680000
```

## CORS Configuration

### Allowed Origins
- **Development**: `http://localhost:3000`, `https://dev.datadrivenjobsearch.com`
- **Production**: `https://datadrivenjobsearch.com`

### Allowed Methods
- `GET`, `POST`, `PUT`, `DELETE`, `OPTIONS`

### Allowed Headers
- `Content-Type`, `Authorization`, `X-Requested-With`

## API Versioning

Currently using implicit versioning. Future versions will use URL versioning:
- **v1**: `/api/v1/emails/analyze`
- **v2**: `/api/v2/emails/analyze`

## SDK/Client Libraries

### JavaScript/TypeScript
```typescript
// Example API client
class DDJSApiClient {
  constructor(private token: string, private baseUrl: string) {}

  async analyzeEmails(startDate: string, endDate: string) {
    const response = await fetch(`${this.baseUrl}/api/emails/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      },
      body: JSON.stringify({ startDate, endDate })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return response.json()
  }
}
```

## Testing

### Example Test Cases
```typescript
// Jest test example
describe('Email Analysis API', () => {
  test('should analyze emails successfully', async () => {
    const response = await request(app)
      .post('/api/emails/analyze')
      .set('Authorization', `Bearer ${validToken}`)
      .send({
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      })
      .expect(200)

    expect(response.body).toHaveProperty('jobId')
    expect(response.body.status).toBe('processing')
  })
})
```
