# Email Analysis Status Page - Troubleshooting Guide

## Common Issues and Solutions

### 1. Analysis Button Remains Disabled

**Symptoms:**
- Start Email Analysis button is grayed out
- Cannot click to start analysis
- <PERSON><PERSON> shows "disabled" state

**Possible Causes & Solutions:**

#### Missing Date Selection
- **Check**: Both start and end dates are selected
- **Solution**: Select both start and end dates in the date picker fields

#### Insufficient Tokens
- **Check**: Token estimation shows "You don't have enough tokens"
- **Solution**: 
  - Select a smaller date range
  - Purchase additional tokens
  - Wait for token refresh if on a subscription plan

#### Invalid Date Range
- **Check**: End date is before start date
- **Solution**: Ensure end date is after start date

#### Network/API Issues
- **Check**: Browser console for error messages
- **Solution**: Refresh page and try again

### 2. Real-time Updates Not Working

**Symptoms:**
- Processing analysis runs don't update
- Status remains stuck on old information
- Progress bars don't advance

**Diagnostic Steps:**

1. **Check Network Connection**
   ```
   - Open browser developer tools (F12)
   - Go to Network tab
   - Look for failed requests to /api/jobs/*/status
   ```

2. **Check Console <PERSON>rrors**
   ```
   - Open browser console
   - Look for error messages related to polling
   - Check for authentication errors
   ```

3. **Manual Refresh**
   ```
   - Click the "Refresh" button
   - If data updates, polling mechanism may be failing
   ```

**Solutions:**

- **Network Issues**: Check internet connection
- **Authentication**: Sign out and sign back in
- **Browser Issues**: Clear cache and cookies
- **Server Issues**: Contact support if problem persists

### 3. Table Not Loading or Empty

**Symptoms:**
- Table shows "No analysis runs found"
- Loading state persists indefinitely
- Error message appears

**Diagnostic Steps:**

1. **Check Authentication**
   ```
   - Verify you're signed in
   - Check if other pages work correctly
   ```

2. **Check API Response**
   ```
   - Open browser developer tools
   - Go to Network tab
   - Look for /api/analysis-runs requests
   - Check response status and content
   ```

3. **Check Filters**
   ```
   - Clear all filters using "Clear all" button
   - Try different filter combinations
   ```

**Solutions:**

- **Authentication Issues**: Sign in again
- **API Errors**: Check server status, contact support
- **Filter Issues**: Clear filters and try again
- **First Time User**: Start your first analysis

### 4. Performance Issues

**Symptoms:**
- Page loads slowly
- Table scrolling is laggy
- Browser becomes unresponsive

**Diagnostic Steps:**

1. **Check Data Volume**
   ```
   - Note total number of analysis runs
   - Check if you have many processing runs
   ```

2. **Check Browser Performance**
   ```
   - Open browser developer tools
   - Go to Performance tab
   - Record a session to identify bottlenecks
   ```

3. **Check Memory Usage**
   ```
   - In console, run: logPerformanceSummary()
   - Look for memory warnings
   ```

**Solutions:**

- **Large Dataset**: Use filters to reduce visible data
- **Memory Issues**: Refresh the page periodically
- **Browser Issues**: Try a different browser
- **System Resources**: Close other applications

### 5. Filtering and Sorting Issues

**Symptoms:**
- Filters don't apply correctly
- Sorting doesn't work
- Results don't match filter criteria

**Diagnostic Steps:**

1. **Check Filter State**
   ```
   - Note which filters are active (shown as badges)
   - Try clearing and reapplying filters
   ```

2. **Check Network Requests**
   ```
   - Monitor /api/analysis-runs requests
   - Verify query parameters match selected filters
   ```

**Solutions:**

- **Clear Filters**: Use "Clear all" button and start fresh
- **Refresh Data**: Click refresh button after changing filters
- **Browser Cache**: Clear browser cache if issues persist

### 6. Mobile/Responsive Issues

**Symptoms:**
- Layout broken on mobile devices
- Touch interactions don't work
- Text too small to read

**Solutions:**

- **Zoom Level**: Adjust browser zoom to 100%
- **Orientation**: Try both portrait and landscape modes
- **Browser**: Use a modern mobile browser
- **Update**: Ensure browser is up to date

## Error Messages and Meanings

### API Error Messages

| Error Message | Meaning | Solution |
|---------------|---------|----------|
| "Authentication required" | User session expired | Sign in again |
| "Failed to fetch analysis runs" | Network or server error | Check connection, try refresh |
| "HTTP 500: Internal Server Error" | Server-side error | Contact support |
| "HTTP 401: Unauthorized" | Authentication failed | Sign out and sign in again |
| "HTTP 403: Forbidden" | Access denied | Check user permissions |

### Validation Error Messages

| Error Message | Meaning | Solution |
|---------------|---------|----------|
| "Please select both start and end dates" | Missing date selection | Select both dates |
| "End date must be after start date" | Invalid date range | Correct date order |
| "You don't have enough tokens" | Insufficient tokens | Reduce date range or buy tokens |
| "Date range too large" | Range exceeds limits | Select smaller date range |

## Browser Compatibility

### Supported Browsers
- **Chrome**: Version 90+
- **Firefox**: Version 88+
- **Safari**: Version 14+
- **Edge**: Version 90+

### Known Issues
- **Internet Explorer**: Not supported
- **Older Mobile Browsers**: Limited functionality

## Performance Optimization

### For Users

1. **Use Filters**: Reduce data load by filtering results
2. **Close Other Tabs**: Free up browser memory
3. **Regular Refresh**: Refresh page if it becomes slow
4. **Stable Connection**: Use reliable internet connection

### For Developers

1. **Monitor Performance**: Use browser dev tools
2. **Check Memory**: Run `logPerformanceSummary()` in console
3. **Export Metrics**: Use `exportPerformanceMetrics()` for analysis

## Development/Debug Tools

### Console Commands (Development Mode)

```javascript
// Log performance summary
logPerformanceSummary()

// Export performance metrics
exportPerformanceMetrics()

// Check component state
// (Available in React DevTools)
```

### Browser Developer Tools

1. **Network Tab**: Monitor API requests
2. **Console Tab**: Check for JavaScript errors
3. **Performance Tab**: Analyze rendering performance
4. **Application Tab**: Check local storage and cookies

## Getting Additional Help

### Before Contacting Support

1. **Try Basic Solutions**: Refresh page, clear cache, try different browser
2. **Gather Information**:
   - Browser type and version
   - Error messages (screenshots helpful)
   - Steps to reproduce the issue
   - Network conditions

### Support Channels

1. **Technical Issues**: Contact development team
2. **User Account Issues**: Contact user support
3. **Feature Requests**: Submit through appropriate channels

### Information to Provide

- **Browser**: Type and version
- **Operating System**: Type and version
- **Error Messages**: Exact text or screenshots
- **Steps to Reproduce**: Detailed sequence of actions
- **Expected vs Actual**: What should happen vs what actually happens

## Preventive Measures

### Regular Maintenance

1. **Clear Browser Cache**: Weekly or when issues arise
2. **Update Browser**: Keep browser up to date
3. **Monitor Token Usage**: Track token consumption
4. **Regular Backups**: Export important analysis results

### Best Practices

1. **Reasonable Date Ranges**: Avoid very large ranges
2. **Monitor Progress**: Check on long-running analyses
3. **Use Filters**: Keep table manageable with filters
4. **Stable Environment**: Use reliable internet and updated browser

---

*This troubleshooting guide covers common issues with the Email Analysis Status Page. For technical implementation details, see the developer documentation.*
