# 🏗️ System Architecture Diagrams for Email Analysis Status Page

## Overview

This document provides comprehensive system architecture diagrams showing how the new Email Analysis Status page integrates with the existing DDJS infrastructure.

## High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Browser[Web Browser]
        Mobile[Mobile Browser]
    end
    
    subgraph "Frontend - Next.js Application"
        Dashboard[Dashboard Page]
        AnalysisPage[Analysis Status Page]
        Navigation[Navigation Component]
        
        subgraph "Shared Components"
            EmailDateSelector[Email Date Selector]
            PollingProgress[Polling Progress]
            DataTable[Data Table]
            StatusBadge[Status Badge]
        end
    end
    
    subgraph "API Layer - Next.js Routes"
        AnalysisJobsAPI[/api/analysis-jobs]
        JobStatusAPI[/api/jobs/[id]/status]
        AnalyzeAPI[/api/emails/analyze]
        TokensAPI[/api/tokens]
        MetricsAPI[/api/metrics]
    end
    
    subgraph "Processing Layer - Cloud Functions"
        EmailAnalysisFunc[Email Analysis Function]
        NotificationHandler[Notification Handler]
    end
    
    subgraph "Data Layer"
        Firestore[(Firestore Database)]
        PubSub[Pub/Sub Topics]
        SecretManager[Secret Manager]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        GmailAPI[Gmail API]
        Clerk[Clerk Authentication]
    end
    
    Browser --> AnalysisPage
    Mobile --> AnalysisPage
    AnalysisPage --> Navigation
    AnalysisPage --> EmailDateSelector
    AnalysisPage --> PollingProgress
    AnalysisPage --> DataTable
    DataTable --> StatusBadge
    
    AnalysisPage --> AnalysisJobsAPI
    AnalysisPage --> JobStatusAPI
    EmailDateSelector --> AnalyzeAPI
    
    AnalysisJobsAPI --> Firestore
    JobStatusAPI --> Firestore
    AnalyzeAPI --> PubSub
    AnalyzeAPI --> Firestore
    
    PubSub --> EmailAnalysisFunc
    EmailAnalysisFunc --> OpenAI
    EmailAnalysisFunc --> GmailAPI
    EmailAnalysisFunc --> Firestore
    
    GmailAPI --> NotificationHandler
    NotificationHandler --> PubSub
    
    AnalysisPage --> Clerk
    AnalysisJobsAPI --> Clerk
```

## Data Flow Architecture

```mermaid
sequenceDiagram
    participant User
    participant AnalysisPage
    participant AnalysisJobsAPI
    participant Firestore
    participant PollingService
    participant JobStatusAPI
    
    User->>AnalysisPage: Navigate to /analysis
    AnalysisPage->>AnalysisJobsAPI: GET /api/analysis-jobs
    AnalysisJobsAPI->>Firestore: Query analysisJobs collection
    Firestore-->>AnalysisJobsAPI: Return paginated jobs
    AnalysisJobsAPI-->>AnalysisPage: Return job list with pagination
    AnalysisPage-->>User: Display jobs table
    
    Note over AnalysisPage,PollingService: Real-time Updates
    AnalysisPage->>PollingService: Start polling for active jobs
    loop Every 5 seconds
        PollingService->>JobStatusAPI: GET /api/jobs/[id]/status
        JobStatusAPI->>Firestore: Get job status
        Firestore-->>JobStatusAPI: Return current status
        JobStatusAPI-->>PollingService: Return status update
        PollingService-->>AnalysisPage: Update job status
        AnalysisPage-->>User: Refresh table row
    end
    
    User->>AnalysisPage: Apply filters
    AnalysisPage->>AnalysisJobsAPI: GET /api/analysis-jobs?filters
    AnalysisJobsAPI->>Firestore: Query with filters
    Firestore-->>AnalysisJobsAPI: Return filtered results
    AnalysisJobsAPI-->>AnalysisPage: Return filtered jobs
    AnalysisPage-->>User: Update table display
```

## Database Schema Architecture

```mermaid
erDiagram
    USERS ||--o{ ANALYSIS_JOBS : "creates"
    USERS ||--o{ EMAIL_ANALYSIS : "owns"
    ANALYSIS_JOBS ||--o{ EMAIL_ANALYSIS : "processes"
    
    USERS {
        string userId PK
        string email
        object profile
        object subscription
        timestamp createdAt
        timestamp updatedAt
    }
    
    ANALYSIS_JOBS {
        string jobId PK
        string clerkUserId FK
        string sourceType "NEW: manual|automatic"
        string status
        string current
        number total
        number processed
        number cachedCount
        number newAnalysisCount
        number remainingTokens
        string startDate
        string endDate
        string monitoredEmail
        timestamp createdAt
        timestamp updatedAt
        timestamp completedAt
        string error
    }
    
    EMAIL_ANALYSIS {
        string documentId PK
        string clerkUserId FK
        string messageId
        string monitoredEmail
        string date
        string version
        object parsed
        object usage
        timestamp lastUpdated
    }
    
    NOTIFICATION_PREFERENCES {
        string clerkUserId PK
        boolean automaticAnalysis
        object skipInbox
        object emailNotifications
        timestamp updatedAt
    }
```

## Component Architecture

```mermaid
graph TB
    subgraph "Analysis Page Container"
        AnalysisPage[AnalysisPage Component]
        PageState[Page State Management]
        RealTimeUpdates[Real-time Updates Hook]
    end
    
    subgraph "Analysis Controls"
        StartSection[Start Analysis Section]
        EmailDateSelector[Email Date Selector]
        PollingProgress[Polling Progress]
    end
    
    subgraph "Jobs Display"
        JobsTable[Analysis Jobs Table]
        TableFilters[Table Filters]
        TablePagination[Table Pagination]
        JobRow[Job Row Component]
    end
    
    subgraph "UI Components"
        DataTable[Generic Data Table]
        StatusBadge[Status Badge]
        ProgressIndicator[Progress Indicator]
        SourceTypeBadge[Source Type Badge]
        FilterDropdown[Filter Dropdown]
        PaginationControls[Pagination Controls]
    end
    
    subgraph "Hooks & Services"
        useAnalysisJobs[useAnalysisJobs Hook]
        useRealTimeUpdates[useRealTimeUpdates Hook]
        AnalysisJobsService[Analysis Jobs Service]
        JobStatusService[Job Status Service]
    end
    
    AnalysisPage --> PageState
    AnalysisPage --> RealTimeUpdates
    AnalysisPage --> StartSection
    AnalysisPage --> JobsTable
    
    StartSection --> EmailDateSelector
    StartSection --> PollingProgress
    
    JobsTable --> DataTable
    JobsTable --> TableFilters
    JobsTable --> TablePagination
    
    DataTable --> JobRow
    JobRow --> StatusBadge
    JobRow --> ProgressIndicator
    JobRow --> SourceTypeBadge
    
    TableFilters --> FilterDropdown
    TablePagination --> PaginationControls
    
    AnalysisPage --> useAnalysisJobs
    RealTimeUpdates --> useRealTimeUpdates
    useAnalysisJobs --> AnalysisJobsService
    useRealTimeUpdates --> JobStatusService
```

## API Architecture

```mermaid
graph LR
    subgraph "Client Requests"
        GetJobs[GET /api/analysis-jobs]
        GetStatus[GET /api/jobs/[id]/status]
        PostAnalyze[POST /api/emails/analyze]
    end
    
    subgraph "API Route Handlers"
        AnalysisJobsHandler[Analysis Jobs Handler]
        JobStatusHandler[Job Status Handler]
        AnalyzeHandler[Analyze Handler]
    end
    
    subgraph "Services Layer"
        AuthService[Authentication Service]
        DatabaseService[Database Service]
        TokenService[Token Service]
        GmailService[Gmail Service]
    end
    
    subgraph "Data Sources"
        Firestore[(Firestore)]
        PubSub[Pub/Sub]
        Clerk[Clerk Auth]
    end
    
    GetJobs --> AnalysisJobsHandler
    GetStatus --> JobStatusHandler
    PostAnalyze --> AnalyzeHandler
    
    AnalysisJobsHandler --> AuthService
    AnalysisJobsHandler --> DatabaseService
    
    JobStatusHandler --> AuthService
    JobStatusHandler --> DatabaseService
    
    AnalyzeHandler --> AuthService
    AnalyzeHandler --> TokenService
    AnalyzeHandler --> GmailService
    AnalyzeHandler --> DatabaseService
    
    AuthService --> Clerk
    DatabaseService --> Firestore
    AnalyzeHandler --> PubSub
```

## Real-time Updates Architecture

```mermaid
graph TB
    subgraph "Frontend"
        AnalysisPage[Analysis Page]
        PollingHook[useRealTimeUpdates Hook]
        JobsState[Jobs State]
    end
    
    subgraph "Polling Strategy"
        PollingInterval[5-second Interval]
        ActiveJobsFilter[Active Jobs Filter]
        BatchStatusCheck[Batch Status Check]
    end
    
    subgraph "API Layer"
        JobStatusAPI[Job Status API]
        StatusCache[Response Cache]
    end
    
    subgraph "Database"
        AnalysisJobsCollection[analysisJobs Collection]
        JobUpdates[Real-time Job Updates]
    end
    
    subgraph "Cloud Functions"
        EmailAnalysisFunc[Email Analysis Function]
        ProgressUpdates[Progress Updates]
    end
    
    AnalysisPage --> PollingHook
    PollingHook --> PollingInterval
    PollingInterval --> ActiveJobsFilter
    ActiveJobsFilter --> BatchStatusCheck
    BatchStatusCheck --> JobStatusAPI
    
    JobStatusAPI --> StatusCache
    StatusCache --> AnalysisJobsCollection
    
    EmailAnalysisFunc --> ProgressUpdates
    ProgressUpdates --> AnalysisJobsCollection
    AnalysisJobsCollection --> JobUpdates
    JobUpdates --> JobStatusAPI
    
    JobStatusAPI --> PollingHook
    PollingHook --> JobsState
    JobsState --> AnalysisPage
```

## Security Architecture

```mermaid
graph TB
    subgraph "Authentication Layer"
        ClerkAuth[Clerk Authentication]
        JWTTokens[JWT Tokens]
        UserSession[User Session]
    end
    
    subgraph "Authorization Layer"
        APIMiddleware[API Middleware]
        UserValidation[User Validation]
        ResourceAccess[Resource Access Control]
    end
    
    subgraph "Data Access Layer"
        FirestoreRules[Firestore Security Rules]
        UserDataIsolation[User Data Isolation]
        QueryFiltering[Query Filtering]
    end
    
    subgraph "API Endpoints"
        AnalysisJobsAPI[Analysis Jobs API]
        JobStatusAPI[Job Status API]
        ProtectedRoutes[Protected Routes]
    end
    
    ClerkAuth --> JWTTokens
    JWTTokens --> UserSession
    UserSession --> APIMiddleware
    
    APIMiddleware --> UserValidation
    UserValidation --> ResourceAccess
    ResourceAccess --> ProtectedRoutes
    
    ProtectedRoutes --> AnalysisJobsAPI
    ProtectedRoutes --> JobStatusAPI
    
    AnalysisJobsAPI --> FirestoreRules
    JobStatusAPI --> FirestoreRules
    FirestoreRules --> UserDataIsolation
    UserDataIsolation --> QueryFiltering
```

## Performance Architecture

```mermaid
graph TB
    subgraph "Frontend Optimization"
        ComponentMemo[Component Memoization]
        VirtualScrolling[Virtual Scrolling]
        LazyLoading[Lazy Loading]
        StateOptimization[State Optimization]
    end
    
    subgraph "API Optimization"
        ResponseCaching[Response Caching]
        QueryOptimization[Query Optimization]
        Pagination[Cursor Pagination]
        BatchRequests[Batch Requests]
    end
    
    subgraph "Database Optimization"
        CompositeIndexes[Composite Indexes]
        QueryPlanning[Query Planning]
        ConnectionPooling[Connection Pooling]
        CacheStrategy[Cache Strategy]
    end
    
    subgraph "Monitoring"
        PerformanceMetrics[Performance Metrics]
        AlertSystem[Alert System]
        LoadTesting[Load Testing]
        UserMetrics[User Metrics]
    end
    
    ComponentMemo --> StateOptimization
    VirtualScrolling --> LazyLoading
    
    ResponseCaching --> QueryOptimization
    Pagination --> BatchRequests
    
    CompositeIndexes --> QueryPlanning
    ConnectionPooling --> CacheStrategy
    
    PerformanceMetrics --> AlertSystem
    LoadTesting --> UserMetrics
    
    StateOptimization --> ResponseCaching
    BatchRequests --> CompositeIndexes
    CacheStrategy --> PerformanceMetrics
```

## Deployment Architecture

```mermaid
graph TB
    subgraph "Development Environment"
        DevBranch[Feature Branch]
        DevTesting[Unit & Integration Tests]
        DevDeployment[Dev Deployment]
    end
    
    subgraph "Staging Environment"
        StagingBranch[Staging Branch]
        E2ETests[E2E Tests]
        StagingDeployment[Staging Deployment]
    end
    
    subgraph "Production Environment"
        MainBranch[Main Branch]
        ProductionTests[Production Tests]
        CanaryDeployment[Canary Deployment]
        FullDeployment[Full Deployment]
    end
    
    subgraph "Infrastructure"
        CloudRun[Cloud Run]
        Firestore[Firestore]
        CloudFunctions[Cloud Functions]
        LoadBalancer[Load Balancer]
    end
    
    subgraph "Monitoring & Rollback"
        HealthChecks[Health Checks]
        Monitoring[Monitoring]
        AlertSystem[Alert System]
        RollbackProcedure[Rollback Procedure]
    end
    
    DevBranch --> DevTesting
    DevTesting --> DevDeployment
    DevDeployment --> StagingBranch
    
    StagingBranch --> E2ETests
    E2ETests --> StagingDeployment
    StagingDeployment --> MainBranch
    
    MainBranch --> ProductionTests
    ProductionTests --> CanaryDeployment
    CanaryDeployment --> FullDeployment
    
    FullDeployment --> CloudRun
    CloudRun --> Firestore
    CloudRun --> CloudFunctions
    LoadBalancer --> CloudRun
    
    CloudRun --> HealthChecks
    HealthChecks --> Monitoring
    Monitoring --> AlertSystem
    AlertSystem --> RollbackProcedure
```

---

*These architecture diagrams provide a comprehensive view of how the Email Analysis Status page integrates with and enhances the existing DDJS system.*
