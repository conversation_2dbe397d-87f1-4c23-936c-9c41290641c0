# 🚀 Getting Started - Local Development

This guide will help you set up the DDJS application for local development.

## Prerequisites

### Required Software
- **Node.js 18+** (recommended: Node.js 20)
- **npm** (comes with Node.js)
- **Git** for version control
- **Firebase CLI** for emulators
- **Google Cloud CLI** (optional, for cloud services)

### Installation Commands
```bash
# Install Node.js (using nvm - recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# Install Firebase CLI
npm install -g firebase-tools

# Install Google Cloud CLI (optional)
# macOS
brew install google-cloud-sdk

# Ubuntu/Debian
curl https://sdk.cloud.google.com | bash
```

### Account Setup
1. **GitHub Account**: Access to the repository
2. **Clerk Account**: For authentication services
3. **OpenAI Account**: For AI email analysis
4. **Google Cloud Account**: For Gmail API access (optional for local dev)

## Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/DataDrivenJobSearch/webapp.git
cd webapp
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.local.template .env.local

# Edit with your configuration
nano .env.local
```

### 4. Start Development Environment
```bash
# Use the provided script (recommended)
./RunLocally.sh

# Or start manually
npm run dev
```

### 5. Access the Application
- **Web App**: http://localhost:3000
- **Firebase Emulator UI**: http://localhost:4000
- **Firestore Emulator**: http://localhost:8080

## Detailed Setup

### Environment Variables

Create `.env.local` file in the project root:

```bash
# Core Configuration
NODE_ENV=development
GOOGLE_CLOUD_PROJECT=ddjs-dev-458016

# Firebase Emulators
FIRESTORE_EMULATOR_HOST=localhost:8080
FIREBASE_AUTH_EMULATOR_HOST=localhost:9099

# Clerk Authentication (Development Keys)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_key_here
CLERK_SECRET_KEY=sk_test_your_secret_here
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# OpenAI API
OPENAI_API_KEY=sk-proj-your_openai_key_here

# Application URLs
BASE_URL=http://localhost:3000
WEBAPP_PROGRESS_WEBHOOK_URL=http://localhost:3000/api/emails/progress-webhook

# Gmail API Configuration (for local testing)
GMAIL_PUBSUB_TOPIC=projects/ddjs-dev-458016/topics/gmail-notifications
GMAIL_PUBSUB_SUBSCRIPTION=gmail-notifications-sub
FUNCTIONS_REGION=us-central1
```

### Getting API Keys

#### Clerk Authentication
1. Go to [Clerk Dashboard](https://dashboard.clerk.com/)
2. Create a new application
3. Configure Google OAuth provider
4. Copy the publishable key and secret key
5. Set up redirect URLs:
   - Sign-in URL: `http://localhost:3000/sign-in`
   - Sign-up URL: `http://localhost:3000/sign-up`
   - After sign-in URL: `http://localhost:3000`

#### OpenAI API
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an account or sign in
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key (starts with `sk-proj-`)

#### Gmail API (Optional for Local Dev)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Gmail API
4. Create OAuth 2.0 credentials
5. Configure in Clerk as OAuth provider

## Development Workflow

### Project Structure
```
webapp/
├── apps/
│   ├── webapp/                 # Next.js application
│   ├── email-analysis-function/    # Cloud Function for email analysis
│   └── notification-handler-function/  # Cloud Function for notifications
├── libs/
│   ├── shared/                 # Shared utilities
│   ├── email-core/            # Email processing logic
│   └── services/              # Business logic services
├── scripts/                   # Deployment and utility scripts
├── tests/                     # Test suites
└── docs/                      # Documentation
```

### Build Process
```bash
# Build all projects
npx nx run-many --target=build --all

# Build specific project
npx nx build webapp
npx nx build shared
npx nx build email-core

# Build with dependencies
npx nx build webapp --with-deps
```

### Running Services

#### Start All Services
```bash
# Recommended: Use the provided script
./RunLocally.sh

# This script will:
# 1. Build all necessary libraries
# 2. Start Firebase emulators
# 3. Start the Next.js development server
# 4. Set up proper environment variables
```

#### Start Individual Services
```bash
# Start Firebase emulators only
firebase emulators:start --only firestore,pubsub

# Start Next.js app only
npm run dev

# Start with specific configuration
NODE_ENV=development npm run dev
```

### Development Commands

```bash
# Development server with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint

# Run tests
npm test

# Clean build artifacts
npx nx reset
```

## Testing Setup

### Unit Tests
```bash
# Run all tests
npm test

# Run tests for specific project
npx nx test webapp
npx nx test shared

# Run tests in watch mode
npx nx test webapp --watch
```

### End-to-End Tests
```bash
# Install Playwright browsers
npx playwright install

# Run E2E tests
npx playwright test

# Run tests with UI
npx playwright test --ui
```

### Integration Tests
```bash
# Start test environment
./tests/setup-test-environment.sh

# Run integration tests
npm run test:integration
```

## Database Setup

### Firebase Emulators
The Firebase emulators provide a local development environment:

```bash
# Start emulators
firebase emulators:start

# Available services:
# - Firestore: localhost:8080
# - Pub/Sub: localhost:9085
# - Emulator UI: localhost:4000
```

### Seed Data
```bash
# Initialize with sample data
node scripts/init-firestore-emulator.js

# Clear database
node scripts/clear-database.js
```

### Database Queries
```bash
# Test database connection
curl http://localhost:8080/v1/projects/ddjs-dev-458016/databases/(default)/documents/users

# View emulator UI
open http://localhost:4000
```

## Debugging

### VS Code Configuration
Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm run dev"
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000"
    }
  ]
}
```

### Browser DevTools
- **React DevTools**: Install browser extension
- **Network Tab**: Monitor API calls
- **Console**: View application logs
- **Application Tab**: Check local storage and cookies

### Server Logs
```bash
# View application logs
tail -f logs/run_local_$(date +%Y%m%d_%H%M%S).log

# View Firebase emulator logs
firebase emulators:start --debug
```

## Common Issues & Solutions

### Port Conflicts
```bash
# Check what's using port 3000
lsof -i :3000

# Kill process using port
kill -9 $(lsof -t -i:3000)

# Use different port
PORT=3001 npm run dev
```

### Environment Variable Issues
```bash
# Check if variables are loaded
node -e "console.log(process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY)"

# Restart development server after changing .env.local
```

### Firebase Emulator Issues
```bash
# Clear emulator data
firebase emulators:start --import=./emulator-data --export-on-exit

# Reset emulator state
rm -rf .firebase/
firebase emulators:start
```

### Build Issues
```bash
# Clear Nx cache
npx nx reset

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for TypeScript errors
npx tsc --noEmit
```

### Authentication Issues
```bash
# Verify Clerk configuration
curl -H "Authorization: Bearer $CLERK_SECRET_KEY" https://api.clerk.dev/v1/users

# Check browser network tab for auth redirects
# Ensure redirect URLs match Clerk configuration
```

## Next Steps

1. **Explore the Codebase**: Start with `apps/webapp/src/app/page.tsx`
2. **Read Architecture Docs**: [Frontend Architecture](../architecture/frontend-architecture.md)
3. **Set Up Testing**: [Testing Guide](./testing-guide.md)
4. **Deploy to Development**: [Deployment Guide](../deployment/environments.md)

## Getting Help

- **Documentation**: Check the `/docs` folder
- **Issues**: Create GitHub issues for bugs
- **Discussions**: Use GitHub discussions for questions
- **Logs**: Check application logs for error details

## Development Best Practices

### Code Style
- Use TypeScript for type safety
- Follow ESLint configuration
- Use Prettier for code formatting
- Write meaningful commit messages

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create pull request
git push origin feature/your-feature-name
```

### Testing
- Write tests for new features
- Ensure all tests pass before committing
- Use meaningful test descriptions
- Test both happy path and error cases
