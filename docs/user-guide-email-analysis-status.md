# Email Analysis Status Page - User Guide

## Overview

The Email Analysis Status Page provides a comprehensive view of all your email analysis runs, allowing you to monitor progress, view results, and start new analyses. This page replaces the previous dashboard-based email analysis functionality with a dedicated, feature-rich interface.

## Accessing the Analysis Page

1. **From the Dashboard**: Click the "Start Email Analysis" button or "Go to Analysis Page" link
2. **From Navigation**: Click "Analysis" in the main navigation menu
3. **Direct URL**: Navigate to `/analysis` in your browser

## Page Layout

### Header Section
- **Page Title**: "Email Analysis Status"
- **Description**: Brief explanation of the page functionality
- **Refresh Button**: Manually refresh the analysis runs list
- **Last Updated**: Shows when the data was last refreshed

### Start New Analysis Section
- **Date Selection**: Choose start and end dates for email analysis
- **Token Estimation**: Automatic calculation of required tokens
- **Analysis Button**: Start the email analysis process

### Filters Section
- **Source Type Filter**: Filter by Manual or Automatic analysis runs
- **Status Filter**: Filter by Processing, Completed, Error, or Cancelled
- **Sort Options**: Sort by Start Time, Total Emails, Status, or Source Type
- **Clear Filters**: Remove all active filters

### Analysis Runs Table
- **Comprehensive View**: All analysis runs with detailed information
- **Real-time Updates**: Active runs update automatically every 2-30 seconds
- **Interactive Sorting**: Click column headers to sort data
- **Status Indicators**: Visual badges for different analysis states

### Summary Statistics
- **Completed**: Number of successfully completed analyses
- **Processing**: Number of currently running analyses
- **Failed**: Number of failed analyses
- **Total Emails**: Sum of all emails processed across all runs

## Starting a New Analysis

### Step 1: Select Date Range
1. Click on the **Start Date** field
2. Choose the beginning date for your email analysis
3. Click on the **End Date** field
4. Choose the ending date for your email analysis

### Step 2: Review Token Estimation
- The system automatically calculates:
  - **Total Emails Found**: Number of emails in the selected date range
  - **Tokens Required**: Estimated tokens needed for analysis
  - **Token Availability**: Whether you have sufficient tokens

### Step 3: Start Analysis
1. Click the **"Start Email Analysis"** button
2. The analysis will begin and appear in the table below
3. Progress will be tracked in real-time

## Understanding the Analysis Table

### Column Descriptions

| Column | Description |
|--------|-------------|
| **Source Type** | Manual (user-initiated) or Automatic (triggered by notifications) |
| **Total Emails** | Number of emails found in the specified date range |
| **Cached** | Number of emails with existing analysis (no tokens used) |
| **Pending** | Number of emails still waiting to be processed |
| **Analyzed** | Number of emails successfully analyzed |
| **Status** | Current state: Processing, Completed, Error, or Cancelled |
| **Email Date Range** | Date range of emails being analyzed |
| **Started** | When the analysis run began |

### Status Indicators

- **🔵 Processing**: Analysis is currently running
  - Shows animated progress indicator
  - Updates every 2-30 seconds automatically
  - Displays current progress percentage

- **✅ Completed**: Analysis finished successfully
  - Shows completion time
  - All emails have been processed

- **❌ Error**: Analysis encountered an error
  - Row highlighted in red
  - Error details available

- **⏹️ Cancelled**: Analysis was stopped
  - Analysis was manually cancelled or system stopped

### Real-time Updates

- **Active Monitoring**: Processing runs update automatically
- **Adaptive Polling**: Update frequency adjusts based on activity (2-30 seconds)
- **Visual Indicators**: Blue dot shows active runs
- **Progress Tracking**: Live progress bars for processing analyses

## Using Filters and Sorting

### Filtering Options

1. **Source Type Filter**:
   - **All Sources**: Show both manual and automatic runs
   - **Manual**: Show only user-initiated analyses
   - **Automatic**: Show only system-triggered analyses

2. **Status Filter**:
   - **All Statuses**: Show runs in any state
   - **Processing**: Show only currently running analyses
   - **Completed**: Show only finished analyses
   - **Error**: Show only failed analyses
   - **Cancelled**: Show only stopped analyses

### Sorting Options

1. **Sort By**:
   - **Start Time**: When the analysis began (default)
   - **Total Emails**: Number of emails in the analysis
   - **Status**: Current analysis state
   - **Source Type**: Manual vs Automatic

2. **Sort Order**:
   - **Newest First**: Most recent analyses first (default)
   - **Oldest First**: Earliest analyses first

### Active Filters Display

- Active filters appear as colored badges below the filter controls
- Click the "×" on any badge to remove that specific filter
- Click "Clear all" to remove all active filters

## Pagination

- **Page Navigation**: Use arrow buttons to navigate between pages
- **Page Numbers**: Click specific page numbers to jump to that page
- **Results Info**: Shows current range (e.g., "Showing 1 to 20 of 150 results")
- **Smart Pagination**: Ellipsis (...) for large page counts

## Performance Features

### Optimized Loading
- **Fast Rendering**: Optimized for large datasets
- **Efficient Updates**: Only active runs are polled for updates
- **Memory Management**: Automatic cleanup prevents memory leaks

### Responsive Design
- **Mobile Friendly**: Optimized for phones and tablets
- **Touch Interactions**: Swipe and tap gestures supported
- **Adaptive Layout**: Adjusts to different screen sizes

## Troubleshooting

### Common Issues

1. **Analysis Button Disabled**
   - **Cause**: Missing dates, insufficient tokens, or validation errors
   - **Solution**: Ensure both dates are selected and you have enough tokens

2. **No Analysis Runs Showing**
   - **Cause**: No analyses have been started yet
   - **Solution**: Start your first analysis using the form above

3. **Real-time Updates Not Working**
   - **Cause**: Network connectivity issues
   - **Solution**: Check internet connection and click refresh

4. **Slow Performance**
   - **Cause**: Large number of analysis runs
   - **Solution**: Use filters to narrow down the view

### Error Messages

- **"Authentication required"**: Sign in again
- **"Failed to fetch analysis runs"**: Check network connection
- **"Insufficient tokens"**: Purchase more tokens or select smaller date range

## Tips for Best Performance

1. **Use Filters**: Filter by status or source type to reduce data load
2. **Reasonable Date Ranges**: Avoid very large date ranges for better performance
3. **Monitor Active Runs**: Keep track of processing analyses
4. **Regular Refresh**: Use the refresh button if data seems stale

## Getting Help

If you encounter issues not covered in this guide:

1. **Check Console**: Open browser developer tools for error details
2. **Contact Support**: Use the support channels provided in the application
3. **Documentation**: Refer to the technical documentation for advanced topics

---

*This user guide covers the Email Analysis Status Page functionality. For technical implementation details, see the developer documentation.*
