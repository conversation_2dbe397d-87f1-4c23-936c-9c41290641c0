{"name": "email-analysis-function", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/email-analysis-function/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/apps/email-analysis-function", "format": ["cjs"], "bundle": false, "main": "apps/email-analysis-function/src/main.ts", "tsConfig": "apps/email-analysis-function/tsconfig.app.json", "assets": ["apps/email-analysis-function/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "email-analysis-function:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "email-analysis-function:build:development"}, "production": {"buildTarget": "email-analysis-function:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}