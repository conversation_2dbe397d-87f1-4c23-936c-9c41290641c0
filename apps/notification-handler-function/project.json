{"name": "notification-handler-function", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/notification-handler-function/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/apps/notification-handler-function", "format": ["cjs"], "bundle": false, "main": "apps/notification-handler-function/src/main.ts", "tsConfig": "apps/notification-handler-function/tsconfig.app.json", "assets": ["apps/notification-handler-function/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "notification-handler-function:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "notification-handler-function:build:development"}, "production": {"buildTarget": "notification-handler-function:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}