{"name": "notification-handler-function", "version": "1.0.0", "description": "GCP Cloud Function for Gmail notification handling", "main": "dist/main.js", "scripts": {"start": "functions-framework --target=handleGmailNotification --signature-type=http", "build": "tsc", "deploy": "gcloud functions deploy handleGmailNotification --runtime=nodejs20 --trigger-http --allow-unauthenticated --entry-point=handleGmailNotification"}, "dependencies": {"@google-cloud/functions-framework": "^3.0.0", "@google-cloud/pubsub": "^4.0.0", "@webapp/email-core": "file:../../libs/email-core", "@webapp/services": "file:../../libs/services", "tslib": "^2.3.0"}, "devDependencies": {"@types/node": "^18.0.0", "typescript": "^5.0.0"}, "engines": {"node": "20"}}