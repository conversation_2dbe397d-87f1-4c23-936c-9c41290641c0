{"name": "webapp", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/webapp/src", "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/webapp"}, "configurations": {"development": {"outputPath": "apps/webapp"}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "webapp:build", "dev": true}, "configurations": {"development": {"buildTarget": "webapp:build:development", "dev": true}, "production": {"buildTarget": "webapp:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "webapp:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/webapp/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/webapp/**/*.{ts,tsx,js,jsx}"]}}}, "tags": []}