import type { ReadableStreamDefaultController } from 'stream/web';

// Simple in-memory store for active SSE connections
// In a multi-server setup, replace this with a shared store like Redis

// Define the structure for the progress update messages
export interface ProgressUpdate {
  status: 'idle' | 'processing' | 'completed' | 'error' | 'connecting';
  total?: number;
  processed?: number;
  current?: string;
  cachedCount?: number;
  estimatedTokens?: number | null;
  remainingTokens?: number | null;
  tokenError?: string | null;
}

// Map to store controllers: Key = userId (string), Value = controller
const connectionStore = new Map<string, ReadableStreamDefaultController<any>>();

/**
 * Adds a connection controller to the store.
 * @param userId - The authenticated user's ID.
 * @param controller - The ReadableStreamDefaultController for the user's SSE connection.
 */
export const addConnection = (userId: string, controller: ReadableStreamDefaultController<any>) => {
  console.log(`ConnectionStore: Adding connection for user ${userId}`);
  // Close and remove any existing connection for this user
  const existingController = connectionStore.get(userId);
  if (existingController) {
    try {
      console.warn(`ConnectionStore: Closing existing connection for user ${userId}`);
      existingController.close();
    } catch (error) {
      console.error(`ConnectionStore: Error closing existing controller for ${userId}:`, error);
    }
  }
  connectionStore.set(userId, controller);
};

/**
 * Removes a connection controller from the store.
 * @param userId - The authenticated user's ID.
 */
export const removeConnection = (userId: string) => {
  if (connectionStore.has(userId)) {
     console.log(`ConnectionStore: Removing connection for user ${userId}`);
     connectionStore.delete(userId);
  } else {
     console.log(`ConnectionStore: No connection found to remove for user ${userId}`);
  }
 
};

/**
 * Sends a progress update message to a specific user's connection.
 * @param userId - The authenticated user's ID.
 * @param message - The ProgressUpdate object to send.
 * @returns boolean - True if the message was sent successfully, false otherwise.
 */
export const sendProgress = (userId: string, message: ProgressUpdate): boolean => {
  const controller = connectionStore.get(userId);
  if (controller) {
    try {
      const formattedMessage = `data: ${JSON.stringify(message)}\n\n`;
      console.log(`ConnectionStore: Sending progress to user ${userId}:`, formattedMessage.trim());
      controller.enqueue(new TextEncoder().encode(formattedMessage));
      return true;
    } catch (error) {
      console.error(`ConnectionStore: Error sending progress to user ${userId}:`, error);
      // Attempt to remove the faulty connection
      removeConnection(userId);
      return false;
    }
  } else {
    console.warn(`ConnectionStore: No active connection found for user ${userId} to send progress.`);
    return false;
  }
}; 