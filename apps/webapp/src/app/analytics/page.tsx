'use client'

import { useAuth } from '@clerk/nextjs'
import { useEffect, useState } from 'react'
import { Card } from '@/app/components/ui/Card'
import { LineChart } from '@/app/components/ui/LineChart'

interface AnalyticsData {
  applications: {
    weekly: Array<{ date: string; value: number }>
    monthly: Array<{ date: string; value: number }>
  }
  interviews: {
    weekly: Array<{ date: string; value: number }>
    monthly: Array<{ date: string; value: number }>
  }
}

export default function AnalyticsPage() {
  const { isLoaded, isSignedIn } = useAuth()
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState<'weekly' | 'monthly'>('weekly')

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      fetchAnalyticsData()
    }
  }, [isLoaded, isSignedIn, timeRange])

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Get date range for the last 3 months
      const endDate = new Date()
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 3)

      const response = await fetch(
        `/api/metrics?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}&period=${timeRange}`
      )

      if (!response.ok) {
        throw new Error(`Failed to fetch analytics data: ${response.status}`)
      }

      const data = await response.json()
      setAnalyticsData(data)
    } catch (err) {
      console.error('Error fetching analytics data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data')
    } finally {
      setLoading(false)
    }
  }

  if (!isLoaded) {
    return <div className="flex justify-center items-center h-64">Loading...</div>
  }

  if (!isSignedIn) {
    return <div className="flex justify-center items-center h-64">Please sign in to view analytics.</div>
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics Dashboard</h1>
        <p className="text-gray-600">Detailed insights into your job search progress</p>
      </div>

      {/* Time Range Selector */}
      <div className="mb-6">
        <div className="flex space-x-4">
          <button
            onClick={() => setTimeRange('weekly')}
            className={`px-4 py-2 rounded-md ${
              timeRange === 'weekly'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Weekly View
          </button>
          <button
            onClick={() => setTimeRange('monthly')}
            className={`px-4 py-2 rounded-md ${
              timeRange === 'monthly'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Monthly View
          </button>
        </div>
      </div>

      {loading && (
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading analytics data...</div>
        </div>
      )}

      {error && (
        <Card className="p-6 mb-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-red-600 mb-2">Error Loading Analytics</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={fetchAnalyticsData}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </Card>
      )}

      {analyticsData && !loading && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Applications Chart */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Job Applications</h2>
            <LineChart
              data={analyticsData.applications[timeRange]}
              xField="date"
              series={[{ name: 'Applications', dataKey: 'value', color: '#3B82F6' }]}
              title={`Applications (${timeRange})`}
            />
          </Card>

          {/* Interviews Chart */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Interview Invitations</h2>
            <LineChart
              data={analyticsData.interviews[timeRange]}
              xField="date"
              series={[{ name: 'Interviews', dataKey: 'value', color: '#10B981' }]}
              title={`Interviews (${timeRange})`}
            />
          </Card>

          {/* Summary Stats */}
          <Card className="p-6 lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Summary Statistics</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analyticsData.applications[timeRange].reduce((sum, item) => sum + item.value, 0)}
                </div>
                <div className="text-sm text-gray-600">Total Applications</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {analyticsData.interviews[timeRange].reduce((sum, item) => sum + item.value, 0)}
                </div>
                <div className="text-sm text-gray-600">Total Interviews</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {analyticsData.applications[timeRange].length > 0
                    ? Math.round(
                        (analyticsData.interviews[timeRange].reduce((sum, item) => sum + item.value, 0) /
                          analyticsData.applications[timeRange].reduce((sum, item) => sum + item.value, 0)) *
                          100
                      )
                    : 0}%
                </div>
                <div className="text-sm text-gray-600">Interview Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {analyticsData.applications[timeRange].length}
                </div>
                <div className="text-sm text-gray-600">Data Points</div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {analyticsData && !loading && analyticsData.applications[timeRange].length === 0 && (
        <Card className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No Data Available</h3>
            <p className="text-gray-500 mb-4">
              No analytics data found for the selected time range. Try running an email analysis first to generate data.
            </p>
            <a
              href="/"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-block"
            >
              Go to Dashboard
            </a>
          </div>
        </Card>
      )}
    </div>
  )
}
