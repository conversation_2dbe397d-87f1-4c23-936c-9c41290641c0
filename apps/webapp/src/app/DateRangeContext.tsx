'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react'

interface DateRangeContextType {
  startDate: string
  endDate: string
  setDateRange: (startDate: string, endDate: string) => void
}

// Create context with default values
const DateRangeContext = createContext<DateRangeContextType>({
  startDate: '',
  endDate: '',
  setDateRange: () => {}
})

// Custom hook to use the date range context
export const useDateRange = () => useContext(DateRangeContext)

interface DateRangeProviderProps {
  children: ReactNode
}

export function DateRangeProvider({ children }: DateRangeProviderProps) {
  // Default to empty strings, will be set by components
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  const setDateRange = (start: string, end: string) => {
    console.log('DateRangeContext: Setting date range', { start, end })
    setStartDate(start)
    setEndDate(end)
  }

  return (
    <DateRangeContext.Provider value={{ startDate, endDate, setDateRange }}>
      {children}
    </DateRangeContext.Provider>
  )
}
