import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { MetricsOverview } from './components'
import Link from 'next/link'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

export default async function Home() {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <Link
          href="/analysis"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Start Email Analysis
        </Link>
      </div>

      <div className="grid gap-6">
        {/* Email analysis functionality moved to /analysis page */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-2">Email Analysis</h2>
          <p className="text-gray-600 mb-4">
            Analyze your job-related emails to track applications, interviews, and responses.
          </p>
          <Link
            href="/analysis"
            className="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-medium"
          >
            Go to Analysis Page →
          </Link>
        </div>

        <MetricsOverview />
      </div>
    </div>
  )
}
