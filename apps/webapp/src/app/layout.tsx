import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'
import Navigation from './components/Navigation'

const inter = Inter({ subsets: ['latin'] })

// Get Clerk publishable key with proper error handling
const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY

// Only log a warning in development
if (!publishableKey && process.env.NODE_ENV === 'development') {
  console.warn(
    'Missing NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY - Clerk components may not work properly. ' +
    'Make sure environment variables are set correctly.'
  )
}

export const metadata = {
  title: 'Data Driven Job Search',
  description: 'Track and analyze your job search progress with AI-powered insights',
}

// Force dynamic rendering for the root layout to prevent
// static generation issues with auth
export const dynamic = 'force-dynamic'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        {/* Clerk<PERSON>rovider with empty string fallback, which
            will be replaced with actual values at runtime */}
        <ClerkProvider publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || ''}>
          <Providers>
            <div className="min-h-screen bg-background">
              <Navigation />
              <main className="container mx-auto px-4 py-4">
                {children}
              </main>
            </div>
          </Providers>
        </ClerkProvider>
      </body>
    </html>
  )
}
