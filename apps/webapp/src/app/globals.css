@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #ffffff;
    --foreground: #111827;
    --muted: #9ca3af;
    --muted-foreground: #4b5563;
    --card: #ffffff;
    --card-foreground: #111827;
    --border: #e5e7eb;
    --input: #e5e7eb;
    --ring: #2563eb;
  }

  .dark {
    --background: #0f172a; /* Darker background for better contrast */
    --foreground: #f8fafc; /* Brighter text */
    --muted: #334155; /* Better muted background */
    --muted-foreground: #cbd5e1; /* Much lighter muted text */
    --card: #1e293b; /* Lighter card background */
    --card-foreground: #f8fafc; /* Bright text on cards */
    --border: #334155; /* Visible borders */
    --input: #334155; /* Input backgrounds */
    --ring: #3b82f6; /* Focus rings */
  }
}

@layer components {
  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: <PERSON>l, Helvetica, sans-serif;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background-color: #f3f4f6;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgb(37 99 235 / 0.5);
    border-radius: 9999px;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgb(37 99 235 / 0.7);
  }
}

@layer utilities {
  * {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
  }
}
