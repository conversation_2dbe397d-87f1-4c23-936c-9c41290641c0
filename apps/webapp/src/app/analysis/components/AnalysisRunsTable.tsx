'use client';

import React, { useMemo, useCallback } from 'react';
import { DataTable, Column } from '../../components/ui/DataTable';
import { StatusBadge } from '../../components/ui/StatusBadge';
import { SourceTypeBadge } from '../../components/ui/SourceTypeBadge';
import { ProgressIndicator } from '../../components/ui/ProgressIndicator';
import { Pagination, PaginationInfo } from '../../components/ui/Pagination';

// Types based on corrected data model
export interface AnalysisRunSummary {
  jobId: string;
  sourceType: 'manual' | 'automatic';
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  
  // Email counts (based on actual data model from code analysis)
  totalEmails: number;          // total: emails found in Gmail date range
  cachedEmails: number;         // cachedCount: emails with existing analysis
  pendingEmails: number;        // calculated: total - processed
  successfullyAnalyzed: number; // processed: completed emails (new + cached)
  
  // Date information
  emailDateRange: {             // startDate/endDate: email date range analyzed
    start: string;  // YYYY-MM-DD format
    end: string;    // YYYY-MM-DD format
  };
  analysisStartTime: string;    // createdAt: when analysis run started
  lastUpdated: string;          // updatedAt: last progress update
  completedAt?: string;         // completedAt: when analysis finished
  
  // Status information
  currentStatus: string;        // current: human-readable status message
  error?: string;               // error: failure message if any
  
  // Progress information (for active analysis runs)
  progress?: {
    percentage: number;         // calculated: (processed / total) * 100
  };
}

export interface FilterState {
  sourceType?: 'manual' | 'automatic';
  status?: 'processing' | 'completed' | 'error' | 'cancelled';
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export interface AnalysisRunsTableProps {
  analysisRuns: AnalysisRunSummary[];
  loading: boolean;
  pagination: PaginationInfo;
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  onPageChange: (page: number) => void;
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  onRunSelect?: (jobId: string) => void;
  className?: string;
}

export const AnalysisRunsTable = React.memo(function AnalysisRunsTable({
  analysisRuns,
  loading,
  pagination,
  filters,
  onFilterChange,
  onPageChange,
  onSortChange,
  onRunSelect,
  className = ''
}: AnalysisRunsTableProps) {

  // Memoize formatting functions to prevent recreation on every render
  const formatDate = useCallback((dateString: string) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  }, []);

  const formatDateTime = useCallback((dateString: string) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return dateString;
    }
  }, []);

  const formatDateRange = useCallback((start: string, end: string) => {
    const startFormatted = formatDate(start);
    const endFormatted = formatDate(end);

    if (!startFormatted || !endFormatted) return '';
    if (startFormatted === endFormatted) return startFormatted;

    return `${startFormatted} - ${endFormatted}`;
  }, [formatDate]);

  // Memoize columns to prevent recreation on every render
  const columns: Column<AnalysisRunSummary>[] = useMemo(() => [
    {
      key: 'sourceType',
      title: 'Source Type',
      sortable: true,
      width: '120px',
      render: (value: 'manual' | 'automatic') => (
        <SourceTypeBadge sourceType={value} size="sm" />
      )
    },
    {
      key: 'totalEmails',
      title: 'Total Emails',
      sortable: true,
      width: '100px',
      align: 'right',
      render: (value: number) => value.toLocaleString()
    },
    {
      key: 'cachedEmails',
      title: 'Cached',
      sortable: true,
      width: '80px',
      align: 'right',
      render: (value: number) => value.toLocaleString()
    },
    {
      key: 'pendingEmails',
      title: 'Pending',
      sortable: true,
      width: '80px',
      align: 'right',
      render: (value: number, row: AnalysisRunSummary) => (
        <span className={value > 0 ? 'text-orange-600 font-medium' : 'text-gray-500'}>
          {value.toLocaleString()}
        </span>
      )
    },
    {
      key: 'successfullyAnalyzed',
      title: 'Analyzed',
      sortable: true,
      width: '80px',
      align: 'right',
      render: (value: number) => (
        <span className="text-green-600 font-medium">
          {value.toLocaleString()}
        </span>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      width: '120px',
      render: (value: 'processing' | 'completed' | 'error' | 'cancelled', row: AnalysisRunSummary) => (
        <div className="space-y-1">
          <StatusBadge 
            status={value} 
            size="sm" 
            animated={value === 'processing'} 
          />
          {value === 'processing' && row.progress && (
            <ProgressIndicator
              current={row.successfullyAnalyzed}
              total={row.totalEmails}
              size="sm"
              showNumbers={false}
              showPercentage={true}
            />
          )}
        </div>
      )
    },
    {
      key: 'emailDateRange',
      title: 'Email Date Range',
      sortable: false,
      width: '140px',
      render: (value: { start: string; end: string }) => (
        <span className="text-sm text-gray-600">
          {formatDateRange(value.start, value.end)}
        </span>
      )
    },
    {
      key: 'analysisStartTime',
      title: 'Started',
      sortable: true,
      width: '120px',
      render: (value: string) => (
        <span className="text-sm text-gray-600">
          {formatDateTime(value)}
        </span>
      )
    }
  ], [formatDateRange, formatDateTime]); // Dependencies for useMemo

  // Memoize event handlers to prevent unnecessary re-renders
  const handleSort = useCallback((columnKey: string, order: 'asc' | 'desc') => {
    onSortChange(columnKey, order);
  }, [onSortChange]);

  const handleRowClick = useCallback((row: AnalysisRunSummary) => {
    if (onRunSelect) {
      onRunSelect(row.jobId);
    }
  }, [onRunSelect]);

  const getRowClassName = useCallback((row: AnalysisRunSummary) => {
    if (row.status === 'error') {
      return 'bg-red-50 hover:bg-red-100';
    }
    if (row.status === 'processing') {
      return 'bg-blue-50 hover:bg-blue-100';
    }
    return '';
  }, []);

  return (
    <div className={`space-y-4 ${className}`} data-testid="analysis-runs-table">
      {/* Table */}
      <DataTable
        data={analysisRuns}
        columns={columns}
        loading={loading}
        emptyMessage="No analysis runs found. Start your first email analysis to see results here."
        loadingRows={5}
        sortBy={filters.sortBy}
        sortOrder={filters.sortOrder}
        onSort={handleSort}
        onRowClick={onRunSelect ? handleRowClick : undefined}
        rowClassName={getRowClassName}
        className="shadow-sm"
      />

      {/* Pagination */}
      <Pagination
        pagination={pagination}
        onPageChange={onPageChange}
        className="border-t border-gray-200 pt-4"
      />
    </div>
  );
});
