'use client';

import React from 'react';
import { FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { FilterState } from '../hooks/useAnalysisRuns';

export interface AnalysisFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  className?: string;
}

export function AnalysisFilters({
  filters,
  onFilterChange,
  className = ''
}: AnalysisFiltersProps) {
  
  const handleSourceTypeChange = (sourceType: 'manual' | 'automatic' | '') => {
    onFilterChange({
      ...filters,
      sourceType: sourceType || undefined
    });
  };

  const handleStatusChange = (status: 'processing' | 'completed' | 'error' | 'cancelled' | '') => {
    onFilterChange({
      ...filters,
      status: status || undefined
    });
  };

  const handleSortChange = (sortBy: string, sortOrder: 'asc' | 'desc') => {
    onFilterChange({
      ...filters,
      sortBy,
      sortOrder
    });
  };

  const clearFilters = () => {
    onFilterChange({
      sortBy: 'analysisStartTime',
      sortOrder: 'desc'
    });
  };

  const hasActiveFilters = filters.sourceType || filters.status;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <FunnelIcon className="w-5 h-5 text-gray-400" />
          <h3 className="text-sm font-medium text-gray-900">Filters</h3>
        </div>
        
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="inline-flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700"
          >
            <XMarkIcon className="w-4 h-4" />
            <span>Clear all</span>
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Source Type Filter */}
        <div>
          <label htmlFor="sourceType" className="block text-sm font-medium text-gray-700 mb-1">
            Source Type
          </label>
          <select
            id="sourceType"
            value={filters.sourceType || ''}
            onChange={(e) => handleSourceTypeChange(e.target.value as any)}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            data-testid="source-type-filter"
          >
            <option value="">All Sources</option>
            <option value="manual">Manual</option>
            <option value="automatic">Automatic</option>
          </select>
        </div>

        {/* Status Filter */}
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            id="status"
            value={filters.status || ''}
            onChange={(e) => handleStatusChange(e.target.value as any)}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            data-testid="status-filter"
          >
            <option value="">All Statuses</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="error">Error</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {/* Sort By */}
        <div>
          <label htmlFor="sortBy" className="block text-sm font-medium text-gray-700 mb-1">
            Sort By
          </label>
          <select
            id="sortBy"
            value={filters.sortBy}
            onChange={(e) => handleSortChange(e.target.value, filters.sortOrder)}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="analysisStartTime">Start Time</option>
            <option value="totalEmails">Total Emails</option>
            <option value="status">Status</option>
            <option value="sourceType">Source Type</option>
          </select>
        </div>

        {/* Sort Order */}
        <div>
          <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-1">
            Order
          </label>
          <select
            id="sortOrder"
            value={filters.sortOrder}
            onChange={(e) => handleSortChange(filters.sortBy, e.target.value as 'asc' | 'desc')}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="desc">Newest First</option>
            <option value="asc">Oldest First</option>
          </select>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {filters.sourceType && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Source: {filters.sourceType}
                <button
                  onClick={() => handleSourceTypeChange('')}
                  className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-purple-400 hover:text-purple-600"
                >
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </span>
            )}
            
            {filters.status && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Status: {filters.status}
                <button
                  onClick={() => handleStatusChange('')}
                  className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:text-blue-600"
                >
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
