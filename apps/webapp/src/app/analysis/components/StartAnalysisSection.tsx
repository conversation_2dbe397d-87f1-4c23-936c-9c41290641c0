'use client';

import React, { useState, useRef } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useTokens } from '../../TokenContext';
import { useDateRange } from '../../DateRangeContext';
import { PollingEmailAnalysisProgress, PollingEmailAnalysisProgressRef } from '../../components/PollingEmailAnalysisProgress';

interface TokenEstimate {
  estimatedTokens: number;
  totalEmails: number;
  hasEnoughTokens: boolean;
  remainingTokens: number;
  error?: string;
}

export interface StartAnalysisSectionProps {
  onAnalysisStart?: (startDate: string, endDate: string) => void;
  onAnalysisComplete?: (result: any) => void;
  disabled?: boolean;
  className?: string;
}

export function StartAnalysisSection({
  onAnalysisStart,
  onAnalysisComplete,
  disabled = false,
  className = ''
}: StartAnalysisSectionProps) {
  const { getToken } = useAuth();
  const { updateTokens, refreshTokens } = useTokens();
  const { setDateRange } = useDateRange();
  
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [estimate, setEstimate] = useState<TokenEstimate | null>(null);
  const [fetchingEstimate, setFetchingEstimate] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);

  const progressRef = useRef<PollingEmailAnalysisProgressRef>(null);

  // Fetch token estimate when dates change
  const fetchTokenEstimate = async () => {
    if (!startDate || !endDate) {
      setEstimate(null);
      setErrorMsg(null);
      return;
    }

    setFetchingEstimate(true);
    setErrorMsg(null);

    try {
      const token = await getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/emails/estimate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ startDate, endDate })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setEstimate(data);
    } catch (error) {
      console.error('Failed to fetch token estimate:', error);
      setErrorMsg(error instanceof Error ? error.message : 'Failed to fetch estimate');
      setEstimate(null);
    } finally {
      setFetchingEstimate(false);
    }
  };

  // Debounced estimate fetch
  React.useEffect(() => {
    const timer = setTimeout(fetchTokenEstimate, 500);
    return () => clearTimeout(timer);
  }, [startDate, endDate]);

  // Handle the analyze button click
  const handleAnalyze = async () => {
    if (!startDate || !endDate) {
      alert('Please select both start and end dates');
      return;
    }

    if (!progressRef.current) {
      console.error('StartAnalysisSection: Progress component ref is not available');
      setErrorMsg('Progress component not ready. Please try refreshing the page.');
      return;
    }

    console.log('StartAnalysisSection: Starting analysis with date range', { startDate, endDate });
    setIsLoading(true);
    setErrorMsg(null);

    try {
      // Notify parent component
      onAnalysisStart?.(startDate, endDate);

      // Use the progress component to start analysis
      await progressRef.current.startAnalysis(startDate, endDate);
      console.log('StartAnalysisSection: Analysis started successfully');
    } catch (error) {
      console.error('StartAnalysisSection: Failed to start analysis:', error);
      setErrorMsg(`Failed to start analysis: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnalysisComplete = (results: any) => {
    console.log('Analysis completed:', results);
    // Refresh tokens and date range
    refreshTokens();
    setDateRange(startDate, endDate);
    // Notify parent component
    onAnalysisComplete?.(results);
  };

  const handleAnalysisError = (error: string) => {
    console.error('Analysis error:', error);
    setErrorMsg(error);
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <h2 className="text-xl font-semibold mb-4">Start New Email Analysis</h2>
      
      <div className="space-y-6">
        {/* Date Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
              Start Date
            </label>
            <input
              type="date"
              id="startDate"
              data-testid="start-date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              max={endDate || undefined}
              disabled={disabled}
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
              End Date
            </label>
            <input
              type="date"
              id="endDate"
              data-testid="end-date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              min={startDate || undefined}
              disabled={disabled}
            />
          </div>
        </div>

        {/* Loading State for Estimate */}
        {fetchingEstimate && (
          <div className="p-4 border border-gray-200 rounded-md bg-gray-50">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
              <span className="text-sm text-gray-600">Calculating analysis requirements...</span>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errorMsg && (
          <div className="p-4 border border-red-200 rounded-md bg-red-50">
            <div className="text-red-700 font-medium">
              ⚠️ {errorMsg}
            </div>
          </div>
        )}

        {/* Token Estimate */}
        {estimate && !fetchingEstimate && !errorMsg && (
          <div className="p-4 border border-gray-200 rounded-md bg-gray-50">
            <h3 className="text-md font-semibold mb-2">Analysis Requirements</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-600">Total Emails Found:</div>
                <div className="text-lg font-semibold">{estimate.totalEmails.toLocaleString()}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Tokens Required:</div>
                <div className="text-lg font-semibold">{estimate.estimatedTokens.toLocaleString()}</div>
              </div>
            </div>

            {!estimate.hasEnoughTokens && (
              <div className="mt-3 p-2 bg-red-100 border border-red-300 rounded-md">
                <div className="text-red-700 font-medium">
                  ⚠️ You don't have enough tokens for this analysis. Please select a smaller date range.
                </div>
              </div>
            )}

            {estimate.hasEnoughTokens && estimate.totalEmails > 0 && (
              <div className="mt-3 p-2 bg-green-100 border border-green-300 rounded-md">
                <div className="text-green-700 font-medium">
                  ✓ You have enough tokens for this analysis. {estimate.remainingTokens - estimate.estimatedTokens} tokens will remain after analysis.
                </div>
              </div>
            )}
          </div>
        )}

        {/* Progress Component */}
        <PollingEmailAnalysisProgress
          ref={progressRef}
          onAnalysisComplete={handleAnalysisComplete}
          onAnalysisError={handleAnalysisError}
        />

        {/* Analyze Button */}
        <div className="flex justify-end">
          <button
            onClick={handleAnalyze}
            disabled={
              disabled ||
              isLoading ||
              fetchingEstimate ||
              !startDate ||
              !endDate ||
              !!errorMsg ||
              (estimate ? (!estimate.hasEnoughTokens || estimate.totalEmails === 0) : false)
            }
            data-testid="analyze-button"
            className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            {isLoading ? 'Starting Analysis...' : 'Start Email Analysis'}
          </button>
        </div>
      </div>
    </div>
  );
}
