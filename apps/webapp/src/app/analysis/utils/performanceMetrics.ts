'use client';

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  category: 'render' | 'api' | 'memory' | 'user-interaction';
  metadata?: Record<string, any>;
}

export interface PerformanceReport {
  componentName: string;
  metrics: PerformanceMetric[];
  summary: {
    averageRenderTime: number;
    averageApiTime: number;
    memoryUsage: number;
    totalInteractions: number;
  };
  generatedAt: number;
}

class PerformanceCollector {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private enabled: boolean = process.env.NODE_ENV === 'development';

  addMetric(componentName: string, metric: PerformanceMetric) {
    if (!this.enabled) return;

    if (!this.metrics.has(componentName)) {
      this.metrics.set(componentName, []);
    }

    const componentMetrics = this.metrics.get(componentName)!;
    componentMetrics.push(metric);

    // Keep only last 100 metrics per component to prevent memory leaks
    if (componentMetrics.length > 100) {
      componentMetrics.shift();
    }

    // Log performance warnings
    this.checkPerformanceThresholds(metric);
  }

  private checkPerformanceThresholds(metric: PerformanceMetric) {
    const thresholds = {
      render: 16, // 16ms for 60fps
      api: 1000,  // 1 second for API calls
      memory: 50 * 1024 * 1024, // 50MB memory usage
    };

    switch (metric.category) {
      case 'render':
        if (metric.value > thresholds.render) {
          console.warn(`🐌 Slow render detected: ${metric.name} took ${metric.value.toFixed(2)}ms`);
        }
        break;
      case 'api':
        if (metric.value > thresholds.api) {
          console.warn(`🐌 Slow API call detected: ${metric.name} took ${metric.value.toFixed(2)}ms`);
        }
        break;
      case 'memory':
        if (metric.value > thresholds.memory) {
          console.warn(`🧠 High memory usage detected: ${metric.name} using ${(metric.value / 1024 / 1024).toFixed(2)}MB`);
        }
        break;
    }
  }

  getReport(componentName: string): PerformanceReport | null {
    if (!this.enabled) return null;

    const componentMetrics = this.metrics.get(componentName);
    if (!componentMetrics || componentMetrics.length === 0) {
      return null;
    }

    const renderMetrics = componentMetrics.filter(m => m.category === 'render');
    const apiMetrics = componentMetrics.filter(m => m.category === 'api');
    const memoryMetrics = componentMetrics.filter(m => m.category === 'memory');
    const interactionMetrics = componentMetrics.filter(m => m.category === 'user-interaction');

    const averageRenderTime = renderMetrics.length > 0
      ? renderMetrics.reduce((sum, m) => sum + m.value, 0) / renderMetrics.length
      : 0;

    const averageApiTime = apiMetrics.length > 0
      ? apiMetrics.reduce((sum, m) => sum + m.value, 0) / apiMetrics.length
      : 0;

    const latestMemoryUsage = memoryMetrics.length > 0
      ? memoryMetrics[memoryMetrics.length - 1].value
      : 0;

    return {
      componentName,
      metrics: [...componentMetrics],
      summary: {
        averageRenderTime,
        averageApiTime,
        memoryUsage: latestMemoryUsage,
        totalInteractions: interactionMetrics.length
      },
      generatedAt: Date.now()
    };
  }

  getAllReports(): PerformanceReport[] {
    if (!this.enabled) return [];

    return Array.from(this.metrics.keys())
      .map(componentName => this.getReport(componentName))
      .filter((report): report is PerformanceReport => report !== null);
  }

  clearMetrics(componentName?: string) {
    if (!this.enabled) return;

    if (componentName) {
      this.metrics.delete(componentName);
    } else {
      this.metrics.clear();
    }
  }

  exportMetrics(): string {
    if (!this.enabled) return '{}';

    const reports = this.getAllReports();
    return JSON.stringify({
      exportedAt: new Date().toISOString(),
      reports,
      summary: {
        totalComponents: reports.length,
        totalMetrics: reports.reduce((sum, r) => sum + r.metrics.length, 0),
        averageRenderTime: reports.reduce((sum, r) => sum + r.summary.averageRenderTime, 0) / reports.length,
        averageApiTime: reports.reduce((sum, r) => sum + r.summary.averageApiTime, 0) / reports.length
      }
    }, null, 2);
  }

  logSummary() {
    if (!this.enabled) return;

    const reports = this.getAllReports();
    if (reports.length === 0) {
      console.log('📊 No performance metrics collected yet');
      return;
    }

    console.group('📊 Performance Summary');
    reports.forEach(report => {
      console.group(`🔧 ${report.componentName}`);
      console.log(`Average render time: ${report.summary.averageRenderTime.toFixed(2)}ms`);
      console.log(`Average API time: ${report.summary.averageApiTime.toFixed(2)}ms`);
      console.log(`Memory usage: ${(report.summary.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
      console.log(`Total interactions: ${report.summary.totalInteractions}`);
      console.groupEnd();
    });
    console.groupEnd();
  }
}

// Global performance collector instance
export const performanceCollector = new PerformanceCollector();

// Helper functions for common metrics
export const recordRenderTime = (componentName: string, duration: number, metadata?: Record<string, any>) => {
  performanceCollector.addMetric(componentName, {
    name: 'render',
    value: duration,
    unit: 'ms',
    timestamp: Date.now(),
    category: 'render',
    metadata
  });
};

export const recordApiCall = (componentName: string, apiName: string, duration: number, metadata?: Record<string, any>) => {
  performanceCollector.addMetric(componentName, {
    name: apiName,
    value: duration,
    unit: 'ms',
    timestamp: Date.now(),
    category: 'api',
    metadata
  });
};

export const recordMemoryUsage = (componentName: string, usage: number, metadata?: Record<string, any>) => {
  performanceCollector.addMetric(componentName, {
    name: 'memory-usage',
    value: usage,
    unit: 'bytes',
    timestamp: Date.now(),
    category: 'memory',
    metadata
  });
};

export const recordUserInteraction = (componentName: string, interactionType: string, duration?: number, metadata?: Record<string, any>) => {
  performanceCollector.addMetric(componentName, {
    name: interactionType,
    value: duration || 0,
    unit: duration ? 'ms' : 'count',
    timestamp: Date.now(),
    category: 'user-interaction',
    metadata
  });
};

// Development helper to log performance summary
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).logPerformanceSummary = () => performanceCollector.logSummary();
  (window as any).exportPerformanceMetrics = () => {
    const data = performanceCollector.exportMetrics();
    console.log('📊 Performance Metrics Export:', data);
    return data;
  };
}
