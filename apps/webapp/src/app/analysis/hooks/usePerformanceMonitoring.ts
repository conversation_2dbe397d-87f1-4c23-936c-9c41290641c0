'use client';

import { useEffect, useRef, useCallback } from 'react';
import { performanceCollector, recordRenderTime, recordApiCall, recordMemoryUsage } from '../utils/performanceMetrics';

export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
  apiCallDuration: number;
  lastUpdate: number;
}

export interface UsePerformanceMonitoringProps {
  componentName: string;
  enabled?: boolean;
  logToConsole?: boolean;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

export function usePerformanceMonitoring({
  componentName,
  enabled = process.env.NODE_ENV === 'development',
  logToConsole = true,
  onMetricsUpdate
}: UsePerformanceMonitoringProps) {
  const renderStartTime = useRef<number>(0);
  const apiCallStartTime = useRef<number>(0);
  const metricsRef = useRef<PerformanceMetrics>({
    renderTime: 0,
    apiCallDuration: 0,
    lastUpdate: Date.now()
  });

  // Start render timing
  const startRenderTiming = useCallback(() => {
    if (!enabled) return;
    renderStartTime.current = performance.now();
  }, [enabled]);

  // End render timing
  const endRenderTiming = useCallback(() => {
    if (!enabled || renderStartTime.current === 0) return;

    const renderTime = performance.now() - renderStartTime.current;
    metricsRef.current.renderTime = renderTime;
    metricsRef.current.lastUpdate = Date.now();

    // Record in performance collector
    recordRenderTime(componentName, renderTime);

    if (logToConsole) {
      console.log(`[${componentName}] Render time: ${renderTime.toFixed(2)}ms`);
    }

    onMetricsUpdate?.(metricsRef.current);
    renderStartTime.current = 0;
  }, [enabled, componentName, logToConsole, onMetricsUpdate]);

  // Start API call timing
  const startApiTiming = useCallback(() => {
    if (!enabled) return;
    apiCallStartTime.current = performance.now();
  }, [enabled]);

  // End API call timing
  const endApiTiming = useCallback((apiName: string = 'api-call') => {
    if (!enabled || apiCallStartTime.current === 0) return;

    const apiCallDuration = performance.now() - apiCallStartTime.current;
    metricsRef.current.apiCallDuration = apiCallDuration;
    metricsRef.current.lastUpdate = Date.now();

    // Record in performance collector
    recordApiCall(componentName, apiName, apiCallDuration);

    if (logToConsole) {
      console.log(`[${componentName}] ${apiName} duration: ${apiCallDuration.toFixed(2)}ms`);
    }

    onMetricsUpdate?.(metricsRef.current);
    apiCallStartTime.current = 0;
  }, [enabled, componentName, logToConsole, onMetricsUpdate]);

  // Get memory usage
  const getMemoryUsage = useCallback(() => {
    if (!enabled) return null;
    
    const memory = (performance as any).memory;
    if (!memory) return null;

    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit
    };
  }, [enabled]);

  // Update memory metrics
  const updateMemoryMetrics = useCallback(() => {
    if (!enabled) return;

    const memoryUsage = getMemoryUsage();
    if (memoryUsage) {
      metricsRef.current.memoryUsage = memoryUsage;
      metricsRef.current.lastUpdate = Date.now();

      // Record in performance collector
      recordMemoryUsage(componentName, memoryUsage.usedJSHeapSize);

      if (logToConsole) {
        const usedMB = (memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(2);
        const totalMB = (memoryUsage.totalJSHeapSize / 1024 / 1024).toFixed(2);
        console.log(`[${componentName}] Memory usage: ${usedMB}MB / ${totalMB}MB`);
      }

      onMetricsUpdate?.(metricsRef.current);
    }
  }, [enabled, componentName, logToConsole, onMetricsUpdate, getMemoryUsage]);

  // Measure component lifecycle
  const measureComponentLifecycle = useCallback((phase: 'mount' | 'update' | 'unmount') => {
    if (!enabled) return;
    
    const timestamp = performance.now();
    
    if (logToConsole) {
      console.log(`[${componentName}] Component ${phase} at ${timestamp.toFixed(2)}ms`);
    }
  }, [enabled, componentName, logToConsole]);

  // Performance observer for long tasks
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    let observer: PerformanceObserver | null = null;

    try {
      observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'longtask') {
            if (logToConsole) {
              console.warn(`[${componentName}] Long task detected: ${entry.duration.toFixed(2)}ms`);
            }
          }
        });
      });

      observer.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      // PerformanceObserver might not be supported
      console.warn('PerformanceObserver not supported');
    }

    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, [enabled, componentName, logToConsole]);

  // Periodic memory monitoring
  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(updateMemoryMetrics, 10000); // Every 10 seconds

    return () => {
      clearInterval(interval);
    };
  }, [enabled, updateMemoryMetrics]);

  // Component lifecycle tracking
  useEffect(() => {
    measureComponentLifecycle('mount');
    
    return () => {
      measureComponentLifecycle('unmount');
    };
  }, [measureComponentLifecycle]);

  useEffect(() => {
    measureComponentLifecycle('update');
  });

  // Wrap async functions with timing
  const wrapAsyncFunction = useCallback(<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    name: string
  ): T => {
    if (!enabled) return fn;

    return (async (...args: any[]) => {
      const startTime = performance.now();
      try {
        const result = await fn(...args);
        const duration = performance.now() - startTime;
        
        if (logToConsole) {
          console.log(`[${componentName}] ${name} completed in ${duration.toFixed(2)}ms`);
        }
        
        return result;
      } catch (error) {
        const duration = performance.now() - startTime;
        
        if (logToConsole) {
          console.error(`[${componentName}] ${name} failed after ${duration.toFixed(2)}ms:`, error);
        }
        
        throw error;
      }
    }) as T;
  }, [enabled, componentName, logToConsole]);

  return {
    startRenderTiming,
    endRenderTiming,
    startApiTiming,
    endApiTiming,
    updateMemoryMetrics,
    getMemoryUsage,
    measureComponentLifecycle,
    wrapAsyncFunction,
    metrics: metricsRef.current
  };
}
