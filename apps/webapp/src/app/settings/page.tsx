'use client'

import { useAuth, useUser } from '@clerk/nextjs'
import { useEffect, useState } from 'react'
import { Card } from '@/app/components/ui/Card'

interface TokenInfo {
  remaining: number
  total: number
}

export default function SettingsPage() {
  const { isLoaded, isSignedIn } = useAuth()
  const { user } = useUser()
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      fetchTokenInfo()
    }
  }, [isLoaded, isSignedIn])

  const fetchTokenInfo = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/tokens')
      if (!response.ok) {
        throw new Error(`Failed to fetch token info: ${response.status}`)
      }

      const data = await response.json()
      setTokenInfo(data)
    } catch (err) {
      console.error('Error fetching token info:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch token information')
    } finally {
      setLoading(false)
    }
  }

  if (!isLoaded) {
    return <div className="flex justify-center items-center h-64">Loading...</div>
  }

  if (!isSignedIn) {
    return <div className="flex justify-center items-center h-64">Please sign in to view settings.</div>
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
        <p className="text-gray-600">Manage your account and application preferences</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Account Information */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Account Information</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <div className="text-gray-900">{user?.fullName || 'Not provided'}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <div className="text-gray-900">{user?.primaryEmailAddress?.emailAddress || 'Not provided'}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Account Created</label>
              <div className="text-gray-900">
                {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Last Sign In</label>
              <div className="text-gray-900">
                {user?.lastSignInAt ? new Date(user.lastSignInAt).toLocaleDateString() : 'Unknown'}
              </div>
            </div>
          </div>
        </Card>

        {/* Token Usage */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Token Usage</h2>
          {loading && <div className="text-center py-4">Loading token information...</div>}
          {error && (
            <div className="text-center py-4">
              <p className="text-red-600 mb-2">{error}</p>
              <button
                onClick={fetchTokenInfo}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          )}
          {tokenInfo && !loading && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Remaining Tokens</label>
                <div className="text-2xl font-bold text-blue-600">{tokenInfo.remaining.toLocaleString()}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Total Tokens</label>
                <div className="text-lg text-gray-900">{tokenInfo.total.toLocaleString()}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Usage</label>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{
                      width: `${((tokenInfo.total - tokenInfo.remaining) / tokenInfo.total) * 100}%`
                    }}
                  ></div>
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {((tokenInfo.total - tokenInfo.remaining) / tokenInfo.total * 100).toFixed(1)}% used
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* Email Analysis Settings */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Email Analysis Settings</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Default Analysis Period
              </label>
              <select className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="daily">Daily</option>
              </select>
            </div>
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  defaultChecked
                />
                <span className="text-sm text-gray-700">Enable real-time progress updates</span>
              </label>
            </div>
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  defaultChecked
                />
                <span className="text-sm text-gray-700">Auto-refresh metrics dashboard</span>
              </label>
            </div>
          </div>
        </Card>

        {/* Application Preferences */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Application Preferences</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Theme
              </label>
              <select className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="system">System</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date Format
              </label>
              <select className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
              </select>
            </div>
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  defaultChecked
                />
                <span className="text-sm text-gray-700">Show detailed error messages</span>
              </label>
            </div>
          </div>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="mt-8 flex justify-between">
        <button className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
          Export Data
        </button>
        <div className="space-x-4">
          <button className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Save Changes
          </button>
          <button className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
            Reset to Defaults
          </button>
        </div>
      </div>
    </div>
  )
}
