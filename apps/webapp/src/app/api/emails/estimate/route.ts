import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import { ServiceContainer } from '@/services/container'
const { Database } = require('@webapp/services')

// Mark this route as dynamic to prevent static generation errors
export const dynamic = 'force-dynamic'

export async function POST(request: Request) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const { startDate, endDate } = await request.json()
    if (!startDate || !endDate) {
      return new NextResponse('Missing required fields', { status: 400 })
    }

    console.log('Received date range:', { startDate, endDate })

    const container = new ServiceContainer()
    const emailService = container.getEmailService()
    const tokenService = container.getTokenService()
    const database = new Database()

    if (!emailService || !tokenService) {
      console.error('Failed to get services', {
        hasEmailService: !!emailService,
        hasTokenService: !!tokenService
      })
      return new NextResponse('Service unavailable', { status: 500 })
    }

    // Create properly formatted Date objects
    const startDateObj = new Date(startDate + 'T00:00:00Z')
    const endDateObj = new Date(endDate + 'T23:59:59Z')

    console.log('Using date objects:', {
      startDateObj: startDateObj.toISOString(),
      endDateObj: endDateObj.toISOString()
    })

    try {
      // Get current token balance first
      const remainingTokens = await tokenService.getRemainingTokens(userId)

      // Get Gmail service to count emails
      const authService = container.getAuthService()
      if (!authService) {
        console.error('Failed to get auth service')
        return new NextResponse('Service unavailable', { status: 500 })
      }
      const gmailService = await authService.getGmailClientForUser(userId)

      // Get email IDs in the date range
      const startDateFormatted = startDateObj.toISOString().split('T')[0].replace(/-/g, '/')
      const endDateFormatted = endDateObj.toISOString().split('T')[0].replace(/-/g, '/')
      const emailIds = await gmailService.getEmailsByDateRange(startDateFormatted, endDateFormatted)
      const totalEmails = emailIds.length

      // Check which emails already have cached analysis to provide accurate estimate
      let emailsNeedingTokens = 0;

      for (const email of emailIds) {
        const docId = `${userId}_${gmailService.getMonitoredEmail()}_${email.id}`;
        const analysisDoc = await database.collection('emailAnalysis').doc(docId).get();

        // If no cached analysis exists or version is outdated, it needs tokens
        if (!analysisDoc.exists || !analysisDoc.data()?.version) {
          emailsNeedingTokens++;
        }
      }

      const hasEnoughTokens = remainingTokens >= emailsNeedingTokens

      console.log('Token estimate:', {
        totalEmails,
        emailsNeedingTokens,
        hasEnoughTokens,
        remainingTokens
      })

      return NextResponse.json({
        estimatedTokens: emailsNeedingTokens,
        totalEmails,
        hasEnoughTokens,
        remainingTokens
      })
    } catch (error) {
      console.error('Failed to estimate token usage:', error)

      // Provide a more graceful fallback for UI
      return NextResponse.json({
        estimatedTokens: 0,
        totalEmails: 0,
        hasEnoughTokens: true,
        remainingTokens: 0,
        error: error instanceof Error ? error.message : 'Failed to estimate token usage'
      })
    }
  } catch (error) {
    console.error('Failed to estimate token usage:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}