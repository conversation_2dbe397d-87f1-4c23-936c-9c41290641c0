import { NextRequest, NextResponse } from 'next/server'

// Mark this route as dynamic to prevent static generation errors
export const dynamic = 'force-dynamic'

interface ProgressWebhookBody {
  clerkUserId: string
  messageId: string
  status: 'processing' | 'completed' | 'error'
  current?: string
  processed?: number
  total?: number
  analysis?: {
    company_name?: string
    email_type_category?: string
    is_related_to_job_search?: boolean
  }
  error?: string
}

export async function POST(req: NextRequest) {
  try {
    console.log('Progress Webhook: POST request received')

    // Parse the request body
    const body = await req.json() as ProgressWebhookBody
    const { clerkUserId, messageId, status, current, processed, total, analysis, error } = body

    if (!clerkUserId || !messageId || !status) {
      console.error('Progress Webhook: Missing required fields', body)
      return NextResponse.json({ message: 'Missing required fields' }, { status: 400 })
    }

    console.log('Progress Webhook: Received progress update', {
      clerkUserId,
      messageId,
      status,
      processed,
      total
    })

    // Progress updates are now handled via polling-based architecture
    // The Cloud Functions update job status in the database, which is polled by the frontend
    console.log('Progress webhook received:', {
      clerkUserId,
      messageId,
      status,
      current,
      processed,
      total,
      analysis: analysis ? 'present' : 'none'
    })

    return NextResponse.json({ message: 'Progress update received' })

  } catch (error) {
    console.error('Progress Webhook: Unexpected error', error)
    return NextResponse.json(
      { message: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    )
  }
}
