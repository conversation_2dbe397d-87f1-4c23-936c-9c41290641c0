import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Database, Logger } from '@webapp/services';

const logger = new Logger();
const database = new Database();

export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    // Verify authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId } = params;

    // Get job status from database
    const jobDoc = await database.collection('analysisJobs').doc(jobId).get();

    if (!jobDoc.exists) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    const jobData = jobDoc.data();

    // Verify the job belongs to the authenticated user
    if (jobData?.clerkUserId !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Calculate additional fields for enhanced response
    const total = jobData.total || 0;
    const processed = jobData.processed || 0;
    const pendingEmails = Math.max(0, total - processed);
    const progressPercentage = total > 0 ? Math.round((processed / total) * 100) : 0;
    const duration = jobData.completedAt && jobData.createdAt
      ? Math.round((jobData.completedAt.toMillis() - jobData.createdAt.toMillis()) / 1000)
      : undefined;

    // Return enhanced job status
    return NextResponse.json({
      jobId,
      status: jobData.status || 'unknown',
      sourceType: jobData.sourceType || 'manual', // NEW FIELD

      progress: {
        total,
        processed,
        current: jobData.current || '',
        cachedCount: jobData.cachedCount || 0,
        newAnalysisCount: jobData.newAnalysisCount || 0,
        remainingTokens: jobData.remainingTokens,
        error: jobData.error,
        percentage: progressPercentage, // NEW: calculated field
        pendingEmails // NEW: calculated field
      },

      // Enhanced timing information
      timing: { // NEW SECTION
        createdAt: jobData.createdAt?.toISOString(),
        updatedAt: jobData.updatedAt?.toISOString(),
        completedAt: jobData.completedAt?.toISOString(),
        duration // seconds
      },

      // Email analysis details
      emailDetails: { // NEW SECTION
        dateRange: {
          start: jobData.startDate || '',
          end: jobData.endDate || ''
        },
        monitoredEmail: jobData.monitoredEmail || '',
        totalEmails: total,
        pendingEmails
      },

      result: jobData.result
    });

  } catch (error) {
    logger.error('Error fetching job status', {
      error: error instanceof Error ? error.message : String(error),
      jobId: params.jobId
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
