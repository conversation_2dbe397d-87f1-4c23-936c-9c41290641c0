import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
const { ServiceContainer } = require('@/services/container')

// Mark this route as dynamic to prevent static generation errors
export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Get URL parameters
    const url = new URL(request.url)
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')
    const period = url.searchParams.get('period') || 'weekly'

    // Validate parameters
    if (!startDate || !endDate) {
      return new NextResponse('Missing required parameters: startDate and endDate', { status: 400 })
    }

    // Validate period
    if (!['daily', 'weekly', 'monthly'].includes(period)) {
      return new NextResponse('Invalid period. Must be one of: daily, weekly, monthly', { status: 400 })
    }

    console.log('Metrics API: Fetching metrics', { userId, startDate, endDate, period })

    // Initialize services with error handling
    let container, metricsService
    try {
      container = new ServiceContainer()
      metricsService = container.getMetricsService()
    } catch (containerError) {
      console.error('Metrics API: Failed to initialize ServiceContainer', containerError)
      return NextResponse.json(
        {
          error: 'Service initialization failed',
          applications: { [period]: [] },
          interviews: { [period]: [] }
        },
        { status: 500 }
      )
    }

    if (!metricsService) {
      console.error('Metrics API: Failed to get metrics service')
      return NextResponse.json(
        {
          error: 'Metrics service unavailable',
          applications: { [period]: [] },
          interviews: { [period]: [] }
        },
        { status: 500 }
      )
    }

    try {
      // Convert string dates to Date objects with proper time ranges
      const startDateObj = new Date(startDate + 'T00:00:00Z')
      const endDateObj = new Date(endDate + 'T23:59:59Z')

      // Validate date objects
      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        console.error('Metrics API: Invalid date format', { startDate, endDate })
        return NextResponse.json(
          {
            error: 'Invalid date format. Please use YYYY-MM-DD format.',
            applications: { [period]: [] },
            interviews: { [period]: [] }
          },
          { status: 400 }
        )
      }

      // Get metrics data
      const metrics = await metricsService.getMetrics({
        userId,
        startDate: startDateObj,
        endDate: endDateObj,
        period
      })

      // Check if metrics data is valid
      if (!metrics || !metrics.labels) {
        console.error('Metrics API: Invalid metrics data returned', { metrics })
        return NextResponse.json(
          {
            error: 'No valid metrics data available',
            applications: { [period]: [] },
            interviews: { [period]: [] }
          },
          { status: 404 }
        )
      }

      // Format data for the frontend
      const formattedData = {
        applications: {
          [period]: metrics.labels.map((date: any, index: number) => ({
            date,
            value: metrics.applications[index]
          }))
        },
        interviews: {
          [period]: metrics.labels.map((date: any, index: number) => ({
            date,
            value: metrics.interviews[index]
          }))
        }
      }

      console.log('Metrics API: Successfully fetched metrics', {
        userId,
        dataPoints: metrics.labels.length
      })

      return NextResponse.json(formattedData)
    } catch (error) {
      console.error('Metrics API: Error fetching metrics', error)

      // Return a more graceful error response
      return NextResponse.json(
        {
          error: error instanceof Error ? error.message : 'Failed to fetch metrics',
          // Return empty data structure to prevent frontend errors
          applications: { [period]: [] },
          interviews: { [period]: [] }
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Metrics API: Unexpected error', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
