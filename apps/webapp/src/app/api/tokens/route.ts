import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
const { ServiceContainer } = require('@/services/container')
const { ConfigService } = require('@webapp/services')

// Mark this route as dynamic to prevent static generation errors
export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const { userId } = await auth()
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    let container, tokenService
    try {
      container = new ServiceContainer()
      tokenService = container.getTokenService()
    } catch (containerError) {
      console.error('Tokens API: Failed to initialize ServiceContainer', containerError)
      return NextResponse.json(
        { error: 'Service initialization failed. Please try again later.' },
        { status: 503 }
      )
    }

    if (!tokenService) {
      console.error('Tokens API: Token service not available')
      return NextResponse.json(
        { error: 'Token service temporarily unavailable. Please try again later.' },
        { status: 503 }
      )
    }

    try {
      console.log('Tokens API: Getting tokens for user:', userId)

      // Get remaining tokens for the user
      const remainingTokens = await tokenService.getRemainingTokens(userId)

      // Get total tokens from configuration service
      const config = ConfigService.getInstance()
      const totalTokens = config.getUserTokenLimit()

      console.log('Tokens API: Retrieved tokens:', {
        userId,
        remainingTokens,
        totalTokens
      })

      return NextResponse.json({
        remaining: remainingTokens,
        total: totalTokens,
        usage: {
          used: totalTokens - remainingTokens,
          percentage: Math.round(((totalTokens - remainingTokens) / totalTokens) * 100)
        }
      })
    } catch (error) {
      console.error('Error accessing token service:', error)

      // Return proper error response
      return NextResponse.json(
        {
          error: 'Failed to retrieve token information. Please try again later.',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Failed to fetch token counts:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}