"use client"

import { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react'
import { useTokens } from '../TokenContext'
import { useAuth } from '@clerk/nextjs'

// Define the progress state interface
interface ProgressState {
  total: number
  processed: number
  current: string
  status: 'idle' | 'processing' | 'completed' | 'error'
  cachedCount: number
  newAnalysisCount?: number
  remainingTokens?: number
}

// Define the component handle interface exposed via ref
export interface EmailAnalysisProgressHandles {
  // New method that does both connection management and analysis triggering
  startAnalysis: (startDate: string, endDate: string) => Promise<void>
  // Keep isConnected for compatibility during transition
  isConnected: () => boolean
}

export const EmailAnalysisProgress = forwardRef<EmailAnalysisProgressHandles, {}>((props, ref) => {
  // Default progress state
  const [progress, setProgress] = useState<ProgressState>({
    total: 0,
    processed: 0,
    current: 'Waiting for connection...',
    status: 'idle',
    cachedCount: 0,
    newAnalysisCount: 0
  })

  const [connectionState, setConnectionState] = useState<
    'connecting' | 'open' | 'closed' | 'error'
  >('connecting')

  const [errorMsg, setErrorMsg] = useState<string | null>(null)
  const [lastMessageTime, setLastMessageTime] = useState<number>(Date.now())
  const [retryCount, setRetryCount] = useState<number>(0)

  // Store eventSource in a ref to keep it stable across renders
  const eventSourceRef = useRef<EventSource | null>(null)
  const pingTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const analysisInProgressRef = useRef<boolean>(false)
  // Track if a message was received - used for reliable connection detection
  const receivedMessageRef = useRef<boolean>(false)

  // Get token utilities
  const { updateTokens, refreshTokens } = useTokens()

  // Get Clerk auth information
  const { getToken } = useAuth()

  // Define setupEventSource function before it's used
  const setupEventSource = () => {
    setConnectionState('connecting')
    console.log('EmailAnalysisProgress: Creating new EventSource connection')

    // Close previous connection if exists
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
    }

    // Clear any existing ping timeout
    if (pingTimeoutRef.current) {
      clearTimeout(pingTimeoutRef.current)
    }

    try {
      // Get the session token for authentication
      const getSessionToken = async () => {
        try {
          return await getToken();
        } catch (error) {
          console.error('EmailAnalysisProgress: Error getting session token:', error);
          return null;
        }
      };

      // We need to get the token before creating the EventSource
      getSessionToken().then(token => {
        // Add timestamp to prevent caching issues and include the session token
        const url = `/api/emails/progress?t=${Date.now()}${token ? `&token=${token}` : ''}`;
        console.log('EmailAnalysisProgress: Creating EventSource with auth token');
        eventSourceRef.current = new EventSource(url);

        // Setup a timeout to detect stalled connections
        pingTimeoutRef.current = setTimeout(() => {
          console.warn('EmailAnalysisProgress: No message received for 60 seconds, reconnecting')
          if (eventSourceRef.current) {
            eventSourceRef.current.close()
            setRetryCount(prev => prev + 1)
            setupEventSource()
          }
        }, 60000)

        eventSourceRef.current.onopen = () => {
          console.log('EmailAnalysisProgress: EventSource connection opened')
          // Don't immediately set connectionState to open
          // Wait for the first real message to confirm the connection is working
          setLastMessageTime(Date.now())
        }

        eventSourceRef.current.onmessage = (event) => {
          console.log('EmailAnalysisProgress: Received message:', event.data)
          setLastMessageTime(Date.now())

          // Update our ref to indicate we've received at least one message
          receivedMessageRef.current = true

          // Once we receive any message, the connection is confirmed working
          if (connectionState !== 'open') {
            console.log('EmailAnalysisProgress: Connection confirmed via message')
            setConnectionState('open')
            setRetryCount(0) // Reset retry count on successful connection
          }

          // Reset ping timeout since we received a message
          if (pingTimeoutRef.current) {
            clearTimeout(pingTimeoutRef.current)
            pingTimeoutRef.current = setTimeout(() => {
              console.warn('EmailAnalysisProgress: No message received for 60 seconds, reconnecting')
              if (eventSourceRef.current) {
                eventSourceRef.current.close()
                setRetryCount(prev => prev + 1)
                setupEventSource()
              }
            }, 60000)
          }

          try {
            // Ignore ping messages (they start with ":")
            if (event.data && event.data.startsWith(':')) {
              console.log('EmailAnalysisProgress: Received ping')
              return
            }

            const data = JSON.parse(event.data)
            console.log('EmailAnalysisProgress: Parsed data:', data)

            if (data && typeof data === 'object') {
              // Only update if status is different or processing
              if (data.status !== progress.status || data.status === 'processing') {
                 setProgress(data)
              }

              // Update token context if we received token information
              if (typeof data.remainingTokens === 'number') {
                updateTokens(data.remainingTokens)
              }

              // If we get a completed or error status, mark analysis as no longer in progress
              if (data.status === 'completed' || data.status === 'error') {
                console.log(`EmailAnalysisProgress: Process ${data.status}, updating state`)
                analysisInProgressRef.current = false

                // Refresh tokens one more time when complete
                refreshTokens()
              }
            }
          } catch (error) {
            console.error('EmailAnalysisProgress: Error parsing event data:', error)
          }
        }

        eventSourceRef.current.onerror = (error) => {
          console.error('EmailAnalysisProgress: EventSource error:', error)
          setConnectionState('error')
          setErrorMsg('Connection error. Will attempt to reconnect...')

          if (eventSourceRef.current) {
            eventSourceRef.current.close()
          }

          // Retry connection with exponential backoff
          const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000)
          console.log(`EmailAnalysisProgress: Retrying in ${retryDelay}ms (retry #${retryCount + 1})`)

          setTimeout(() => {
            setRetryCount(prev => prev + 1)
            setupEventSource()
          }, retryDelay)
        }
      }).catch(error => {
        console.error('EmailAnalysisProgress: Error setting up EventSource:', error)
        setConnectionState('error')
        setErrorMsg(`Failed to connect: ${error instanceof Error ? error.message : String(error)}`)

        // Retry connection
        setTimeout(() => {
          setRetryCount(prev => prev + 1)
          setupEventSource()
        }, 5000)
      });
    } catch (error) {
      console.error('EmailAnalysisProgress: Error in setupEventSource:', error)
      setConnectionState('error')
      setErrorMsg(`Failed to connect: ${error instanceof Error ? error.message : String(error)}`)

      // Retry connection
      setTimeout(() => {
        setRetryCount(prev => prev + 1)
        setupEventSource()
      }, 5000)
    }
  }

  // Helper method to ensure connection is ready
  const ensureConnectionReady = async (): Promise<boolean> => {
    console.log('EmailAnalysisProgress: Ensuring connection is ready', {
      connectionState,
      receivedMessage: receivedMessageRef.current
    })

    // If already open and receiving messages, we're good
    if (connectionState === 'open' && receivedMessageRef.current) {
      return true
    }

    // If closed or error, try to reconnect
    if (connectionState === 'closed' || connectionState === 'error') {
      console.log('EmailAnalysisProgress: Connection closed/error, reconnecting')
      setupEventSource()
    }

    // Wait for the connection to open, with timeout
    return new Promise<boolean>((resolve) => {
      // Set a timeout in case connection never establishes
      const timeout = setTimeout(() => {
        console.log('EmailAnalysisProgress: Connection timeout')
        resolve(false)
      }, 5000)

      // Check connection state immediately and every 200ms
      const checkConnection = () => {
        // If connection is open and we've received at least one message, we're ready
        if (connectionState === 'open' && receivedMessageRef.current) {
          clearTimeout(timeout)
          resolve(true)
          return
        }

        // If connection failed, resolve with false
        if (connectionState === 'error') {
          clearTimeout(timeout)
          resolve(false)
          return
        }

        // Otherwise, check again in 200ms
        setTimeout(checkConnection, 200)
      }

      checkConnection()
    })
  }

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    // For backward compatibility during transition
    isConnected: () => {
      const isConnected = connectionState === 'open' || receivedMessageRef.current;
      console.log('EmailAnalysisProgress: isConnected check', {
        connectionState,
        receivedMessage: receivedMessageRef.current,
        returning: isConnected
      });
      return isConnected;
    },

    // New streamlined method that:
    // 1. Ensures connection is active
    // 2. Makes the API call to trigger analysis
    startAnalysis: async (startDate: string, endDate: string) => {
      console.log('EmailAnalysisProgress: startAnalysis called', { startDate, endDate })

      if (analysisInProgressRef.current) {
        console.warn('EmailAnalysisProgress: Analysis already in progress')
        throw new Error('Analysis already in progress')
      }

      analysisInProgressRef.current = true

      try {
        // First, ensure connection is active
        const isReady = await ensureConnectionReady()
        if (!isReady) {
          analysisInProgressRef.current = false
          throw new Error('Failed to establish SSE connection')
        }

        // Now trigger the analysis
        console.log('EmailAnalysisProgress: Connection ready, starting analysis', { startDate, endDate })

        // Reset progress for new analysis
        setProgress(prev => ({
          ...prev,
          status: 'processing',
          current: 'Starting analysis...',
          processed: 0,
          total: 0
        }))

        // Make API call to start analysis
        const token = await getToken()
        if (!token) {
          throw new Error('Authentication required')
        }

        const response = await fetch('/api/emails/analyze', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ startDate, endDate }),
        })

        const results = await response.json()

        // Update token information if available
        if (typeof results.remainingTokens === 'number') {
          updateTokens(results.remainingTokens)
        }

        if (!response.ok) {
          console.error('EmailAnalysisProgress: Analysis API call failed', results)
          setProgress(prev => ({
            ...prev,
            status: 'error',
            current: results.message || 'Analysis request failed'
          }))
          analysisInProgressRef.current = false
          throw new Error(results.message || 'Analysis failed')
        }

        console.log('EmailAnalysisProgress: Analysis successfully initiated', results)
        return
      } catch (error) {
        analysisInProgressRef.current = false
        console.error('EmailAnalysisProgress: Error starting analysis:', error)
        setProgress(prev => ({
          ...prev,
          status: 'error',
          current: `Error: ${error instanceof Error ? error.message : String(error)}`
        }))
        throw error
      }
    }
  }))

  // Set up EventSource connection
  useEffect(() => {
    console.log('EmailAnalysisProgress: Setting up EventSource connection')

    // Initial setup
    setupEventSource()

    // Cleanup on unmount
    return () => {
      console.log('EmailAnalysisProgress: Cleaning up - closing EventSource connection')
      if (eventSourceRef.current) {
        setConnectionState('closed')
        eventSourceRef.current.close()
      }
      if (pingTimeoutRef.current) {
        clearTimeout(pingTimeoutRef.current)
      }
      // Reset the message received flag when unmounting
      receivedMessageRef.current = false
    }
  }, [updateTokens, refreshTokens]) // Removed retryCount from dependency array to prevent loops

  // Render the component UI
  return (
    <div className="mb-8 p-6 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4">Email Analysis Progress</h2>

      {connectionState === 'error' && errorMsg && (
        <div className="mb-4 p-3 bg-red-100 text-red-800 rounded">
          {errorMsg}
        </div>
      )}

      <div className="mb-4">
        <div className="flex justify-between mb-2">
          <span>Status:</span>
          <span className="font-semibold">
            {progress.status === 'idle' && 'Ready'}
            {progress.status === 'processing' && 'Processing...'}
            {progress.status === 'completed' && 'Completed'}
            {progress.status === 'error' && 'Error'}
          </span>
        </div>

        {progress.status === 'processing' && (
          <div className="w-full bg-gray-200 rounded-full h-4 dark:bg-gray-700 mb-2">
            <div
              className="bg-blue-600 h-4 rounded-full transition-all duration-500"
              style={{ width: `${progress.total ? (progress.processed / progress.total) * 100 : 0}%` }}
            ></div>
          </div>
        )}

        {progress.status === 'processing' && (
          <div className="text-sm text-gray-600 mb-2">
            Processed {progress.processed} of {progress.total} emails
            {progress.cachedCount > 0 && ` (${progress.cachedCount} from cache)`}
          </div>
        )}

        <div className="text-sm">{progress.current}</div>
      </div>
    </div>
  )
})

// Add display name for better debugging
EmailAnalysisProgress.displayName = 'EmailAnalysisProgress'

export default EmailAnalysisProgress