'use client';

import React from 'react';
import { UserIcon, BoltIcon } from '@heroicons/react/24/outline';

export type SourceType = 'manual' | 'automatic';

export interface SourceTypeBadgeProps {
  sourceType: SourceType;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

const sourceTypeConfig = {
  manual: {
    label: 'Manual',
    bgColor: 'bg-purple-100',
    textColor: 'text-purple-800',
    borderColor: 'border-purple-200',
    icon: UserIcon,
    description: 'User-initiated analysis'
  },
  automatic: {
    label: 'Automatic',
    bgColor: 'bg-indigo-100',
    textColor: 'text-indigo-800',
    borderColor: 'border-indigo-200',
    icon: BoltIcon,
    description: 'Triggered by email notifications'
  }
};

const sizeConfig = {
  sm: {
    padding: 'px-2 py-1',
    text: 'text-xs',
    iconSize: 'w-3 h-3',
    gap: 'gap-1'
  },
  md: {
    padding: 'px-2.5 py-1.5',
    text: 'text-sm',
    iconSize: 'w-4 h-4',
    gap: 'gap-1.5'
  },
  lg: {
    padding: 'px-3 py-2',
    text: 'text-base',
    iconSize: 'w-5 h-5',
    gap: 'gap-2'
  }
};

export function SourceTypeBadge({
  sourceType,
  size = 'md',
  showIcon = true,
  className = ''
}: SourceTypeBadgeProps) {
  const config = sourceTypeConfig[sourceType];
  const sizeStyles = sizeConfig[size];
  const IconComponent = config.icon;

  return (
    <span
      data-testid="source-type-badge"
      className={`
        inline-flex items-center font-medium rounded-full border
        ${sizeStyles.padding} ${sizeStyles.text} ${sizeStyles.gap}
        ${config.bgColor} ${config.textColor} ${config.borderColor}
        ${className}
      `.trim()}
      title={config.description}
    >
      {showIcon && (
        <IconComponent className={`${sizeStyles.iconSize} flex-shrink-0`} />
      )}
      <span>{config.label}</span>
    </span>
  );
}

// Specialized source type badges for common use cases
export function ManualBadge(props: Omit<SourceTypeBadgeProps, 'sourceType'>) {
  return <SourceTypeBadge {...props} sourceType="manual" />;
}

export function AutomaticBadge(props: Omit<SourceTypeBadgeProps, 'sourceType'>) {
  return <SourceTypeBadge {...props} sourceType="automatic" />;
}
