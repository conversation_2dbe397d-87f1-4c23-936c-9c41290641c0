'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

export interface Column<T> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  emptyMessage?: string;
  loadingRows?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSort?: (key: string, order: 'asc' | 'desc') => void;
  className?: string;
  rowClassName?: (row: T, index: number) => string;
  onRowClick?: (row: T, index: number) => void;
}

export const DataTable = React.memo(function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  emptyMessage = 'No data available',
  loadingRows = 5,
  sortBy,
  sortOrder,
  onSort,
  className = '',
  rowClassName,
  onRowClick
}: DataTableProps<T>) {
  const [internalSortBy, setInternalSortBy] = useState<string>('');
  const [internalSortOrder, setInternalSortOrder] = useState<'asc' | 'desc'>('asc');

  const currentSortBy = sortBy || internalSortBy;
  const currentSortOrder = sortOrder || internalSortOrder;

  const handleSort = useCallback((columnKey: string) => {
    const newOrder = currentSortBy === columnKey && currentSortOrder === 'asc' ? 'desc' : 'asc';

    if (onSort) {
      onSort(columnKey, newOrder);
    } else {
      setInternalSortBy(columnKey);
      setInternalSortOrder(newOrder);
    }
  }, [currentSortBy, currentSortOrder, onSort]);

  const sortedData = useMemo(() => {
    if (!currentSortBy || loading) return data;

    return [...data].sort((a, b) => {
      const aValue = a[currentSortBy];
      const bValue = b[currentSortBy];

      if (aValue === bValue) return 0;

      let comparison = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else if (aValue instanceof Date && bValue instanceof Date) {
        comparison = aValue.getTime() - bValue.getTime();
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }

      return currentSortOrder === 'desc' ? -comparison : comparison;
    });
  }, [data, currentSortBy, currentSortOrder, loading]);

  const renderSortIcon = useCallback((columnKey: string) => {
    if (currentSortBy !== columnKey) {
      return <ChevronUpIcon className="w-4 h-4 text-gray-400" />;
    }

    return currentSortOrder === 'asc'
      ? <ChevronUpIcon className="w-4 h-4 text-gray-600" />
      : <ChevronDownIcon className="w-4 h-4 text-gray-600" />;
  }, [currentSortBy, currentSortOrder]);

  const renderLoadingRow = useCallback((index: number) => (
    <tr key={`loading-${index}`} className="animate-pulse">
      {columns.map((column, colIndex) => (
        <td key={colIndex} className="px-6 py-4 whitespace-nowrap">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </td>
      ))}
    </tr>
  ), [columns]);

  const renderDataRow = useCallback((row: T, index: number) => {
    const baseClassName = "hover:bg-gray-50 transition-colors duration-150";
    const clickableClassName = onRowClick ? "cursor-pointer" : "";
    const customClassName = rowClassName ? rowClassName(row, index) : "";
    const finalClassName = `${baseClassName} ${clickableClassName} ${customClassName}`.trim();

    return (
      <tr
        key={index}
        className={finalClassName}
        onClick={() => onRowClick?.(row, index)}
      >
        {columns.map((column, colIndex) => {
          const value = row[column.key];
          const alignClass = column.align === 'center' ? 'text-center' :
                           column.align === 'right' ? 'text-right' : 'text-left';

          return (
            <td
              key={colIndex}
              className={`px-6 py-4 whitespace-nowrap text-sm ${alignClass} ${column.className || ''}`}
              style={{ width: column.width }}
            >
              {column.render ? column.render(value, row) : String(value || '')}
            </td>
          );
        })}
      </tr>
    );
  }, [columns, onRowClick, rowClassName]);

  return (
    <div className={`overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg ${className}`}>
      <table className="min-w-full divide-y divide-gray-300">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((column, index) => {
              const alignClass = column.align === 'center' ? 'text-center' : 
                               column.align === 'right' ? 'text-right' : 'text-left';
              
              return (
                <th
                  key={index}
                  scope="col"
                  className={`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${alignClass} ${
                    column.sortable ? 'cursor-pointer select-none hover:bg-gray-100' : ''
                  }`}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(String(column.key))}
                >
                  <div className="flex items-center gap-1">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <span className="flex-shrink-0">
                        {renderSortIcon(String(column.key))}
                      </span>
                    )}
                  </div>
                </th>
              );
            })}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {loading ? (
            Array.from({ length: loadingRows }, (_, index) => renderLoadingRow(index))
          ) : sortedData.length > 0 ? (
            sortedData.map((row, index) => renderDataRow(row, index))
          ) : (
            <tr>
              <td 
                colSpan={columns.length} 
                className="px-6 py-12 text-center text-sm text-gray-500"
              >
                <div className="flex flex-col items-center">
                  <svg
                    className="w-12 h-12 text-gray-300 mb-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <p className="text-lg font-medium text-gray-900 mb-1">No data found</p>
                  <p className="text-gray-500">{emptyMessage}</p>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
}) as <T extends Record<string, any>>(props: DataTableProps<T>) => JSX.Element;
