'use client';

import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalRuns: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  pageSize: number;
}

export interface PaginationProps {
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  className?: string;
  showInfo?: boolean;
  maxVisiblePages?: number;
}

export function Pagination({
  pagination,
  onPageChange,
  className = '',
  showInfo = true,
  maxVisiblePages = 7
}: PaginationProps) {
  const {
    currentPage,
    totalPages,
    totalRuns,
    hasNextPage,
    hasPreviousPage,
    pageSize
  } = pagination;

  // Calculate visible page numbers
  const getVisiblePages = () => {
    const pages: (number | string)[] = [];
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show smart pagination with ellipsis
      const halfVisible = Math.floor(maxVisiblePages / 2);
      
      if (currentPage <= halfVisible + 1) {
        // Near the beginning
        for (let i = 1; i <= maxVisiblePages - 2; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - halfVisible) {
        // Near the end
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - (maxVisiblePages - 3); i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // In the middle
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - halfVisible + 1; i <= currentPage + halfVisible - 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  // Calculate result range
  const startResult = (currentPage - 1) * pageSize + 1;
  const endResult = Math.min(currentPage * pageSize, totalRuns);

  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  if (totalPages <= 1) {
    return showInfo ? (
      <div className={`flex items-center justify-between ${className}`}>
        <div className="text-sm text-gray-700">
          Showing {totalRuns} result{totalRuns !== 1 ? 's' : ''}
        </div>
      </div>
    ) : null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`} data-testid="pagination">
      {/* Results info */}
      {showInfo && (
        <div className="text-sm text-gray-700">
          Showing <span className="font-medium">{startResult}</span> to{' '}
          <span className="font-medium">{endResult}</span> of{' '}
          <span className="font-medium">{totalRuns}</span> results
        </div>
      )}

      {/* Pagination controls */}
      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
        {/* Previous button */}
        <button
          onClick={() => handlePageClick(currentPage - 1)}
          disabled={!hasPreviousPage}
          data-testid="previous-page"
          className={`
            relative inline-flex items-center px-2 py-2 rounded-l-md border text-sm font-medium
            ${hasPreviousPage
              ? 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700'
              : 'border-gray-300 bg-gray-100 text-gray-300 cursor-not-allowed'
            }
          `}
        >
          <span className="sr-only">Previous</span>
          <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
        </button>

        {/* Page numbers */}
        {visiblePages.map((page, index) => {
          if (page === '...') {
            return (
              <span
                key={`ellipsis-${index}`}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
              >
                ...
              </span>
            );
          }

          const pageNumber = page as number;
          const isCurrentPage = pageNumber === currentPage;

          return (
            <button
              key={pageNumber}
              onClick={() => handlePageClick(pageNumber)}
              data-testid={isCurrentPage ? "current-page" : `page-${pageNumber}`}
              className={`
                relative inline-flex items-center px-4 py-2 border text-sm font-medium
                ${isCurrentPage
                  ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                  : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700'
                }
              `}
            >
              {pageNumber}
            </button>
          );
        })}

        {/* Next button */}
        <button
          onClick={() => handlePageClick(currentPage + 1)}
          disabled={!hasNextPage}
          data-testid="next-page"
          className={`
            relative inline-flex items-center px-2 py-2 rounded-r-md border text-sm font-medium
            ${hasNextPage
              ? 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700'
              : 'border-gray-300 bg-gray-100 text-gray-300 cursor-not-allowed'
            }
          `}
        >
          <span className="sr-only">Next</span>
          <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
        </button>
      </nav>
    </div>
  );
}
