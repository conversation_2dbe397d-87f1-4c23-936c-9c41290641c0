'use client'

import * as React from 'react'
import * as SelectPrimitive from '@radix-ui/react-select'
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline'

interface SelectOption {
  label: string
  value: string
}

interface SelectProps {
  value: string
  onValueChange: (value: string) => void
  options: SelectOption[]
  placeholder?: string
  className?: string
  multiple?: boolean
  values?: string[]
  onValuesChange?: (values: string[]) => void
}

export function Select({
  value,
  onValueChange,
  options,
  placeholder = 'Select an option',
  className = '',
  multiple = false,
  values = [],
  onValuesChange,
}: SelectProps) {
  // For multi-select
  const handleMultiSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (!onValuesChange) return

    const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value)
    onValuesChange(selectedOptions)
  }

  if (multiple && onValuesChange) {
    const [isOpen, setIsOpen] = React.useState(false);
    const dropdownRef = React.useRef<HTMLDivElement>(null);

    // Handle click outside to close dropdown
    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Get display text for selected values
    const selectedLabels = values.map(value => {
      const option = options.find(opt => opt.value === value);
      return option ? option.label : value;
    });

    const displayText = selectedLabels.length > 0
      ? selectedLabels.join(', ')
      : placeholder;

    const toggleOption = (value: string) => {
      if (values.includes(value)) {
        onValuesChange(values.filter(v => v !== value));
      } else {
        onValuesChange([...values, value]);
      }
    };

    return (
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className={`w-full text-left inline-flex items-center justify-between rounded-md border border-input bg-background text-foreground px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
        >
          <span className="truncate">{displayText}</span>
          <ChevronDownIcon className="h-4 w-4 opacity-50" />
        </button>

        {isOpen && (
          <div className="absolute z-50 mt-1 w-full rounded-md border bg-popover p-1 text-popover-foreground shadow-md max-h-60 overflow-auto">
            {options.map((option) => (
              <div
                key={option.value}
                className="flex cursor-pointer items-center rounded-sm px-2 py-1.5 hover:bg-accent hover:text-accent-foreground"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleOption(option.value);
                }}
              >
                <div className="mr-2 flex h-4 w-4 items-center justify-center rounded-sm border">
                  {values.includes(option.value) && (
                    <CheckIcon className="h-3 w-3" />
                  )}
                </div>
                <span>{option.label}</span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Regular single-select
  return (
    <SelectPrimitive.Root value={value} onValueChange={onValueChange}>
      <SelectPrimitive.Trigger
        className={`inline-flex items-center justify-between rounded-md border border-input bg-background text-foreground px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
      >
        <SelectPrimitive.Value placeholder={placeholder} />
        <SelectPrimitive.Icon>
          <ChevronDownIcon className="h-4 w-4 opacity-50" />
        </SelectPrimitive.Icon>
      </SelectPrimitive.Trigger>
      <SelectPrimitive.Portal>
        <SelectPrimitive.Content
          className="relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-80"
        >
          <SelectPrimitive.Viewport className="p-1">
            {options.map((option) => (
              <SelectPrimitive.Item
                key={option.value}
                value={option.value}
                className="relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
              >
                <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                  <SelectPrimitive.ItemIndicator>
                    <CheckIcon className="h-4 w-4" />
                  </SelectPrimitive.ItemIndicator>
                </span>
                <SelectPrimitive.ItemText>{option.label}</SelectPrimitive.ItemText>
              </SelectPrimitive.Item>
            ))}
          </SelectPrimitive.Viewport>
        </SelectPrimitive.Content>
      </SelectPrimitive.Portal>
    </SelectPrimitive.Root>
  )
}