'use client'

import {
  <PERSON><PERSON>hart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts'

interface DataSeries {
  name: string
  dataKey: string
  color: string
}

interface LineChartProps {
  data: Array<{ [key: string]: any }>
  xField: string
  series: DataSeries[]
  title: string
}

export function LineChart({ data, xField, series, title }: LineChartProps) {
  // Function to format date strings like "2023-01-W0" into readable format
  const formatDateString = (dateStr: string) => {
    // Check if it's a weekly format (contains 'W')
    if (dateStr.includes('W')) {
      // Extract year and month
      const [yearMonth, weekPart] = dateStr.split('-W');
      const [year, month] = yearMonth.split('-');

      // Create a readable format
      return `Week ${weekPart} of ${month}/${year}`;
    }

    // Check if it's a monthly format (YYYY-MM)
    if (dateStr.match(/^\d{4}-\d{2}$/)) {
      const [year, month] = dateStr.split('-');
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return `${monthNames[parseInt(month) - 1]} ${year}`;
    }

    // For standard date format, try to parse it
    try {
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString();
      }
    } catch (e) {
      // If parsing fails, return the original string
    }

    // Default case: return the original string
    return dateStr;
  };

  return (
    <div className="w-full h-full">
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height="90%">
        <RechartsLineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted/20" />
          <XAxis
            dataKey={xField}
            tickFormatter={formatDateString}
            className="text-foreground"
          />
          <YAxis className="text-foreground" />
          <Tooltip
            labelFormatter={formatDateString}
            contentStyle={{
              backgroundColor: 'var(--background)',
              borderColor: 'var(--primary)',
              borderRadius: '8px',
            }}
          />
          {series.map((s, index) => (
            <Line
              key={s.dataKey}
              type="monotone"
              dataKey={s.dataKey}
              name={s.name}
              stroke={s.color}
              strokeWidth={2}
              dot={{ fill: s.color, strokeWidth: 2 }}
              activeDot={{ r: 8, fill: s.color }}
            />
          ))}
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  )
}