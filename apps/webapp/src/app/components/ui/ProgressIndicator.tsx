'use client';

import React from 'react';

export interface ProgressIndicatorProps {
  current: number;
  total: number;
  status?: string;
  showPercentage?: boolean;
  showNumbers?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  barClassName?: string;
  textClassName?: string;
}

const sizeConfig = {
  sm: {
    height: 'h-1.5',
    text: 'text-xs',
    spacing: 'space-y-1'
  },
  md: {
    height: 'h-2',
    text: 'text-sm',
    spacing: 'space-y-2'
  },
  lg: {
    height: 'h-3',
    text: 'text-base',
    spacing: 'space-y-2'
  }
};

export function ProgressIndicator({
  current,
  total,
  status,
  showPercentage = true,
  showNumbers = true,
  size = 'md',
  className = '',
  barClassName = '',
  textClassName = ''
}: ProgressIndicatorProps) {
  const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
  const isComplete = current >= total && total > 0;
  const sizeStyles = sizeConfig[size];

  // Determine progress bar color based on completion status
  const getProgressColor = () => {
    if (isComplete) return 'bg-green-500';
    if (percentage > 75) return 'bg-blue-500';
    if (percentage > 50) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const formatNumbers = (current: number, total: number) => {
    // Format large numbers with commas
    const formatNumber = (num: number) => {
      return num.toLocaleString();
    };
    
    return `${formatNumber(current)} of ${formatNumber(total)}`;
  };

  return (
    <div className={`${sizeStyles.spacing} ${className}`} data-testid="progress-indicator">
      {/* Progress Bar */}
      <div className="relative">
        <div className={`w-full ${sizeStyles.height} bg-gray-200 rounded-full overflow-hidden`}>
          <div
            className={`${sizeStyles.height} ${getProgressColor()} transition-all duration-300 ease-out rounded-full ${barClassName}`}
            style={{ width: `${Math.min(percentage, 100)}%` }}
            data-testid="progress-bar"
          />
        </div>
        
        {/* Percentage overlay for larger sizes */}
        {size === 'lg' && showPercentage && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-medium text-white drop-shadow-sm">
              {percentage}%
            </span>
          </div>
        )}
      </div>

      {/* Progress Information */}
      <div className={`flex items-center justify-between ${sizeStyles.text} ${textClassName}`}>
        <div className="flex items-center space-x-2">
          {showNumbers && (
            <span className="font-medium text-gray-900" data-testid="progress-numbers">
              {formatNumbers(current, total)}
            </span>
          )}
          
          {showPercentage && (
            <span className="text-gray-600" data-testid="progress-percentage">
              ({percentage}%)
            </span>
          )}
        </div>

        {status && (
          <span className="text-gray-500 italic" data-testid="progress-status">
            {status}
          </span>
        )}
      </div>

      {/* Additional status text for incomplete progress */}
      {!isComplete && total > 0 && (
        <div className={`${sizeStyles.text} text-gray-500`}>
          <span data-testid="remaining-count">
            {total - current} remaining
          </span>
        </div>
      )}
    </div>
  );
}

// Specialized progress indicators for common use cases
export function EmailAnalysisProgress({
  processed,
  total,
  currentStatus,
  ...props
}: {
  processed: number;
  total: number;
  currentStatus?: string;
} & Omit<ProgressIndicatorProps, 'current' | 'total' | 'status'>) {
  return (
    <ProgressIndicator
      current={processed}
      total={total}
      status={currentStatus}
      {...props}
    />
  );
}

export function CompactProgress({
  current,
  total,
  ...props
}: Omit<ProgressIndicatorProps, 'showNumbers' | 'size'>) {
  return (
    <ProgressIndicator
      current={current}
      total={total}
      size="sm"
      showNumbers={false}
      {...props}
    />
  );
}
