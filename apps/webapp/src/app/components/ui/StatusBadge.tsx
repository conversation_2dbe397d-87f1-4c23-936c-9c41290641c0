'use client';

import React from 'react';
import { 
  CheckCircleIcon, 
  ClockIcon, 
  ExclamationCircleIcon, 
  XCircleIcon 
} from '@heroicons/react/24/outline';

export type StatusType = 'processing' | 'completed' | 'error' | 'cancelled';

export interface StatusBadgeProps {
  status: StatusType;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  animated?: boolean;
  className?: string;
}

const statusConfig = {
  processing: {
    label: 'Processing',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
    icon: ClockIcon,
    pulseColor: 'bg-blue-400'
  },
  completed: {
    label: 'Completed',
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
    borderColor: 'border-green-200',
    icon: CheckCircleIcon,
    pulseColor: 'bg-green-400'
  },
  error: {
    label: 'Error',
    bgColor: 'bg-red-100',
    textColor: 'text-red-800',
    borderColor: 'border-red-200',
    icon: ExclamationCircleIcon,
    pulseColor: 'bg-red-400'
  },
  cancelled: {
    label: 'Cancelled',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-200',
    icon: XCircleIcon,
    pulseColor: 'bg-gray-400'
  }
};

const sizeConfig = {
  sm: {
    padding: 'px-2 py-1',
    text: 'text-xs',
    iconSize: 'w-3 h-3',
    gap: 'gap-1'
  },
  md: {
    padding: 'px-2.5 py-1.5',
    text: 'text-sm',
    iconSize: 'w-4 h-4',
    gap: 'gap-1.5'
  },
  lg: {
    padding: 'px-3 py-2',
    text: 'text-base',
    iconSize: 'w-5 h-5',
    gap: 'gap-2'
  }
};

export function StatusBadge({
  status,
  size = 'md',
  showIcon = true,
  animated = false,
  className = ''
}: StatusBadgeProps) {
  const config = statusConfig[status];
  const sizeStyles = sizeConfig[size];
  const IconComponent = config.icon;

  const shouldAnimate = animated && status === 'processing';

  return (
    <span
      data-testid="status-badge"
      className={`
        inline-flex items-center font-medium rounded-full border
        ${sizeStyles.padding} ${sizeStyles.text} ${sizeStyles.gap}
        ${config.bgColor} ${config.textColor} ${config.borderColor}
        ${className}
      `.trim()}
    >
      {showIcon && (
        <span className="relative flex-shrink-0">
          <IconComponent className={sizeStyles.iconSize} />
          {shouldAnimate && (
            <span 
              data-testid="animated-indicator"
              className={`
                absolute inset-0 rounded-full animate-ping
                ${config.pulseColor} opacity-75
              `}
            />
          )}
        </span>
      )}
      <span>{config.label}</span>
    </span>
  );
}

// Specialized status badges for common use cases
export function ProcessingBadge(props: Omit<StatusBadgeProps, 'status'>) {
  return <StatusBadge {...props} status="processing" animated={true} />;
}

export function CompletedBadge(props: Omit<StatusBadgeProps, 'status'>) {
  return <StatusBadge {...props} status="completed" />;
}

export function ErrorBadge(props: Omit<StatusBadgeProps, 'status'>) {
  return <StatusBadge {...props} status="error" />;
}

export function CancelledBadge(props: Omit<StatusBadgeProps, 'status'>) {
  return <StatusBadge {...props} status="cancelled" />;
}
