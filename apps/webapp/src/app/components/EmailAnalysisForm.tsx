'use client';

import { useState } from "react";

interface AnalysisResults {
  remainingTokens: number;
  success: number;
  failed: number;
  errors: Array<{
    emailId: string;
    error: string;
  }>;
  processedEmails: Array<{
    id: string;
    analysis: any;
  }>;
}

interface EmailAnalysisFormProps {
  onAnalyze: (startDate: string, endDate: string) => Promise<AnalysisResults>;
}

export default function EmailAnalysisForm({ onAnalyze }: EmailAnalysisFormProps) {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState("");

  const handleAnalyze = async () => {
    try {
      setIsAnalyzing(true);
      setError("");
      const results = await onAnalyze(startDate, endDate);
      setAnalysisResults(results);
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : "Failed to analyze emails");
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="flex flex-col gap-4 p-6 border rounded-lg">
      <h2 className="text-xl font-bold">Email Analysis</h2>
      
      <div className="flex flex-col gap-2">
        <label className="text-sm">Start Date</label>
        <input
          type="date"
          value={startDate}
          onChange={(e) => setStartDate(e.target.value)}
          className="border rounded px-3 py-2"
        />
      </div>

      <div className="flex flex-col gap-2">
        <label className="text-sm">End Date</label>
        <input
          type="date"
          value={endDate}
          onChange={(e) => setEndDate(e.target.value)}
          className="border rounded px-3 py-2"
        />
      </div>

      <button
        onClick={handleAnalyze}
        disabled={isAnalyzing || !startDate || !endDate}
        className="bg-blue-500 text-white px-4 py-2 rounded disabled:bg-gray-300"
      >
        {isAnalyzing ? "Analyzing..." : "Analyze Emails"}
      </button>

      {error && (
        <div className="text-red-500 text-sm">{error}</div>
      )}

      {analysisResults && (
        <div className="mt-4">
          <h3 className="font-bold mb-2">Results:</h3>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(analysisResults, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
} 