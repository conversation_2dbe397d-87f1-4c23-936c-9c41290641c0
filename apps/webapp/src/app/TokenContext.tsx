'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useAuth } from '@clerk/nextjs'

interface TokenContextType {
  remainingTokens: number
  totalTokens: number
  updateTokens: (tokens: number) => void
  isLoading: boolean
  refreshTokens: () => Promise<void>
}

const defaultContext: TokenContextType = {
  remainingTokens: 0,
  totalTokens: 5000, // Default total tokens
  updateTokens: () => {},
  isLoading: true,
  refreshTokens: async () => {}
}

const TokenContext = createContext<TokenContextType>(defaultContext)

export function useTokens() {
  return useContext(TokenContext)
}

interface TokenProviderProps {
  children: ReactNode
}

export function TokenProvider({ children }: TokenProviderProps) {
  const [remainingTokens, setRemainingTokens] = useState<number>(0)
  const [totalTokens, setTotalTokens] = useState<number>(5000) // Default total
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const { userId, isLoaded, isSignedIn, getToken } = useAuth()

  // Update tokens when user changes value
  const updateTokens = (tokens: number) => {
    setRemainingTokens(tokens)
  }

  // Fetch tokens from the API
  const fetchTokens = async () => {
    if (!userId || !isSignedIn) {
      setRemainingTokens(0)
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)

      // Get authentication token
      const token = await getToken()
      if (!token) {
        throw new Error('Authentication required')
      }

      const response = await fetch('/api/tokens', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch tokens')
      }

      const data = await response.json()

      if (data && typeof data.remaining === 'number') {
        setRemainingTokens(data.remaining)
      }

      if (data && typeof data.total === 'number') {
        setTotalTokens(data.total)
      }
    } catch (error) {
      console.error('Error fetching tokens:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Initial fetch after auth is loaded
  useEffect(() => {
    if (isLoaded && isSignedIn) {
      fetchTokens()
    }
  }, [isLoaded, isSignedIn, userId])

  // Function to manually refresh tokens
  const refreshTokens = async () => {
    await fetchTokens()
  }

  return (
    <TokenContext.Provider
      value={{
        remainingTokens,
        totalTokens,
        updateTokens,
        isLoading,
        refreshTokens
      }}
    >
      {children}
    </TokenContext.Provider>
  )
} 