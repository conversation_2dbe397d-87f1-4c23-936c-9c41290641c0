'use client'

import { ThemeProvider } from 'next-themes'
import { TokenProvider } from './TokenContext'
import { DateRangeProvider } from './DateRangeContext'

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <TokenProvider>
        <DateRangeProvider>
          {children}
        </DateRangeProvider>
      </TokenProvider>
    </ThemeProvider>
  )
}