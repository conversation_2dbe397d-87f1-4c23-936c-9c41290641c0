/**
 * Test utility for metrics calculation
 * This file contains functions to test the metrics service with known test data
 */

/**
 * Create a test document with known values and verify metrics calculation
 * @param {Object} db - Firestore database instance
 * @param {Object} logger - Logger instance
 * @param {Object} metricsService - Metrics service instance
 * @returns {Promise<Object>} - Test results
 */
async function testMetricsCalculation(db, logger, metricsService) {
    try {
        logger.info('Starting metrics calculation test');
        
        // Create a test document with known values
        const testDocId = `test_user_${Date.now()}`;
        const testDate = new Date('2023-01-02T12:00:00Z');
        
        await db.collection('emailAnalysis').doc(testDocId).set({
            parsed: {
                email_type_category: 'thanks_for_applying_or_application_received_confirmation',
                company_name: 'Test Company',
                role_name: 'Test Role',
                is_related_to_job_search: true
            },
            date: testDate.toISOString(),
            version: '1.0.0',
            lastUpdated: new Date().toISOString()
        });
        
        logger.info('Created test document', { 
            testDocId, 
            testDate: testDate.toISOString(),
            category: 'thanks_for_applying_or_application_received_confirmation'
        });
        
        // Query with exact date range
        const startDate = new Date('2023-01-02T00:00:00Z');
        const endDate = new Date('2023-01-02T23:59:59Z');
        
        // First, verify the document exists and can be retrieved
        const snapshot = await db.collection('emailAnalysis')
            .get();
        
        logger.info('All documents in collection', { 
            documentsFound: snapshot.size,
            documents: snapshot.docs.map(d => ({
                id: d.id,
                date: d.data().date,
                category: d.data().parsed?.email_type_category
            }))
        });
        
        // Now test the metrics service
        const metrics = await metricsService.getMetrics({
            userId: 'test_user',
            startDate,
            endDate,
            period: 'daily'
        });
        
        logger.info('Test metrics result', { 
            metrics,
            hasData: metrics.labels.length > 0,
            applications: metrics.applications,
            interviews: metrics.interviews
        });
        
        // Clean up
        await db.collection('emailAnalysis').doc(testDocId).delete();
        logger.info('Test document deleted', { testDocId });
        
        return {
            success: true,
            metrics
        };
    } catch (error) {
        logger.error('Error in metrics test', { error });
        return {
            success: false,
            error: error.message
        };
    }
}

module.exports = { testMetricsCalculation };
