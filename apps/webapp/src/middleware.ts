import { clerkMiddleware } from '@clerk/nextjs/server'

// This example protects all routes including api/trpc routes
// Please edit this to allow other routes to be public as needed.
// See https://clerk.com/docs/references/nextjs/auth-middleware for more information about configuring your middleware
export default clerkMiddleware()

export const config = {
  matcher: [
    // Skip Next.js internals and static files
    '/((?!_next/|.*\\.(?:jpg|jpeg|gif|png|svg|ico|webp|js|css)).*)',
    // Force all API routes to go through middleware
    '/api/:path*',
  ],
} 