{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@webapp/shared": ["../../libs/shared/src/index.ts"], "@webapp/services": ["../../libs/services/src/index.ts"], "@webapp/email-core": ["../../libs/email-core/src/index.ts"], "@webapp/gcp-utils": ["../../libs/gcp-utils/src/index.ts"]}, "baseUrl": "."}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../../apps/webapp/.next/types/**/*.ts", "../../dist/apps/webapp/.next/types/**/*.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "jest.config.ts", "**/*.spec.ts", "**/*.test.ts"]}