const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// Start the server without Socket.io for production
async function startServer() {
  const dev = process.env.NODE_ENV !== 'production';
  const hostname = '0.0.0.0'; // Listen on all interfaces for Cloud Run
  const port = parseInt(process.env.PORT) || 8080;

  console.log('Starting production server...');
  console.log(`Environment: ${process.env.NODE_ENV}`);
  console.log(`Port: ${port}`);
  console.log(`Hostname: ${hostname}`);

  // Initialize Next.js
  const app = next({ dev, hostname, port });
  const handle = app.getRequestHandler();

  try {
    await app.prepare();
    console.log('Next.js app prepared successfully');

    // Create HTTP server without Socket.io for simplicity
    const server = createServer((req, res) => {
      const parsedUrl = parse(req.url, true);
      handle(req, res, parsedUrl);
    });

    // Start listening
    server.listen(port, hostname, (err) => {
      if (err) throw err;
      console.log(`> Ready on http://${hostname}:${port}`);
      console.log('> Production server started successfully');
      console.log('> Note: Socket.io real-time features are disabled in this production build');
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('Error starting server:', error);
    process.exit(1);
  }
}

startServer();
