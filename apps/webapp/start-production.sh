#!/bin/sh

echo "🔐 Retrieving secrets from Google Cloud Secret Manager..."

# Configuration
SECRETS_PROJECT_ID="data-driven-job-search"

echo "Secrets will be retrieved from project: \$SECRETS_PROJECT_ID"

# Function to retrieve secret
get_secret() {
    local secret_name=\$1
    gcloud secrets versions access latest --secret="\$secret_name" --project=\$SECRETS_PROJECT_ID 2>/dev/null
}

# Retrieve secrets and export as environment variables
export NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=\$(get_secret "CLERK_PUBLISHABLE_KEY")
export CLERK_SECRET_KEY=\$(get_secret "CLERK_SECRET_KEY")
export OPENAI_API_KEY=\$(get_secret "OPENAI_API_KEY")

# Verify secrets were retrieved
if [ -z "\$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY" ] || [ -z "\$CLERK_SECRET_KEY" ] || [ -z "\$OPENAI_API_KEY" ]; then
    echo "❌ Failed to retrieve one or more secrets from Secret Manager"
    exit 1
fi

echo "✅ All secrets retrieved successfully"

# Set other environment variables
export NODE_ENV=production
export BASE_URL=https://dev.datadrivenjobsearch.com
export NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
export NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
export NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
export NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
export WEBAPP_PROGRESS_WEBHOOK_URL=https://dev.datadrivenjobsearch.com/api/emails/progress-webhook
export GMAIL_PUBSUB_TOPIC=projects/ddjs-dev-458016/topics/gmail-notifications
export GMAIL_PUBSUB_SUBSCRIPTION=gmail-notifications-sub
export FUNCTIONS_REGION=us-central1

echo "🚀 Starting application..."
exec node server.js
