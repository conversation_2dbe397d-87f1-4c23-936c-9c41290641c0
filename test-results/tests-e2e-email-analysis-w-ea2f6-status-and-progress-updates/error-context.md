# Test info

- Name: Email Analysis Workflow >> should show connection status and progress updates
- Location: /Users/<USER>/GitRepos/webapp/tests/e2e/email-analysis-workflow.spec.ts:55:7

# Error details

```
Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/chromium_headless_shell-1169/chrome-mac/headless_shell
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | /**
   4 |  * End-to-End Email Analysis Workflow Tests
   5 |  * 
   6 |  * This test suite covers the complete email analysis workflow:
   7 |  * 1. Date selection and validation
   8 |  * 2. Analysis trigger and progress tracking
   9 |  * 3. Real-time updates via Socket.io
   10 |  * 4. Results display and metrics
   11 |  * 5. Error handling and recovery
   12 |  */
   13 |
   14 | const WEBAPP_URL = process.env.WEBAPP_URL || 'https://webapp-4r5k3ebaga-uc.a.run.app';
   15 |
   16 | test.describe('Email Analysis Workflow', () => {
   17 |   test.beforeEach(async ({ page }) => {
   18 |     // Navigate to the webapp
   19 |     await page.goto(WEBAPP_URL);
   20 |     
   21 |     // Wait for authentication to complete
   22 |     await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
   23 |   });
   24 |
   25 |   test('should load dashboard with all components', async ({ page }) => {
   26 |     // Verify dashboard elements are present
   27 |     await expect(page.locator('h1')).toContainText('Dashboard');
   28 |     await expect(page.locator('h2')).toContainText('Analyze New Emails');
   29 |     await expect(page.locator('h2')).toContainText('Metrics Overview');
   30 |     
   31 |     // Verify date inputs are present
   32 |     await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
   33 |     await expect(page.locator('input[placeholder*="End"]')).toBeVisible();
   34 |     
   35 |     // Verify analyze button is present (may be disabled initially)
   36 |     await expect(page.locator('button')).toContainText(/Analyze|Connecting/);
   37 |   });
   38 |
   39 |   test('should handle date selection correctly', async ({ page }) => {
   40 |     const startDateInput = page.locator('input[placeholder*="Start"]').first();
   41 |     const endDateInput = page.locator('input[placeholder*="End"]').first();
   42 |     
   43 |     // Set test dates (May 12-13, 2025)
   44 |     await startDateInput.fill('2025-05-12');
   45 |     await endDateInput.fill('2025-05-13');
   46 |     
   47 |     // Verify date range is updated
   48 |     await expect(page.locator('text=/Date range:/')).toBeVisible();
   49 |     
   50 |     // Verify button state changes based on date selection
   51 |     const analyzeButton = page.locator('button').filter({ hasText: /Analyze|Connecting/ });
   52 |     await expect(analyzeButton).toBeVisible();
   53 |   });
   54 |
>  55 |   test('should show connection status and progress updates', async ({ page }) => {
      |       ^ Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/chromium_headless_shell-1169/chrome-mac/headless_shell
   56 |     // Check for connection status indicators
   57 |     await expect(page.locator('text=/Status:|Connecting|Ready/')).toBeVisible();
   58 |     
   59 |     // Check for progress indicators
   60 |     await expect(page.locator('text=/Progress:|Waiting/')).toBeVisible();
   61 |     
   62 |     // Check for internal state display
   63 |     await expect(page.locator('text=/Internal state:/')).toBeVisible();
   64 |   });
   65 |
   66 |   test('should handle Socket.io connection gracefully', async ({ page }) => {
   67 |     // Monitor console for Socket.io connection attempts
   68 |     const consoleMessages: string[] = [];
   69 |     page.on('console', msg => {
   70 |       consoleMessages.push(msg.text());
   71 |     });
   72 |     
   73 |     // Wait for Socket.io connection attempts
   74 |     await page.waitForTimeout(3000);
   75 |     
   76 |     // Verify that Socket.io errors are handled gracefully
   77 |     const hasSocketErrors = consoleMessages.some(msg => 
   78 |       msg.includes('socket.io') || msg.includes('xhr poll error')
   79 |     );
   80 |     
   81 |     if (hasSocketErrors) {
   82 |       // Verify the UI still functions without Socket.io
   83 |       await expect(page.locator('text=/Connecting|Waiting/')).toBeVisible();
   84 |       console.log('Socket.io connection failed gracefully - UI remains functional');
   85 |     }
   86 |   });
   87 |
   88 |   test('should trigger email analysis workflow', async ({ page }) => {
   89 |     const startDateInput = page.locator('input[placeholder*="Start"]').first();
   90 |     const endDateInput = page.locator('input[placeholder*="End"]').first();
   91 |     
   92 |     // Set test dates
   93 |     await startDateInput.fill('2025-05-12');
   94 |     await endDateInput.fill('2025-05-13');
   95 |     
   96 |     // Wait for any connection attempts to complete
   97 |     await page.waitForTimeout(2000);
   98 |     
   99 |     // Try to click the analyze button if it's enabled
  100 |     const analyzeButton = page.locator('button').filter({ hasText: /Analyze/ });
  101 |     
  102 |     if (await analyzeButton.isEnabled()) {
  103 |       await analyzeButton.click();
  104 |       
  105 |       // Verify analysis starts
  106 |       await expect(page.locator('text=/Analyzing|Processing|In Progress/')).toBeVisible({ timeout: 5000 });
  107 |       
  108 |       // Monitor for progress updates
  109 |       await page.waitForTimeout(5000);
  110 |       
  111 |       // Check if progress is being tracked
  112 |       const progressText = await page.locator('text=/Progress:.*\/.*-.*Status:/')
  113 |       await expect(progressText).toBeVisible();
  114 |     } else {
  115 |       console.log('Analyze button is disabled - likely due to connection issues');
  116 |       
  117 |       // Verify the button shows appropriate disabled state
  118 |       await expect(analyzeButton).toBeDisabled();
  119 |       await expect(page.locator('text=/Connecting|Waiting/')).toBeVisible();
  120 |     }
  121 |   });
  122 |
  123 |   test('should display metrics overview', async ({ page }) => {
  124 |     // Verify metrics section is present
  125 |     await expect(page.locator('h2')).toContainText('Metrics Overview');
  126 |     
  127 |     // Check for date range display
  128 |     await expect(page.locator('text=/Date range:/')).toBeVisible();
  129 |     
  130 |     // Check for metric type selector
  131 |     await expect(page.locator('button')).toContainText('Applications');
  132 |     
  133 |     // Check for period selector
  134 |     await expect(page.locator('text=/Weekly|Monthly|Daily/')).toBeVisible();
  135 |   });
  136 |
  137 |   test('should handle navigation between pages', async ({ page }) => {
  138 |     // Test Analytics navigation
  139 |     await page.click('a[href="/analytics"]');
  140 |     
  141 |     // Check if Analytics page loads or shows 404
  142 |     const pageContent = await page.textContent('body');
  143 |     if (pageContent?.includes('404')) {
  144 |       console.log('Analytics page not yet deployed - showing 404 as expected');
  145 |       await expect(page.locator('text=/404.*Page Not Found/')).toBeVisible();
  146 |     } else {
  147 |       await expect(page.locator('h1')).toContainText('Analytics');
  148 |     }
  149 |     
  150 |     // Test Settings navigation
  151 |     await page.click('a[href="/settings"]');
  152 |     
  153 |     const settingsContent = await page.textContent('body');
  154 |     if (settingsContent?.includes('404')) {
  155 |       console.log('Settings page not yet deployed - showing 404 as expected');
```