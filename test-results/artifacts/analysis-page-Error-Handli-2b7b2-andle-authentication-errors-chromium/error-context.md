# Test info

- Name: <PERSON>rror Handling >> should handle authentication errors
- Location: /Users/<USER>/GitRepos/webapp/tests/e2e/analysis-page.spec.ts:296:7

# Error details

```
Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/analysis
Call log:
  - navigating to "http://localhost:3000/analysis", waiting until "load"

    at /Users/<USER>/GitRepos/webapp/tests/e2e/analysis-page.spec.ts:306:16
```

# Test source

```ts
  206 |     await page.waitForLoadState('networkidle');
  207 |
  208 |     // Check for analysis link on dashboard
  209 |     const analysisLink = page.locator('a[href="/analysis"]');
  210 |     if (await analysisLink.first().isVisible()) {
  211 |       await analysisLink.first().click();
  212 |       
  213 |       // Should navigate to analysis page
  214 |       await expect(page).toHaveURL('/analysis');
  215 |       await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
  216 |     }
  217 |   });
  218 |
  219 |   test('should display summary statistics when data is present', async ({ page }) => {
  220 |     await page.goto('/analysis');
  221 |     await page.waitForLoadState('networkidle');
  222 |
  223 |     // Wait for page to fully load
  224 |     await page.waitForTimeout(2000);
  225 |
  226 |     // Check if summary section is present (only shows when there are analysis runs)
  227 |     const summarySection = page.locator('h3', { hasText: 'Summary' });
  228 |     if (await summarySection.isVisible()) {
  229 |       await expect(summarySection).toBeVisible();
  230 |       
  231 |       // Check for summary stats
  232 |       await expect(page.locator('text=Completed')).toBeVisible();
  233 |       await expect(page.locator('text=Processing')).toBeVisible();
  234 |       await expect(page.locator('text=Failed')).toBeVisible();
  235 |       await expect(page.locator('text=Total Emails')).toBeVisible();
  236 |     }
  237 |   });
  238 | });
  239 |
  240 | test.describe('Mobile Responsiveness', () => {
  241 |   test.use({ viewport: { width: 375, height: 667 } }); // iPhone SE size
  242 |
  243 |   test('should display mobile-optimized layout', async ({ page }) => {
  244 |     await page.goto('/analysis');
  245 |     await page.waitForLoadState('networkidle');
  246 |
  247 |     // Check that page loads and is responsive
  248 |     await expect(page.locator('h1', { hasText: 'Email Analysis Status' })).toBeVisible();
  249 |     
  250 |     // Check that table is present (it should adapt to mobile)
  251 |     await page.waitForSelector('[data-testid="analysis-runs-table"]', { timeout: 10000 });
  252 |     
  253 |     // Check that filters are accessible on mobile
  254 |     await expect(page.locator('text=Filters')).toBeVisible();
  255 |   });
  256 |
  257 |   test('should handle mobile navigation', async ({ page }) => {
  258 |     await page.goto('/');
  259 |     await page.waitForLoadState('networkidle');
  260 |
  261 |     // Check if mobile menu exists and navigate to analysis
  262 |     const mobileMenuButton = page.locator('button[aria-label="Open menu"]');
  263 |     if (await mobileMenuButton.isVisible()) {
  264 |       await mobileMenuButton.click();
  265 |     }
  266 |     
  267 |     // Navigate to analysis page
  268 |     const analysisLink = page.locator('a[href="/analysis"]');
  269 |     if (await analysisLink.isVisible()) {
  270 |       await analysisLink.click();
  271 |       await expect(page).toHaveURL('/analysis');
  272 |     }
  273 |   });
  274 | });
  275 |
  276 | test.describe('Error Handling', () => {
  277 |   test('should handle network errors gracefully', async ({ page }) => {
  278 |     // Intercept API calls and simulate network error
  279 |     await page.route('/api/analysis-runs*', route => {
  280 |       route.abort('failed');
  281 |     });
  282 |
  283 |     await page.goto('/analysis');
  284 |     await page.waitForLoadState('networkidle');
  285 |
  286 |     // Wait for error to appear
  287 |     await page.waitForTimeout(3000);
  288 |
  289 |     // Check for error message
  290 |     const errorMessage = page.locator('text=Error:');
  291 |     if (await errorMessage.isVisible()) {
  292 |       await expect(errorMessage).toBeVisible();
  293 |     }
  294 |   });
  295 |
  296 |   test('should handle authentication errors', async ({ page }) => {
  297 |     // Intercept API calls and simulate auth error
  298 |     await page.route('/api/analysis-runs*', route => {
  299 |       route.fulfill({
  300 |         status: 401,
  301 |         contentType: 'application/json',
  302 |         body: JSON.stringify({ error: 'Authentication required' })
  303 |       });
  304 |     });
  305 |
> 306 |     await page.goto('/analysis');
      |                ^ Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/analysis
  307 |     await page.waitForLoadState('networkidle');
  308 |
  309 |     // Wait for error handling
  310 |     await page.waitForTimeout(3000);
  311 |
  312 |     // Should handle auth error appropriately
  313 |     // This might redirect to sign-in or show an error message
  314 |   });
  315 | });
  316 |
```