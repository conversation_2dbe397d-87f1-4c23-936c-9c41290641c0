# Test info

- Name: Email Analysis Status Page >> should display analysis page with correct title and navigation
- Location: /Users/<USER>/GitRepos/webapp/tests/e2e/analysis-page.spec.ts:19:7

# Error details

```
Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
Call log:
  - navigating to "http://localhost:3000/", waiting until "load"

    at /Users/<USER>/GitRepos/webapp/tests/e2e/analysis-page.spec.ts:6:16
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Email Analysis Status Page', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Navigate to the application
>  6 |     await page.goto('/');
     |                ^ Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
   7 |     
   8 |     // Wait for the page to load
   9 |     await page.waitForLoadState('networkidle');
   10 |     
   11 |     // Check if we need to sign in (this will depend on your auth setup)
   12 |     const signInButton = page.locator('text=Sign in');
   13 |     if (await signInButton.isVisible()) {
   14 |       // Handle sign in if needed - this might need to be customized based on your auth flow
   15 |       console.log('Sign in required - this test assumes user is already authenticated');
   16 |     }
   17 |   });
   18 |
   19 |   test('should display analysis page with correct title and navigation', async ({ page }) => {
   20 |     // Navigate to analysis page
   21 |     await page.goto('/analysis');
   22 |     await page.waitForLoadState('networkidle');
   23 |
   24 |     // Check page title and header
   25 |     await expect(page.locator('h1')).toContainText('Email Analysis Status');
   26 |     
   27 |     // Check navigation includes Analysis link
   28 |     const analysisNavLink = page.locator('nav a[href="/analysis"]');
   29 |     await expect(analysisNavLink).toBeVisible();
   30 |     
   31 |     // Check page description
   32 |     await expect(page.locator('text=Monitor and manage your email analysis runs')).toBeVisible();
   33 |   });
   34 |
   35 |   test('should display analysis runs table with correct headers', async ({ page }) => {
   36 |     await page.goto('/analysis');
   37 |     await page.waitForLoadState('networkidle');
   38 |
   39 |     // Wait for table to load
   40 |     await page.waitForSelector('[data-testid="analysis-runs-table"]', { timeout: 10000 });
   41 |
   42 |     // Check table headers are present
   43 |     const expectedHeaders = [
   44 |       'Source Type',
   45 |       'Total Emails', 
   46 |       'Cached',
   47 |       'Pending',
   48 |       'Analyzed',
   49 |       'Status',
   50 |       'Email Date Range',
   51 |       'Started'
   52 |     ];
   53 |
   54 |     for (const header of expectedHeaders) {
   55 |       await expect(page.locator('th', { hasText: header })).toBeVisible();
   56 |     }
   57 |   });
   58 |
   59 |   test('should display start analysis section', async ({ page }) => {
   60 |     await page.goto('/analysis');
   61 |     await page.waitForLoadState('networkidle');
   62 |
   63 |     // Check start analysis section is present
   64 |     await expect(page.locator('h2', { hasText: 'Start New Email Analysis' })).toBeVisible();
   65 |     
   66 |     // Check date inputs are present
   67 |     await expect(page.locator('[data-testid="start-date"]')).toBeVisible();
   68 |     await expect(page.locator('[data-testid="end-date"]')).toBeVisible();
   69 |     
   70 |     // Check analyze button is present
   71 |     await expect(page.locator('[data-testid="analyze-button"]')).toBeVisible();
   72 |   });
   73 |
   74 |   test('should handle date selection and enable analyze button', async ({ page }) => {
   75 |     await page.goto('/analysis');
   76 |     await page.waitForLoadState('networkidle');
   77 |
   78 |     // Initially analyze button should be disabled
   79 |     const analyzeButton = page.locator('[data-testid="analyze-button"]');
   80 |     await expect(analyzeButton).toBeDisabled();
   81 |
   82 |     // Fill in dates
   83 |     const startDate = '2024-01-01';
   84 |     const endDate = '2024-01-03';
   85 |     
   86 |     await page.fill('[data-testid="start-date"]', startDate);
   87 |     await page.fill('[data-testid="end-date"]', endDate);
   88 |
   89 |     // Wait a moment for any validation/estimation to complete
   90 |     await page.waitForTimeout(1000);
   91 |
   92 |     // Button should now be enabled (assuming valid dates and sufficient tokens)
   93 |     // Note: This might be disabled if there are no tokens or other validation issues
   94 |     const buttonState = await analyzeButton.isDisabled();
   95 |     console.log('Analyze button disabled state:', buttonState);
   96 |   });
   97 |
   98 |   test('should display and interact with filters', async ({ page }) => {
   99 |     await page.goto('/analysis');
  100 |     await page.waitForLoadState('networkidle');
  101 |
  102 |     // Check filter section is present
  103 |     await expect(page.locator('text=Filters')).toBeVisible();
  104 |
  105 |     // Check filter dropdowns
  106 |     await expect(page.locator('[data-testid="source-type-filter"]')).toBeVisible();
```