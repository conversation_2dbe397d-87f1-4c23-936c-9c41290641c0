# Test info

- Name: Error Handling and Recovery >> should show appropriate error messages
- Location: /Users/<USER>/GitRepos/webapp/tests/e2e/email-analysis-workflow.spec.ts:201:7

# Error details

```
Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/chromium_headless_shell-1169/chrome-mac/headless_shell
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
  101 |     
  102 |     if (await analyzeButton.isEnabled()) {
  103 |       await analyzeButton.click();
  104 |       
  105 |       // Verify analysis starts
  106 |       await expect(page.locator('text=/Analyzing|Processing|In Progress/')).toBeVisible({ timeout: 5000 });
  107 |       
  108 |       // Monitor for progress updates
  109 |       await page.waitForTimeout(5000);
  110 |       
  111 |       // Check if progress is being tracked
  112 |       const progressText = await page.locator('text=/Progress:.*\/.*-.*Status:/')
  113 |       await expect(progressText).toBeVisible();
  114 |     } else {
  115 |       console.log('Analyze button is disabled - likely due to connection issues');
  116 |       
  117 |       // Verify the button shows appropriate disabled state
  118 |       await expect(analyzeButton).toBeDisabled();
  119 |       await expect(page.locator('text=/Connecting|Waiting/')).toBeVisible();
  120 |     }
  121 |   });
  122 |
  123 |   test('should display metrics overview', async ({ page }) => {
  124 |     // Verify metrics section is present
  125 |     await expect(page.locator('h2')).toContainText('Metrics Overview');
  126 |     
  127 |     // Check for date range display
  128 |     await expect(page.locator('text=/Date range:/')).toBeVisible();
  129 |     
  130 |     // Check for metric type selector
  131 |     await expect(page.locator('button')).toContainText('Applications');
  132 |     
  133 |     // Check for period selector
  134 |     await expect(page.locator('text=/Weekly|Monthly|Daily/')).toBeVisible();
  135 |   });
  136 |
  137 |   test('should handle navigation between pages', async ({ page }) => {
  138 |     // Test Analytics navigation
  139 |     await page.click('a[href="/analytics"]');
  140 |     
  141 |     // Check if Analytics page loads or shows 404
  142 |     const pageContent = await page.textContent('body');
  143 |     if (pageContent?.includes('404')) {
  144 |       console.log('Analytics page not yet deployed - showing 404 as expected');
  145 |       await expect(page.locator('text=/404.*Page Not Found/')).toBeVisible();
  146 |     } else {
  147 |       await expect(page.locator('h1')).toContainText('Analytics');
  148 |     }
  149 |     
  150 |     // Test Settings navigation
  151 |     await page.click('a[href="/settings"]');
  152 |     
  153 |     const settingsContent = await page.textContent('body');
  154 |     if (settingsContent?.includes('404')) {
  155 |       console.log('Settings page not yet deployed - showing 404 as expected');
  156 |       await expect(page.locator('text=/404.*Page Not Found/')).toBeVisible();
  157 |     } else {
  158 |       await expect(page.locator('h1')).toContainText('Settings');
  159 |     }
  160 |     
  161 |     // Return to Dashboard
  162 |     await page.click('a[href="/"]');
  163 |     await expect(page.locator('h1')).toContainText('Dashboard');
  164 |   });
  165 |
  166 |   test('should handle authentication correctly', async ({ page }) => {
  167 |     // Verify user is authenticated
  168 |     await expect(page.locator('img[alt*="logo"]')).toBeVisible();
  169 |     
  170 |     // Check for user menu or profile indicator
  171 |     const userButton = page.locator('button').filter({ hasText: /user|profile|menu/i });
  172 |     if (await userButton.count() > 0) {
  173 |       await expect(userButton.first()).toBeVisible();
  174 |     }
  175 |   });
  176 | });
  177 |
  178 | test.describe('Error Handling and Recovery', () => {
  179 |   test('should handle API failures gracefully', async ({ page }) => {
  180 |     // Monitor network requests
  181 |     const failedRequests: string[] = [];
  182 |     page.on('response', response => {
  183 |       if (response.status() >= 400) {
  184 |         failedRequests.push(`${response.status()} ${response.url()}`);
  185 |       }
  186 |     });
  187 |     
  188 |     await page.goto(WEBAPP_URL);
  189 |     await page.waitForTimeout(3000);
  190 |     
  191 |     // Log any API failures
  192 |     if (failedRequests.length > 0) {
  193 |       console.log('API failures detected:', failedRequests);
  194 |       
  195 |       // Verify the UI still functions despite API failures
  196 |       await expect(page.locator('h1')).toContainText('Dashboard');
  197 |       await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
  198 |     }
  199 |   });
  200 |
> 201 |   test('should show appropriate error messages', async ({ page }) => {
      |       ^ Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/chromium_headless_shell-1169/chrome-mac/headless_shell
  202 |     await page.goto(WEBAPP_URL);
  203 |     
  204 |     // Check for error indicators in the UI
  205 |     const errorElements = page.locator('text=/Error|Failed|Internal Server Error/');
  206 |     const errorCount = await errorElements.count();
  207 |     
  208 |     if (errorCount > 0) {
  209 |       console.log(`Found ${errorCount} error messages in UI`);
  210 |       
  211 |       // Verify errors are displayed appropriately
  212 |       for (let i = 0; i < errorCount; i++) {
  213 |         const errorText = await errorElements.nth(i).textContent();
  214 |         console.log(`Error ${i + 1}: ${errorText}`);
  215 |       }
  216 |     }
  217 |   });
  218 | });
  219 |
  220 | test.describe('Performance and Responsiveness', () => {
  221 |   test('should load within acceptable time limits', async ({ page }) => {
  222 |     const startTime = Date.now();
  223 |     
  224 |     await page.goto(WEBAPP_URL);
  225 |     await page.waitForSelector('h1', { timeout: 10000 });
  226 |     
  227 |     const loadTime = Date.now() - startTime;
  228 |     console.log(`Page load time: ${loadTime}ms`);
  229 |     
  230 |     // Verify page loads within 10 seconds
  231 |     expect(loadTime).toBeLessThan(10000);
  232 |   });
  233 |
  234 |   test('should be responsive on different screen sizes', async ({ page }) => {
  235 |     // Test mobile viewport
  236 |     await page.setViewportSize({ width: 375, height: 667 });
  237 |     await page.goto(WEBAPP_URL);
  238 |     
  239 |     await expect(page.locator('h1')).toBeVisible();
  240 |     await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
  241 |     
  242 |     // Test tablet viewport
  243 |     await page.setViewportSize({ width: 768, height: 1024 });
  244 |     await page.reload();
  245 |     
  246 |     await expect(page.locator('h1')).toBeVisible();
  247 |     await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
  248 |     
  249 |     // Test desktop viewport
  250 |     await page.setViewportSize({ width: 1920, height: 1080 });
  251 |     await page.reload();
  252 |     
  253 |     await expect(page.locator('h1')).toBeVisible();
  254 |     await expect(page.locator('input[placeholder*="Start"]')).toBeVisible();
  255 |   });
  256 | });
  257 |
```