rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Clerk user ID match instead of session-based
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Remove old session collection rules
    // match /sessions/{session} { ... }

    match /userEmails/{email} {
      allow read, write: if request.auth.uid == resource.data.userId;
    }

    match /emailAnalysis/{docId} {
      allow read, write: if 
        request.auth.uid == docId.split('_')[0] // Clerk user ID match
        && resource.data.monitoredEmail == request.auth.token.email; // Email ownership
    }
  }
} 