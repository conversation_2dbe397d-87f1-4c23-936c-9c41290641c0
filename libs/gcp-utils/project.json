{"name": "gcp-utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/gcp-utils/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/gcp-utils", "main": "libs/gcp-utils/src/index.ts", "tsConfig": "libs/gcp-utils/tsconfig.lib.json", "assets": ["libs/gcp-utils/*.md"]}}}}