import { SecretManagerServiceClient } from '@google-cloud/secret-manager';

/**
 * Service for accessing secrets from Google Cloud Secret Manager
 */
export class SecretManagerService {
  private client: SecretManagerServiceClient;
  private projectId: string;
  private secretsProjectId: string;
  private secretCache: Map<string, string> = new Map();

  constructor() {
    this.client = new SecretManagerServiceClient();
    this.projectId = process.env['GOOGLE_CLOUD_PROJECT'] || process.env['GCP_PROJECT'] || 'ddjs-dev-458016';

    // Secrets are stored in the production project
    this.secretsProjectId = process.env['SECRETS_PROJECT_ID'] || 'data-driven-job-search';
  }

  /**
   * Get a secret value from Secret Manager
   * @param secretName - Name of the secret
   * @param useCache - Whether to use cached value (default: true)
   * @returns Promise<string> - The secret value
   */
  async getSecret(secretName: string, useCache: boolean = true): Promise<string> {
    // Check cache first if enabled
    if (useCache && this.secretCache.has(secretName)) {
      return this.secretCache.get(secretName)!;
    }

    try {
      const name = `projects/${this.secretsProjectId}/secrets/${secretName}/versions/latest`;
      const [version] = await this.client.accessSecretVersion({ name });

      if (!version.payload?.data) {
        throw new Error(`Secret ${secretName} has no data`);
      }

      const secretValue = version.payload.data.toString();

      // Cache the secret if caching is enabled
      if (useCache) {
        this.secretCache.set(secretName, secretValue);
      }

      return secretValue;
    } catch (error) {
      throw new Error(`Failed to retrieve secret ${secretName} from project ${this.secretsProjectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get OpenAI API key from Secret Manager
   * @returns Promise<string>
   */
  async getOpenAIApiKey(): Promise<string> {
    return this.getSecret('OPENAI_API_KEY');
  }

  /**
   * Get Clerk secret key from Secret Manager
   * @returns Promise<string>
   */
  async getClerkSecretKey(): Promise<string> {
    return this.getSecret('CLERK_SECRET_KEY');
  }

  /**
   * Get Clerk publishable key from Secret Manager
   * @returns Promise<string>
   */
  async getClerkPublishableKey(): Promise<string> {
    return this.getSecret('CLERK_PUBLISHABLE_KEY');
  }

  /**
   * Clear the secret cache
   */
  clearCache(): void {
    this.secretCache.clear();
  }

  /**
   * Initialize all commonly used secrets and cache them
   * This is useful for warming up the cache at function startup
   */
  async warmupCache(): Promise<void> {
    try {
      await Promise.all([
        this.getOpenAIApiKey(),
        this.getClerkSecretKey(),
        this.getClerkPublishableKey()
      ]);
    } catch (error) {
      console.warn('Failed to warm up secret cache:', error);
      // Don't throw here - let individual secret access handle errors
    }
  }
}
