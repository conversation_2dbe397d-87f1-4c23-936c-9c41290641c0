import { Database, Logger, sanitizeId, sanitizeEmail, DDJS_LABEL_PREFIX } from '@webapp/services';
import { OpenAIService } from './openai-service';
import {
  EmailAnalysisResult,
  EmailContent,
  ProcessEmailOptions,
  TokenCheckResult,
  EmailProcessingResult
} from './types';

export interface TokenService {
  getRemainingTokens(clerkUserId: string): Promise<number>;
  deductTokens(clerkUserId: string, amount: number): Promise<void>;
}

export interface GmailService {
  getEmailContent(messageId: string): Promise<EmailContent>;
  getMonitoredEmail(): string;
  modifyMessage(messageId: string, options: { addLabelIds?: string[], removeLabelIds?: string[] }): Promise<void>;
}

export class EmailAnalyzer {
  private db: Database;
  private openAIService: OpenAIService;
  private logger: Logger;

  constructor() {
    this.db = new Database();
    this.openAIService = new OpenAIService();
    this.logger = new Logger();
  }

  /**
   * Check if user has enough tokens for email analysis
   * @param clerkUserId - The Clerk user ID
   * @param emailIds - Array of email IDs to check
   * @param gmailService - Gmail service instance
   * @param tokenService - Token service instance
   * @returns Promise<TokenCheckResult>
   */
  async checkTokenRequirements(
    clerkUserId: string,
    emailIds: Array<{ id: string }>,
    gmailService: GmailService,
    tokenService: TokenService
  ): Promise<TokenCheckResult> {
    try {
      // Get the remaining tokens for the user
      const remainingTokens = await tokenService.getRemainingTokens(clerkUserId);
      let emailsNeedingTokens = 0;
      const totalEmails = emailIds.length;

      // Check which emails already have cached analysis
      for (const emailId of emailIds) {
        const docId = `${sanitizeId(clerkUserId)}_${sanitizeEmail(gmailService.getMonitoredEmail())}_${emailId.id}`;
        const analysisDoc = await this.db.collection('emailAnalysis')
          .doc(docId)
          .get();

        if (!analysisDoc.exists ||
          analysisDoc.data()?.['version'] !== this.openAIService.analysisVersion) {
          emailsNeedingTokens++;
        }
      }

      const hasEnoughTokens = remainingTokens >= emailsNeedingTokens;

      return {
        totalEmails,
        emailsNeedingTokens,
        hasEnoughTokens,
        remainingTokens
      };
    } catch (error) {
      this.logger.error('Failed to check token requirements', {
        clerkUserId,
        emailCount: emailIds.length,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Process a single email for analysis
   * @param options - Processing options
   * @param gmailService - Gmail service instance
   * @param tokenService - Token service instance
   * @param labelIds - Gmail label IDs mapping
   * @returns Promise<EmailAnalysisResult>
   */
  async processEmail(
    options: ProcessEmailOptions,
    gmailService: GmailService,
    tokenService: TokenService,
    labelIds: Record<string, string>
  ): Promise<EmailAnalysisResult> {
    const { clerkUserId, messageId, skipTokenCheck = false, forceReanalysis = false } = options;

    try {
      this.logger.info('Processing email for analysis', {
        clerkUserId,
        messageId,
        skipTokenCheck,
        forceReanalysis
      });

      // Check if analysis results already exist for this email
      const docId = `${sanitizeId(clerkUserId)}_${sanitizeEmail(gmailService.getMonitoredEmail())}_${messageId}`;
      const analysisDoc = await this.db.collection('emailAnalysis').doc(docId).get();

      if (!forceReanalysis && analysisDoc.exists && analysisDoc.data()?.['version'] === this.openAIService.analysisVersion) {
        this.logger.info(`Using cached analysis for email ${messageId}`);
        return analysisDoc.data() as EmailAnalysisResult;
      }

      // Check if user has sufficient tokens before processing
      if (!skipTokenCheck) {
        const remainingTokens = await tokenService.getRemainingTokens(clerkUserId);
        if (remainingTokens < 1) {
          throw new Error('Insufficient tokens for analysis');
        }
      }

      // Get email content
      const emailData = await gmailService.getEmailContent(messageId);
      const emailContent = `Subject: ${emailData.subject}\nFrom: ${emailData.from}\nTo: ${emailData.to}\nDate: ${emailData.date}\n\n${emailData.body}`;

      // Analyze email with OpenAI
      const analysis = await this.openAIService.analyzeEmail(emailContent);

      // Log the full analysis data for debugging
      this.logger.info(`Email analysis completed for ${messageId}`, {
        messageId,
        company: analysis.parsed?.company_name || 'unknown',
        role: analysis.parsed?.role_name || 'unknown',
        category: analysis.parsed?.email_type_category || 'unknown',
        isJobSearch: analysis.parsed?.is_related_to_job_search || false,
        nextSteps: analysis.parsed?.what_does_the_receiver_of_the_email_need_to_do_in_less_than_10_words || 'none'
      });

      // Add the appropriate label to the email based on the AI analysis
      if (analysis.parsed?.email_type_category) {
        const labelKey = DDJS_LABEL_PREFIX + analysis.parsed.email_type_category;
        if (labelIds[labelKey]) {
          await gmailService.modifyMessage(messageId, {
            addLabelIds: [labelIds[labelKey]]
          });
        }
      }

      // Deduct one token for the new analysis (if not skipping token check)
      if (!skipTokenCheck) {
        await tokenService.deductTokens(clerkUserId, 1);
      }

      // Save the analysis results to Firestore
      const analysisData: EmailAnalysisResult = {
        ...analysis,
        date: emailData.date,
        version: this.openAIService.analysisVersion,
        lastUpdated: new Date().toISOString()
      };

      await this.db.collection('emailAnalysis').doc(docId).set(analysisData);

      return analysisData;
    } catch (error) {
      this.logger.error('Failed to process email', {
        clerkUserId,
        messageId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * Process multiple emails for analysis
   * @param clerkUserId - The Clerk user ID
   * @param emailIds - Array of email IDs to process
   * @param gmailService - Gmail service instance
   * @param tokenService - Token service instance
   * @param labelIds - Gmail label IDs mapping
   * @returns Promise<EmailProcessingResult>
   */
  async processMultipleEmails(
    clerkUserId: string,
    emailIds: Array<{ id: string }>,
    gmailService: GmailService,
    tokenService: TokenService,
    labelIds: Record<string, string>
  ): Promise<EmailProcessingResult> {
    const results: EmailProcessingResult = {
      success: 0,
      failed: 0,
      cached: 0,
      processedEmails: [],
      errors: []
    };

    this.logger.info('Starting batch email processing', {
      clerkUserId,
      emailCount: emailIds.length
    });

    for (const emailId of emailIds) {
      try {
        const analysis = await this.processEmail(
          { clerkUserId, messageId: emailId.id },
          gmailService,
          tokenService,
          labelIds
        );

        results.success++;
        results.processedEmails.push({
          id: emailId.id,
          analysis: analysis
        });
      } catch (error) {
        results.failed++;
        results.errors.push({
          id: emailId.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });

        this.logger.error('Failed to process email in batch', {
          clerkUserId,
          emailId: emailId.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    this.logger.info('Batch email processing completed', {
      clerkUserId,
      totalEmails: emailIds.length,
      successful: results.success,
      failed: results.failed,
      cached: results.cached
    });

    return results;
  }

  /**
   * Get cached analysis for an email
   * @param clerkUserId - The Clerk user ID
   * @param messageId - The email message ID
   * @param monitoredEmail - The monitored email address
   * @returns Promise<EmailAnalysisResult | null>
   */
  async getCachedAnalysis(
    clerkUserId: string,
    messageId: string,
    monitoredEmail: string
  ): Promise<EmailAnalysisResult | null> {
    try {
      const docId = `${sanitizeId(clerkUserId)}_${sanitizeEmail(monitoredEmail)}_${messageId}`;
      const analysisDoc = await this.db.collection('emailAnalysis').doc(docId).get();

      if (analysisDoc.exists && analysisDoc.data()?.['version'] === this.openAIService.analysisVersion) {
        return analysisDoc.data() as EmailAnalysisResult;
      }

      return null;
    } catch (error) {
      this.logger.error('Failed to get cached analysis', {
        clerkUserId,
        messageId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return null;
    }
  }
}
