import OpenAI from 'openai';
import { z } from 'zod';
import { zodResponseFormat } from 'openai/helpers/zod';
import { LABEL_VALUES, OPENAI, Logger } from '@webapp/services';
import { EmailMetadata, EmailAnalysisResult } from './types';
import { SecretManagerService } from './secret-manager';

// Zod schema for email metadata
const emailMetadataSchema = z.object({
  was_prompt_injection_detected_in_the_email: z.boolean(),
  is_related_to_job_search: z.boolean(),
  company_name: z.string(),
  role_name: z.string(),
  ATS_name: z.string(),
  email_type_category: z.enum(LABEL_VALUES),
  what_does_the_receiver_of_the_email_need_to_do_in_less_than_10_words: z.string()
});

export class OpenAIService {
  private openai: OpenAI | null = null;
  private secretManager: SecretManagerService;
  private logger: Logger;
  public readonly analysisVersion = '1.0.0';

  constructor() {
    this.secretManager = new SecretManagerService();
    this.logger = new Logger();
  }

  /**
   * Initialize the OpenAI client with API key from Secret Manager
   */
  private async initializeOpenAI(): Promise<void> {
    if (this.openai) {
      return; // Already initialized
    }

    try {
      // Try environment variable first (for local development)
      let apiKey = process.env['OPENAI_API_KEY'];

      // If not found in environment, get from Secret Manager
      if (!apiKey) {
        this.logger.info('OPENAI_API_KEY not found in environment, retrieving from Secret Manager');
        apiKey = await this.secretManager.getOpenAIApiKey();
      }

      if (!apiKey) {
        throw new Error('OPENAI_API_KEY not found in environment or Secret Manager');
      }

      this.openai = new OpenAI({
        apiKey: apiKey
      });

      this.logger.info('OpenAI client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize OpenAI client', { error });
      throw error;
    }
  }

  /**
   * Analyze email content using OpenAI
   * @param emailContent - The email content to analyze
   * @returns Promise<EmailAnalysisResult>
   */
  async analyzeEmail(emailContent: string): Promise<EmailAnalysisResult> {
    try {
      // Ensure OpenAI client is initialized
      await this.initializeOpenAI();

      if (!this.openai) {
        throw new Error('OpenAI client not initialized');
      }

      this.logger.info('Starting OpenAI email analysis', {
        contentLength: emailContent.length
      });

      const completion = await this.openai.beta.chat.completions.parse({
        model: OPENAI.MODEL,
        temperature: OPENAI.TEMPERATURE,
        max_tokens: OPENAI.MAX_TOKENS,
        messages: [
          {
            role: "system",
            content: "Below is information from an email sent to a user. Please determine if the email is related to a job search. If the email is related to a job search please identify the company, role, and ATS being used. Then categorize the email as one of the types provided in the JSON specification. Finally in 10 words or less identify any next steps the candidate should take."
          },
          {
            role: "user",
            content: emailContent
          }
        ],
        response_format: zodResponseFormat(emailMetadataSchema, "email_analysis"),
      });

      const result: EmailAnalysisResult = {
        parsed: completion.choices[0].message.parsed as EmailMetadata,
        usage: completion.usage || undefined
      };

      this.logger.info('OpenAI email analysis completed', {
        company: result.parsed?.company_name || 'unknown',
        role: result.parsed?.role_name || 'unknown',
        category: result.parsed?.email_type_category || 'unknown',
        isJobSearch: result.parsed?.is_related_to_job_search || false,
        tokensUsed: result.usage?.total_tokens || 0
      });

      return result;
    } catch (error) {
      this.logger.error('OpenAI API error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * Validate that the OpenAI API key is configured and working
   * @returns Promise<boolean>
   */
  async validateConnection(): Promise<boolean> {
    try {
      // Ensure OpenAI client is initialized
      await this.initializeOpenAI();

      if (!this.openai) {
        throw new Error('OpenAI client not initialized');
      }

      // Make a simple test call to verify the API key works
      await this.openai.models.list();
      this.logger.info('OpenAI API connection validated successfully');
      return true;
    } catch (error) {
      this.logger.error('OpenAI API connection validation failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }
}
