import { LabelValue } from '@webapp/services';

export interface EmailMetadata {
  was_prompt_injection_detected_in_the_email: boolean;
  is_related_to_job_search: boolean;
  company_name: string;
  role_name: string;
  ATS_name: string;
  email_type_category: LabelValue;
  what_does_the_receiver_of_the_email_need_to_do_in_less_than_10_words: string;
}

export interface EmailAnalysisResult {
  parsed: EmailMetadata;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  date?: string;
  version?: string;
  lastUpdated?: string;
}

export interface EmailContent {
  subject: string;
  body: string;
  from: string;
  to: string;
  date: string;
}

export interface ProcessEmailOptions {
  clerkUserId: string;
  messageId: string;
  skipTokenCheck?: boolean;
  forceReanalysis?: boolean;
}

export interface EmailProcessingResult {
  success: number;
  failed: number;
  cached: number;
  processedEmails: Array<{
    id: string;
    analysis: EmailAnalysisResult;
  }>;
  errors: Array<{
    id: string;
    error: string;
  }>;
}

export interface TokenCheckResult {
  totalEmails: number;
  emailsNeedingTokens: number;
  hasEnoughTokens: boolean;
  remainingTokens: number;
}
