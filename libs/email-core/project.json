{"name": "email-core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/email-core/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/email-core", "main": "libs/email-core/src/index.ts", "tsConfig": "libs/email-core/tsconfig.lib.json", "assets": ["libs/email-core/*.md"]}}}}