export const SCOPES = [
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.labels',
  'https://www.googleapis.com/auth/gmail.modify'
] as const;

export const DDJS_LABEL_PREFIX = 'DDJS/';

export const LABEL_VALUES = [
  'not_job_search_related',
  'inbound_job_opportunity',
  'thanks_for_applying_or_application_received_confirmation',
  'rejection_role_paused_or_closed',
  'rejection_role_filled',
  'rejection_other',
  'next_steps_online_assement',
  'next_steps_interview_coordination',
  'next_steps_other',
  'offer',
  'other'
] as const;

export type LabelValue = typeof LABEL_VALUES[number];

export const OPENAI = {
  MODEL: 'gpt-4o-mini',
  TEMPERATURE: 0,
  MAX_TOKENS: 5000
} as const;

export const GMAIL_CONFIG = {
  pubsub: {
    // Determine project ID based on environment
    get projectId(): string {
      const isProduction = process.env['NODE_ENV'] === 'production' ||
                          process.env['USE_PRODUCTION_FIRESTORE'] === 'true';

      return process.env['GOOGLE_CLOUD_PROJECT'] ||
             (isProduction ? 'data-driven-job-search' : 'ddjs-dev-458016');
    },
    // Use a getter for topicName to ensure it uses the current projectId
    get topicName(): string {
      const projectId = this.projectId;
      return `projects/${projectId}/topics/gmail-notifications`;
    },
    subscriptionName: 'gmail-notifications-sub'
  }
} as const;
