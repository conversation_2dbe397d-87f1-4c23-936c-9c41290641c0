/**
 * Sanitize a Clerk user ID or other identifier for use in document IDs
 * @param id - The ID to sanitize
 * @returns The sanitized ID
 */
export function sanitizeId(id: string): string {
  return id.replace(/[^a-zA-Z0-9-]/g, '_');
}

/**
 * Sanitize an email address for use in document IDs
 * @param email - The email address to sanitize
 * @returns The sanitized email address
 */
export function sanitizeEmail(email: string): string {
  return email.replace(/[^a-zA-Z0-9@._-]/g, '_').toLowerCase();
}

/**
 * Validate a Clerk user ID format
 * @param id - The ID to validate
 * @returns Whether the ID is valid
 */
export function isClerkUserId(id: string): boolean {
  return /^[a-z0-9-]{36}$/.test(id);
}

/**
 * Validate a Clerk user ID and throw if invalid
 * @param id - The ID to validate
 * @throws Error if the ID is invalid
 */
export function validateClerkUserId(id: string): void {
  if (!isClerkUserId(id)) {
    throw new Error('Invalid Clerk user ID format');
  }
}
