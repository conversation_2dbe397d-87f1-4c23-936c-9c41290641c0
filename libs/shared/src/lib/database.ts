import * as admin from 'firebase-admin';

export type DatabaseType = 'emulator' | 'production' | 'development';

export class Database {
  public db!: admin.firestore.Firestore;
  public admin!: typeof admin;
  public databaseType!: DatabaseType;

  constructor() {
    this._initializeDatabase();
  }

  /**
   * Initialize the database connection
   * This is separated from the constructor to allow for async initialization
   */
  private _initializeDatabase(): void {
    // Check for explicit environment flag to use production
    const useProduction = process.env['USE_PRODUCTION_FIRESTORE'] === 'true';

    // Determine project ID based on environment
    const projectId = useProduction || process.env['NODE_ENV'] === 'production'
      ? 'data-driven-job-search'  // Production project
      : 'ddjs-dev-458016';        // Development project

    if (process.env['FIRESTORE_EMULATOR_HOST']) {
      // Option 1: Use Firestore emulator
      console.log(`Using Firestore emulator at ${process.env['FIRESTORE_EMULATOR_HOST']} with project ID: ${projectId}`);

      if (!admin.apps.length) {
        admin.initializeApp({
          projectId: projectId
        });

        const db = admin.firestore();
        db.settings({
          host: process.env['FIRESTORE_EMULATOR_HOST'],
          ssl: false
        });
      }

      this.db = admin.firestore();
      this.admin = admin;
      this.databaseType = 'emulator';

    } else if (useProduction || process.env['NODE_ENV'] === 'production') {
      // Option 2: Use production Firestore project
      console.log('Using production Firestore database with project ID: data-driven-job-search');

      if (!admin.apps.length) {
        admin.initializeApp({
          credential: admin.credential.applicationDefault(),
          projectId: 'data-driven-job-search'
        });
      }

      this.db = admin.firestore();
      this.admin = admin;
      this.databaseType = 'production';

    } else {
      // Option 3 (Default): Use development Firestore project
      console.log(`Using development Firestore database with project ID: ${projectId}`);

      if (!admin.apps.length) {
        admin.initializeApp({
          credential: admin.credential.applicationDefault(),
          projectId: projectId
        });
      }

      this.db = admin.firestore();
      this.admin = admin;
      this.databaseType = 'development';
    }
  }

  collection(name: string): admin.firestore.CollectionReference {
    return this.db.collection(name);
  }

  // Add other database methods as needed
  runTransaction(updateFunction: (transaction: admin.firestore.Transaction) => Promise<any>): Promise<any> {
    return this.db.runTransaction(updateFunction);
  }

  // Clear data method is no longer supported
  clearData(): boolean {
    console.warn('clearData() is no longer supported - use Firestore console to manage data');
    return false;
  }
}
