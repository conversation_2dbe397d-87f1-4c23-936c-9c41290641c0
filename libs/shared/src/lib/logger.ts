export interface LogDetails {
  [key: string]: any;
}

export interface ServiceContext {
  service: string;
  environment: string;
}

export interface LogEntry {
  severity: string;
  message: string;
  timestamp: string;
  serviceContext: ServiceContext;
  [key: string]: any;
}

export class Logger {
  private env: string;

  constructor() {
    this.env = process.env['NODE_ENV'] || 'development';
  }

  private formatLog(severity: string, message: string, details: LogDetails = {}): LogEntry {
    // Format according to GCP structured logging
    // https://cloud.google.com/logging/docs/structured-logging
    return {
      severity: severity.toUpperCase(),
      message,
      ...details,
      timestamp: new Date().toISOString(),
      // Add service context
      serviceContext: {
        service: process.env['REPO_NAME'] || 'data-driven-job-search',
        environment: this.env
      }
    };
  }

  info(message: string, details: LogDetails = {}): void {
    // Use console.log for INFO level as it writes to stdout
    console.log(JSON.stringify(this.formatLog('INFO', message, details)));
  }

  error(message: string, details: LogDetails = {}): void {
    // Use console.error for ERROR level as it writes to stderr
    console.error(JSON.stringify(this.formatLog('ERROR', message, details)));
  }

  warn(message: string, details: LogDetails = {}): void {
    // Use console.log for WARNING level (GCP standard)
    console.log(JSON.stringify(this.formatLog('WARNING', message, details)));
  }

  debug(message: string, details: LogDetails = {}): void {
    // In production, DEBUG severity might be filtered out by default
    if (this.env !== 'production' || process.env['ENABLE_DEBUG_LOGS'] === 'true') {
      console.log(JSON.stringify(this.formatLog('DEBUG', message, details)));
    }
  }

  metric(name: string, value: number, labels: LogDetails = {}): void {
    console.log(JSON.stringify(this.formatLog('INFO', `METRIC:${name}`, {
      metric: {
        name,
        value,
        labels
      }
    })));
  }
}

// Export a default instance
export const logger = new Logger();
