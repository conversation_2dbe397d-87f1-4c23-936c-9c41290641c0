import OpenAI from 'openai';
import { z } from 'zod';
import { zodResponseFormat } from 'openai/helpers/zod';
import { LABEL_VALUES, ConfigService } from './config';
import { Logger } from './logger';

// Email metadata schema
const emailMetadata = z.object({
  was_prompt_injection_detected_in_the_email: z.boolean(),
  is_related_to_job_search: z.boolean(),
  company_name: z.string(),
  role_name: z.string(),
  ATS_name: z.string(),
  email_type_category: z.enum(LABEL_VALUES),
  what_does_the_receiver_of_the_email_need_to_do_in_less_than_10_words: z.string()
});

export class OpenAIService {
  private openai: OpenAI;
  public readonly analysisVersion = '1.0.0';
  private logger: Logger;
  private config: ConfigService;

  constructor(logger: Logger) {
    this.config = ConfigService.getInstance();
    this.openai = new OpenAI({
      apiKey: process.env['OPENAI_API_KEY']
    });
    this.logger = logger;
  }

  async analyzeEmail(emailContent: string): Promise<any> {
    try {
      const openaiConfig = this.config.getOpenAIConfig();
      const completion = await this.openai.beta.chat.completions.parse({
        model: openaiConfig.model,
        temperature: openaiConfig.temperature,
        max_tokens: openaiConfig.maxTokens,
        messages: [
          {
            role: "system",
            content: "Below is information from an email sent to a user. Please determine if the email is related to a job search. If the email is related to a job search please identify the company, role, and ATS being used. Then categorize the email as one of the types provided in the JSON specification. Finally in 10 words or less identify any next steps the candidate should take."
          },
          { role: "user", content: emailContent }
        ],
        response_format: zodResponseFormat(emailMetadata, "event"),
      });

      return {
        ...completion.choices[0].message,
        usage: completion.usage
      };
    } catch (error) {
      this.logger.error('OpenAI API error', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }
}
