{"name": "services", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/services/src", "projectType": "library", "tags": [], "implicitDependencies": ["shared"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/services", "main": "libs/services/src/index.ts", "tsConfig": "libs/services/tsconfig.lib.json", "assets": ["libs/services/*.md"]}}}}