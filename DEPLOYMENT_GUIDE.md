# 🚀 GCP Deployment Guide for Data Driven Job Search

This guide walks you through deploying the entire application to Google Cloud Platform with a custom domain.

## 📋 Prerequisites

### 1. Install Required Tools

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Install Google Cloud CLI
# Visit: https://cloud.google.com/sdk/docs/install
# Or use package manager:
# macOS: brew install google-cloud-sdk
# Ubuntu: sudo apt-get install google-cloud-cli
```

### 2. Authentication

```bash
# Login to Firebase
firebase login

# Login to Google Cloud
gcloud auth login

# Set application default credentials
gcloud auth application-default login
```

### 3. Verify Project Access

```bash
# Check Firebase projects
firebase projects:list

# Check Google Cloud projects
gcloud projects list

# Verify you can access ddjs-dev-458016
gcloud config set project ddjs-dev-458016
```

## 🌐 Domain Setup (Namecheap)

### Step 1: Verify Domain Ownership

1. Go to [Google Search Console](https://search.google.com/search-console)
2. Click "Add Property"
3. Enter your domain: `datadrivenjobsearch.com`
4. Choose verification method (recommended: DNS record)
5. Add the TXT record to your Namecheap DNS settings:
   - Log in to Namecheap
   - Go to Domain List → Manage → Advanced DNS
   - Add TXT record with the provided value

### Step 2: Prepare DNS for Subdomain

In Namecheap Advanced DNS, prepare for the CNAME record:
- You'll add a CNAME record for `dev` subdomain after deployment
- The target will be provided by the deployment script

## 🚀 Deployment Process

### Step 1: Make Scripts Executable

```bash
chmod +x scripts/deploy-to-gcp.sh
chmod +x scripts/setup-domain.sh
```

### Step 2: Deploy to GCP

```bash
# Run the complete deployment
./scripts/deploy-to-gcp.sh
```

This script will:
- ✅ Build all projects (webapp, Cloud Functions)
- ✅ Enable required Google Cloud APIs
- ✅ Create Pub/Sub topics
- ✅ Deploy Firestore rules and indexes
- ✅ Deploy Cloud Functions
- ✅ Build webapp for production
- ✅ Deploy webapp to Cloud Run
- ✅ Provide next steps for domain setup

### Step 3: Set Up Custom Domain

```bash
# Configure the custom domain
./scripts/setup-domain.sh
```

This script will:
- ✅ Create domain mapping in Cloud Run
- ✅ Provide DNS configuration instructions
- ✅ Show required CNAME record details

### Step 4: Configure DNS in Namecheap

1. Log in to your Namecheap account
2. Go to Domain List → datadrivenjobsearch.com → Manage
3. Click on "Advanced DNS" tab
4. Add a new CNAME record:
   - **Type**: CNAME Record
   - **Host**: dev
   - **Value**: [provided by setup-domain.sh script]
   - **TTL**: Automatic (or 300)
5. Save the record

### Step 5: Wait for DNS Propagation

- DNS changes can take up to 48 hours to propagate
- Check propagation status: https://dnschecker.org/
- Test your domain: https://dev.datadrivenjobsearch.com

## 🔧 Configuration Updates

### Secret Management with Google Cloud Secret Manager

🔐 **SECURE APPROACH**: This deployment uses Google Cloud Secret Manager for centralized secret management.

**Cross-Project Secret Management:**
- **Secrets stored in**: `data-driven-job-search` (production project)
- **Deployed to**: `ddjs-dev-458016` (development project)
- **Automatic retrieval**: Secrets are fetched from production project during deployment

**No manual environment files needed!** The deployment automatically retrieves secrets from the production project.

```bash
# First, set up your secrets in the production project
./scripts/setup-secrets.sh

# Then deploy to dev - secrets are retrieved automatically from production
./scripts/deploy-to-gcp.sh
```

Key differences from local:
```bash
# Local Development
BASE_URL=http://localhost:3000
NODE_ENV=development

# Production
BASE_URL=https://dev.datadrivenjobsearch.com
NODE_ENV=production
```

### Clerk Authentication

For production, you should create separate Clerk application:

1. Go to [Clerk Dashboard](https://dashboard.clerk.com/)
2. Create a new application for production
3. Update `.env.production` with new keys:
   ```bash
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
   CLERK_SECRET_KEY=sk_live_...
   ```

### Gmail API Setup

**Important**: Gmail OAuth is handled entirely by Clerk. No OAuth 2.0 client credentials are needed in our application.

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select project: `ddjs-dev-458016`
3. Enable Gmail API
4. **Authentication Flow**:
   - Users authenticate with Google through Clerk's OAuth flow
   - Clerk manages OAuth tokens and refresh tokens
   - Our application retrieves tokens from Clerk using `getUserOauthAccessToken()`
   - No direct OAuth client credentials required

**Note**: The application uses Clerk's managed OAuth flow, eliminating the need for `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` environment variables.

## 📊 Monitoring and Logs

### Cloud Run Logs

```bash
# View webapp logs
gcloud logging read 'resource.type=cloud_run_revision AND resource.labels.service_name=webapp' --project=ddjs-dev-458016

# Follow logs in real-time
gcloud logging tail 'resource.type=cloud_run_revision AND resource.labels.service_name=webapp' --project=ddjs-dev-458016
```

### Cloud Functions Logs

```bash
# View function logs
gcloud logging read 'resource.type=cloud_function' --project=ddjs-dev-458016

# Specific function logs
gcloud logging read 'resource.type=cloud_function AND resource.labels.function_name=analyzeEmail' --project=ddjs-dev-458016
```

### Firestore Data

```bash
# Access Firestore console
open "https://console.firebase.google.com/project/ddjs-dev-458016/firestore"
```

## 🧪 Testing the Deployment

### 1. Basic Connectivity

```bash
# Test the domain
curl -I https://dev.datadrivenjobsearch.com

# Check SSL certificate
curl -vI https://dev.datadrivenjobsearch.com 2>&1 | grep -i ssl
```

### 2. Application Testing

1. Visit https://dev.datadrivenjobsearch.com
2. Sign in with Clerk
3. Test email analysis functionality
4. Check real-time progress updates
5. Verify Cloud Functions execution

### 3. API Endpoints

```bash
# Test progress webhook
curl -X POST https://dev.datadrivenjobsearch.com/api/emails/progress-webhook \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Test metrics API
curl https://dev.datadrivenjobsearch.com/api/metrics
```

## 🔄 Updates and Redeployment

### Quick Function Update

```bash
# Build and deploy only functions
npx nx run-many --target=build --projects=email-analysis-function,notification-handler-function
firebase deploy --only functions --project ddjs-dev-458016
```

### Full Redeployment

```bash
# Run the full deployment script again
./scripts/deploy-to-gcp.sh
```

### Webapp Only Update

```bash
# Build and deploy only the webapp
cd apps/webapp
npm run build
gcloud builds submit . --tag gcr.io/ddjs-dev-458016/webapp
gcloud run deploy webapp --image gcr.io/ddjs-dev-458016/webapp --region us-central1 --project ddjs-dev-458016
```

## 🚨 Troubleshooting

### Common Issues

1. **Domain not accessible**
   - Check DNS propagation: https://dnschecker.org/
   - Verify CNAME record is correct
   - Wait up to 48 hours for full propagation

2. **SSL Certificate issues**
   - Google automatically provisions SSL certificates
   - Can take up to 24 hours to be active
   - Check certificate status in Cloud Run console

3. **Cloud Functions not triggering**
   - Check Pub/Sub topics exist
   - Verify function deployment logs
   - Test functions manually in console

4. **Authentication issues**
   - Verify Clerk keys are correct for production
   - Check authorized domains in Clerk dashboard
   - Ensure OAuth redirect URLs are updated

### Getting Help

- **Cloud Run Console**: https://console.cloud.google.com/run?project=ddjs-dev-458016
- **Firebase Console**: https://console.firebase.google.com/project/ddjs-dev-458016
- **Cloud Functions Console**: https://console.cloud.google.com/functions/list?project=ddjs-dev-458016

## 📈 Performance Optimization

### Cloud Run Configuration

The deployment uses these optimized settings:
- **CPU**: 1 vCPU
- **Memory**: 512 MiB
- **Concurrency**: 80 requests per instance
- **Min instances**: 0 (scales to zero)
- **Max instances**: 100

### Monitoring

Set up monitoring:
```bash
# Enable monitoring API
gcloud services enable monitoring.googleapis.com

# Create uptime check
gcloud alpha monitoring uptime create https://dev.datadrivenjobsearch.com
```

## 🎉 Success!

Once deployed successfully, you'll have:

- ✅ **Webapp**: https://dev.datadrivenjobsearch.com
- ✅ **Cloud Functions**: Automatically triggered by Pub/Sub
- ✅ **Firestore**: Production database
- ✅ **Real-time updates**: WebSocket connections
- ✅ **SSL Certificate**: Automatically managed
- ✅ **Monitoring**: Cloud Run and Functions logs

Your development environment is now live and ready for testing!
