{"functions": [{"source": "dist/apps/email-analysis-function", "codebase": "email-analysis-function", "runtime": "nodejs20"}, {"source": "dist/apps/notification-handler-function", "codebase": "notification-handler-function", "runtime": "nodejs20"}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "pubsub": {"port": 9085}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}